import React, { useState, useRef, useEffect } from 'react';
import './LoginPage.css';
import BackendService from '../../services/BackendService';
import LicenseStorage from '../../services/LicenseStorage';

const LoginPage = ({ onAuthenticate, authError }) => {
  const [licenseKey, setLicenseKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isShaking, setIsShaking] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    console.log('🔐 LoginPage: Setting up license validation handler');

    // Register handler for license validation responses
    const handleLicenseValidationResult = (message) => {
      console.log('🔐 LoginPage: Received license validation result:', message);
      if (message.type === 'license_validation_result') {
        handleLicenseResponse(message);
      }
    };

    BackendService.registerHandler('license_validation_result', handleLicenseValidationResult);

    // Ensure BackendService is connected
    const ensureConnection = async () => {
      try {
        const connectionState = BackendService.getConnectionState();
        const metadata = BackendService.getConnectionMetadata();
        console.log('🔐 LoginPage: BackendService connection state:', connectionState, metadata);

        if (connectionState !== 'connected') {
          console.log('🔐 LoginPage: Connecting to BackendService...');
          if (typeof BackendService.connectGracefully === 'function') {
            await BackendService.connectGracefully();
          } else {
            await BackendService.connect();
          }
          console.log('🔐 LoginPage: BackendService connected successfully');
        }
      } catch (error) {
        console.error('🔐 LoginPage: Failed to connect to BackendService:', error);

        // Check if we're in startup grace period - don't show error if so
        const metadata = BackendService.getConnectionMetadata();
        if (!metadata.isInGracePeriod) {
          setError('Connection error. Please try again.');
        } else {
          console.log('🔐 LoginPage: Suppressing connection error during startup grace period');
        }
      }
    };

    ensureConnection();

    return () => {
      console.log('🔐 LoginPage: Cleaning up license validation handler');
      BackendService.unregisterHandler('license_validation_result');
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle auto-login events
  useEffect(() => {
    console.log('🔐 LoginPage: Setting up auto-login event listeners');

    const handleAutoLoginSuccess = (event) => {
      console.log('🔐 LoginPage: Auto-login success event received:', event.detail);

      const { data } = event.detail;
      if (data) {
        // Create license info from auto-login data
        const licenseInfo = {
          licenseKey: data.license_key,
          manageUrl: data.manage_url,
          email: data.email,
          status: data.status,
          renewalPeriodEnd: data.renewal_period_end
        };

        console.log('🔐 LoginPage: Auto-login successful, triggering authentication');
        onAuthenticate(true, licenseInfo);
      }
    };

    const handlePongLicenseValid = (event) => {
      console.log('🔐 LoginPage: Pong license valid event received:', event.detail);

      const { data } = event.detail;
      if (data) {
        // Create license info from pong data
        const licenseInfo = {
          licenseKey: data.license_key,
          manageUrl: data.manage_url,
          email: data.email,
          status: data.status,
          renewalPeriodEnd: data.renewal_period_end
        };

        console.log('🔐 LoginPage: Valid license detected in pong, triggering authentication');
        onAuthenticate(true, licenseInfo);
      }
    };

    // Add event listeners
    window.addEventListener('autoLoginSuccess', handleAutoLoginSuccess);
    window.addEventListener('pongLicenseValid', handlePongLicenseValid);

    // Cleanup
    return () => {
      console.log('🔐 LoginPage: Cleaning up auto-login event listeners');
      window.removeEventListener('autoLoginSuccess', handleAutoLoginSuccess);
      window.removeEventListener('pongLicenseValid', handlePongLicenseValid);
    };
  }, [onAuthenticate]);

  // Handle auth error from parent component
  useEffect(() => {
    if (authError) {
      setError(authError);
      shakeInput();
    }
  }, [authError]);

  const handleLicenseResponse = (response) => {
    console.log('Handling license response:', response);
    setIsLoading(false);

    // Check for error in the nested structure
    if (response.data && response.data.error) {
      setError(response.data.error.message || 'Invalid license key. Please try again.');
      shakeInput();
      return;
    }
    /*
    // Check if license is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (response.data.renewal_period_end < currentTime) {
      setError('License has expired. Please renew your subscription.');
      shakeInput();
      return;
    }
      */

    // Store license information for later use
    const licenseInfo = {
      licenseKey: licenseKey,  // Include the license key
      manageUrl: response.data.manage_url,
      email: response.data.email,
      status: response.data.status,
      renewalPeriodEnd: response.data.renewal_period_end
    };

    // Store license key securely for persistence
    const storageSuccess = LicenseStorage.storeLicense(licenseKey, licenseInfo);
    if (storageSuccess) {
      console.log('🔐 LoginPage: License key stored successfully for future sessions');
    } else {
      console.warn('🔐 LoginPage: Failed to store license key - user will need to re-enter on next session');
    }

    // Pass the license info to the parent component
    onAuthenticate(true, licenseInfo);
  };

  const handleLicenseChange = (e) => {
    console.log('🔐 LoginPage: Handling license change:', e.target.value);
    setLicenseKey(e.target.value);
    setError('');
  };

  const shakeInput = () => {
    setIsShaking(true);
    if (inputRef.current) {
      inputRef.current.classList.add('shake');
      setTimeout(() => {
        inputRef.current.classList.remove('shake');
        setIsShaking(false);
      }, 500);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!licenseKey.trim()) {
      setError('Please enter a license key');
      shakeInput();
      return;
    }

    console.log('🔐 LoginPage: Starting license validation for key:', `${licenseKey.substring(0, 8)}...`);
    setIsLoading(true);
    setError('');

    try {
      // Ensure BackendService is connected
      const connectionState = BackendService.getConnectionState();
      console.log('🔐 LoginPage: BackendService connection state before validation:', connectionState);

      if (connectionState !== 'connected') {
        console.log('🔐 LoginPage: Connecting to BackendService before validation...');
        if (typeof BackendService.connectGracefully === 'function') {
          await BackendService.connectGracefully();
        } else {
          await BackendService.connect();
        }
      }

      // Send validation request using BackendService
      console.log('🔐 LoginPage: Sending license validation request...');
      const response = await BackendService.validateLicense(licenseKey);

      console.log('🔐 LoginPage: Received validation response:', response);
      handleLicenseResponse(response);

    } catch (error) {
      console.error('🔐 LoginPage: License validation failed:', error);

      // Check if we're in startup grace period - don't show error if so
      const metadata = BackendService.getConnectionMetadata();
      if (!metadata.isInGracePeriod) {
        setError('Connection error. Please try again.');
      } else {
        console.log('🔐 LoginPage: Suppressing validation error during startup grace period');
        // During grace period, just keep loading state and let the connection establish
        return;
      }
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div id="login-input-card">
        <input
          ref={inputRef}
          type="text"
          placeholder="K-XXXXXX-XXXXXXXX-XXXXXXX"
          value={licenseKey}
          onChange={(e) => setLicenseKey(e.target.value)}
          disabled={isLoading}
          autoComplete="off"
          spellCheck="false"
          className={isShaking ? 'shake' : ''}
        />

        {error && <div className="error-message">{error}</div>}

        <button
          type="submit"
          className="start-button"
          onClick={handleSubmit}
        >
          {isLoading ? (
            <div className="loading-container">
              <div className="loading-glow" />
              <span className="loading-text">Validating...</span>
            </div>
          ) : (
            'Validate License'
          )}
        </button>
      </div>
    </div>
  );
};

export default LoginPage; 