.card-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 16px;
  border: 1px solid rgba(124, 77, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 1200px;
  background: rgba(11, 10, 18, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.card-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(124, 77, 255, 0.05) 0%,
    rgba(124, 77, 255, 0.02) 100%
  );
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-section:hover {
  border-color: rgba(124, 77, 255, 0.25);
  box-shadow: 0 8px 32px rgba(124, 77, 255, 0.1);
  transform: translateY(-2px);
}

.card-section:hover::before {
  opacity: 1;
}

.card-section:not(:last-child) {
  border-bottom: 1px solid rgba(124, 77, 255, 0.15);
  padding-bottom: 1.5rem;
  margin-bottom: 1rem;
} 