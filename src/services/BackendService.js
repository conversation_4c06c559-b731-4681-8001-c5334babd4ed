/**
 * BackendService.js
 * Handles WebSocket communication with the Python backend with robust retry logic
 */
class BackendService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.messageHandlers = new Map();
    this.pendingRequests = new Map();
    this.connectionStateCallbacks = new Set();
    this.messageQueue = [];
    this.maxQueueSize = 100;

    // Enhanced retry configuration
    this.retryConfig = {
      maxAttempts: Infinity, // Continuously retry
      baseDelay: 1000,      // Start with 1 second
      maxDelay: 30000,      // Maximum 30 seconds
      backoffFactor: 2,     // Exponential backoff
      jitterFactor: 0.1     // Add randomness to prevent thundering herd
    };

    // Adaptive timeout configuration for handling backend load
    this.adaptiveTimeouts = {
      baseTimeout: 15000,           // Base timeout for normal operations
      maxTimeout: 60000,            // Maximum timeout during heavy load
      pingTimeout: 15000,           // Current ping timeout (adaptive)
      queueTasksActive: false,      // Track if QueueIT tasks are running
      consecutiveTimeouts: 0,       // Track consecutive timeout failures
      lastSuccessfulPing: Date.now(), // Track last successful ping
      adaptiveMultiplier: 1.0       // Current timeout multiplier
    };

    // Error handling and user feedback
    this.errorHandling = {
      suppressTimeoutErrors: false,  // Whether to suppress timeout error logs
      lastTimeoutWarning: 0,         // Last time we showed a timeout warning
      timeoutWarningInterval: 30000, // Minimum interval between timeout warnings (30s)
      errorCallbacks: new Set()      // Callbacks for error notifications
    };

    // Setup global error handling for uncaught ping timeouts
    this._setupGlobalErrorHandling();

    // Retry state
    this.retryAttempts = 0;
    this.retryTimeout = null;
    this.lastConnectionAttempt = null;
    this.connectionStartTime = null;

    // Health monitoring
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;
    this.missedHeartbeats = 0;
    this.maxMissedHeartbeats = 3;
    this.heartbeatIntervalMs = 1000; // 1 second heartbeat

    // Connection state constants
    this.CONNECTION_STATES = {
      DISCONNECTED: 'disconnected',
      CONNECTING: 'connecting',
      CONNECTED: 'connected',
      RECONNECTING: 'reconnecting',
      ERROR: 'error',
      RETRYING: 'retrying'
    };

    this.currentState = this.CONNECTION_STATES.DISCONNECTED;
    this.connectionMetadata = {
      lastError: null,
      retryAttempt: 0,
      nextRetryIn: null,
      connectionDuration: null,
      serverStatus: null
    };

    // Auto-start connection attempts (disabled by default to prevent issues during initialization)
    this.autoConnect = false;
    this.shouldReconnect = true;

    // Graceful startup configuration to prevent annoying error messages during app initialization
    this.startupGracePeriod = 10000; // 10 seconds grace period
    this.startupTime = Date.now();
    this.isInStartupGracePeriod = true;
    this.startupGraceTimeout = null;

    // Initial connection retry configuration (separate from main retry system)
    this.initialConnectionFailures = 0;
    this.maxInitialConnectionFailures = 3;
    this.initialRetryDelay = 2000; // 2 seconds between initial connection retries
    this.initialRetryTimeout = null;

    // Initialize startup grace period timeout
    this._initializeStartupGracePeriod();

    // Note: Auto-initialization is disabled to prevent race conditions
    // Call initialize() manually or use connectToBackend() from AppContext
  }

  /**
   * Initialize startup grace period to prevent error messages during app startup
   */
  _initializeStartupGracePeriod() {
    this.startupGraceTimeout = setTimeout(() => {
      this.isInStartupGracePeriod = false;
      this._resetInitialConnectionFailures();
      console.log('Startup grace period ended - connection errors will now be shown to user');
    }, this.startupGracePeriod);
  }

  /**
   * Check if we're still in the startup grace period
   */
  isInGracePeriod() {
    return this.isInStartupGracePeriod && (Date.now() - this.startupTime) < this.startupGracePeriod;
  }

  /**
   * End the startup grace period early (e.g., when connection succeeds)
   */
  _endStartupGracePeriod() {
    if (this.startupGraceTimeout) {
      clearTimeout(this.startupGraceTimeout);
      this.startupGraceTimeout = null;
    }
    this.isInStartupGracePeriod = false;

    // Reset initial connection failures when grace period ends
    this._resetInitialConnectionFailures();
  }

  /**
   * Reset initial connection failure counter
   */
  _resetInitialConnectionFailures() {
    this.initialConnectionFailures = 0;
    if (this.initialRetryTimeout) {
      clearTimeout(this.initialRetryTimeout);
      this.initialRetryTimeout = null;
    }
  }

  /**
   * Handle initial connection failure with automatic retry
   */
  _handleInitialConnectionFailure(error, rejectConnection) {
    this.initialConnectionFailures++;
    console.log(`Initial connection failure ${this.initialConnectionFailures}/${this.maxInitialConnectionFailures}:`, error.message);

    if (this.initialConnectionFailures >= this.maxInitialConnectionFailures) {
      // Maximum failures reached, show error to user
      console.error('Maximum initial connection failures reached, showing error to user');
      this._updateConnectionState(this.CONNECTION_STATES.ERROR, {
        code: error.code,
        reason: error.reason || 'Connection failed after multiple attempts',
        initialFailures: this.initialConnectionFailures
      });
      rejectConnection(error);
      return;
    }

    // Schedule retry attempt
    console.log(`Scheduling initial connection retry in ${this.initialRetryDelay}ms (attempt ${this.initialConnectionFailures + 1}/${this.maxInitialConnectionFailures})`);

    this.initialRetryTimeout = setTimeout(async () => {
      this.initialRetryTimeout = null;

      if (this.shouldReconnect && !this.isConnected && !this.isConnecting) {
        console.log(`Executing initial connection retry attempt ${this.initialConnectionFailures + 1}`);
        try {
          // Reset connecting state and try again
          this.isConnecting = false;
          await this.connect();
        } catch (retryError) {
          console.error('Initial connection retry failed:', retryError);
          // The retry will be handled by the onclose handler again
        }
      }
    }, this.initialRetryDelay);
  }

  /**
   * Initialize the backend service (can be called manually if auto-connect is disabled)
   */
  async initialize() {
    if (this.isConnected || this.isConnecting) {
      console.log('Backend service already initialized');
      return;
    }

    this.shouldReconnect = true;

    try {
      await this.connect();
    } catch (error) {
      console.log('Initial connection failed, starting retry process:', error.message);
      this._startRetryProcess();
    }
  }

  /**
   * Graceful connection attempt that respects startup grace period
   * This method is designed to be called multiple times safely during app startup
   */
  async connectGracefully() {
    // If already connected, return immediately
    if (this.isConnected) {
      console.log('Backend service already connected');
      return Promise.resolve();
    }

    // If already connecting, wait for that attempt to complete
    if (this.isConnecting) {
      console.log('Backend service connection already in progress, waiting...');
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.isConnected) {
            resolve();
          } else if (!this.isConnecting) {
            // Connection attempt finished but failed
            if (this.isInGracePeriod()) {
              // During grace period, start retry process silently
              this._startRetryProcess();
              resolve(); // Don't reject during grace period
            } else {
              reject(new Error('Connection failed'));
            }
          } else {
            // Still connecting, check again
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    // Start new connection attempt
    this.shouldReconnect = true;

    try {
      await this.connect();
    } catch (error) {
      if (this.isInGracePeriod()) {
        console.log('Connection failed during grace period, starting retry process silently');
        this._startRetryProcess();
        // Don't throw error during grace period
        return;
      } else {
        throw error;
      }
    }
  }

  /**
   * Add a callback for connection state changes
   */
  addConnectionStateCallback(callback) {
    this.connectionStateCallbacks.add(callback);
  }

  /**
   * Register a callback for connection state changes (alias for addConnectionStateCallback)
   */
  registerConnectionStateCallback(callback) {
    this.connectionStateCallbacks.add(callback);
  }

  /**
   * Remove a connection state callback
   */
  removeConnectionStateCallback(callback) {
    this.connectionStateCallbacks.delete(callback);
  }

  /**
   * Update connection state and notify callbacks
   */
  _updateConnectionState(newState, metadata = {}) {
    const previousState = this.currentState;
    this.currentState = newState;

    // Update metadata
    this.connectionMetadata = {
      ...this.connectionMetadata,
      ...metadata,
      timestamp: Date.now(),
      previousState,
      retryAttempt: this.retryAttempts,
      isInGracePeriod: this.isInGracePeriod()
    };

    console.log(`Connection state changed: ${previousState} → ${newState}`, metadata);

    // End grace period on successful connection
    if (newState === this.CONNECTION_STATES.CONNECTED) {
      this._endStartupGracePeriod();
    }

    // During grace period, suppress error states to prevent annoying startup messages
    const shouldSuppressErrorState = this.isInGracePeriod() &&
      (newState === this.CONNECTION_STATES.ERROR || newState === this.CONNECTION_STATES.DISCONNECTED) &&
      previousState !== this.CONNECTION_STATES.CONNECTED; // Don't suppress if we were previously connected

    if (shouldSuppressErrorState) {
      console.log(`Suppressing error state during grace period: ${newState}`);
      // Still update internal state but don't notify callbacks with error state
      // Instead, use a neutral "connecting" state for UI
      const gracefulState = this.CONNECTION_STATES.CONNECTING;
      this.connectionStateCallbacks.forEach(callback => {
        try {
          callback(gracefulState, { ...this.getConnectionMetadata(), isInGracePeriod: true });
        } catch (error) {
          console.error('Error in connection state callback:', error);
        }
      });
      return;
    }

    // Notify all callbacks with actual state
    this.connectionStateCallbacks.forEach(callback => {
      try {
        callback(newState, this.getConnectionMetadata());
      } catch (error) {
        console.error('Error in connection state callback:', error);
      }
    });
  }

  /**
   * Get current connection state
   */
  getConnectionState() {
    return this.currentState;
  }

  /**
   * Get connection metadata including retry information
   */
  getConnectionMetadata() {
    return {
      ...this.connectionMetadata,
      retryAttempt: this.retryAttempts,
      isRetrying: this.retryTimeout !== null,
      connectionDuration: this.isConnected && this.connectionStartTime
        ? Date.now() - this.connectionStartTime
        : null,
      missedHeartbeats: this.missedHeartbeats,
      autoConnect: this.autoConnect,
      shouldReconnect: this.shouldReconnect,
      nextRetryDelay: this.retryTimeout ? this._calculateRetryDelay() : null,
      isInGracePeriod: this.isInGracePeriod(),
      gracePeriodRemaining: this.isInGracePeriod()
        ? Math.max(0, this.startupGracePeriod - (Date.now() - this.startupTime))
        : 0,
      initialConnectionFailures: this.initialConnectionFailures,
      maxInitialConnectionFailures: this.maxInitialConnectionFailures,
      isRetryingInitialConnection: this.initialRetryTimeout !== null
    };
  }

  /**
   * Calculate next retry delay with exponential backoff and jitter
   */
  _calculateRetryDelay() {
    const { baseDelay, maxDelay, backoffFactor, jitterFactor } = this.retryConfig;

    // Exponential backoff: baseDelay * (backoffFactor ^ retryAttempts)
    let delay = Math.min(baseDelay * Math.pow(backoffFactor, this.retryAttempts), maxDelay);

    // Add jitter to prevent thundering herd problem
    const jitter = delay * jitterFactor * (Math.random() - 0.5);
    delay = Math.max(delay + jitter, baseDelay);

    return Math.floor(delay);
  }

  /**
   * Clear any existing retry timeout
   */
  _clearRetryTimeout() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
  }

  /**
   * Enable or disable automatic reconnection
   */
  setAutoReconnect(enabled) {
    this.shouldReconnect = enabled;
    if (!enabled) {
      this._clearRetryTimeout();
    }
    console.log(`Auto-reconnect ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Reset retry attempts counter
   */
  resetRetryAttempts() {
    this.retryAttempts = 0;
    this.connectionMetadata.retryAttempt = 0;
  }

  /**
   * Connect to the Python backend WebSocket server
   */
  connect() {
    return new Promise((resolve, reject) => {
      // Prevent multiple simultaneous connection attempts
      if (this.isConnecting || this.isConnected) {
        if (this.isConnected) {
          resolve();
          return;
        }
        reject(new Error('Connection attempt already in progress'));
        return;
      }

      // Clear any existing reconnect timer
      if (this.reconnectInterval) {
        clearTimeout(this.reconnectInterval);
        this.reconnectInterval = null;
      }

      let connectionTimeout;
      let connectionResolved = false;

      const cleanup = () => {
        if (connectionTimeout) {
          clearTimeout(connectionTimeout);
          connectionTimeout = null;
        }
      };

      const resolveConnection = () => {
        if (!connectionResolved) {
          connectionResolved = true;
          cleanup();
          resolve();
        }
      };

      const rejectConnection = (error) => {
        if (!connectionResolved) {
          connectionResolved = true;
          cleanup();
          this.isConnecting = false;
          reject(error);
        }
      };

      try {
        this.isConnecting = true;
        this._updateConnectionState(this.CONNECTION_STATES.CONNECTING);

        // Close existing connection if any
        if (this.socket) {
          this.socket.close();
          this.socket = null;
        }

        // Create new WebSocket connection with timeout
        // Reduced timeout to be more responsive with 2-second ping intervals
        connectionTimeout = setTimeout(() => {
          if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
            console.warn('Connection timeout, closing socket');
            this.socket.close();
            this._updateConnectionState(this.CONNECTION_STATES.ERROR, { error: 'Connection timeout' });
            rejectConnection(new Error('Connection timeout'));
          }
        }, 8000); // 8 second timeout (more responsive with faster pings)

        this.socket = new WebSocket('ws://localhost:8765');

        // Set up event handlers
        this.socket.onopen = () => {
          console.log('Connected to Python backend');
          this.isConnected = true;
          this.isConnecting = false;
          this.connectionStartTime = Date.now();

          // Reset retry state on successful connection
          this.resetRetryAttempts();
          this._clearRetryTimeout();
          this._resetInitialConnectionFailures();
          this.missedHeartbeats = 0;

          this._updateConnectionState(this.CONNECTION_STATES.CONNECTED, {
            connectionTime: this.connectionStartTime,
            serverUrl: 'ws://localhost:8765'
          });

          // Process any queued messages
          this._processMessageQueue();

          // Start heartbeat with 1-second interval
          this.startHeartbeat();

          resolveConnection();
        };

        this.socket.onclose = (event) => {
          console.log(`WebSocket closed: ${event.code} ${event.reason}`);

          const wasConnected = this.isConnected;
          this.isConnected = false;

          // Only reset isConnecting if we were actually connected before
          // This prevents race conditions during initial connection
          if (wasConnected) {
            this.isConnecting = false;
          }

          const wasCleanClose = event.code === 1000;

          // Handle different close scenarios
          if (!connectionResolved) {
            // Connection failed during initial attempt - use automatic retry mechanism
            const connectionError = new Error(`Connection failed: ${event.code} ${event.reason || 'Unknown reason'}`);
            connectionError.code = event.code;
            connectionError.reason = event.reason;

            this._handleInitialConnectionFailure(connectionError, rejectConnection);
          } else if (wasConnected && !wasCleanClose && this.shouldReconnect) {
            // Unexpected disconnection after successful connection
            this.connectionMetadata.lastError = `Connection lost: ${event.code} ${event.reason}`;
            this._updateConnectionState(this.CONNECTION_STATES.RECONNECTING, {
              code: event.code,
              reason: event.reason,
              lastError: this.connectionMetadata.lastError
            });
            this._startRetryProcess();
          } else {
            // Clean disconnection or already handled
            this._updateConnectionState(this.CONNECTION_STATES.DISCONNECTED, {
              code: event.code,
              reason: event.reason
            });
          }
        };

        this.socket.onerror = (error) => {
          console.error('WebSocket error:', error);

          const errorMessage = error.message || 'WebSocket connection error';
          this.connectionMetadata.lastError = errorMessage;

            // Error on established connection
            this.isConnected = false;
            this._updateConnectionState(this.CONNECTION_STATES.RECONNECTING, {
              error: errorMessage,
              lastError: errorMessage
            });
            this._startRetryProcess();
        };

        this.socket.onmessage = (event) => {
          this._handleMessage(event.data);
        };

      } catch (error) {
        console.error('Error creating WebSocket connection:', error);
        this._updateConnectionState(this.CONNECTION_STATES.ERROR, { error });
        rejectConnection(error);
      }
    });
  }

  /**
   * Start the retry process with exponential backoff
   */
  _startRetryProcess() {
    if (!this.shouldReconnect || this.isConnected || this.isConnecting) {
      console.log('Retry process skipped: disabled, already connected, or connecting');
      return;
    }

    this._clearRetryTimeout();

    const delay = this._calculateRetryDelay();
    this.retryAttempts++;

    console.log(`Scheduling retry attempt ${this.retryAttempts} in ${delay}ms`);

    this._updateConnectionState(this.CONNECTION_STATES.RETRYING, {
      retryAttempt: this.retryAttempts,
      nextRetryIn: delay,
      lastError: this.connectionMetadata.lastError
    });

    this.retryTimeout = setTimeout(async () => {
      this.retryTimeout = null;

      if (this.shouldReconnect && !this.isConnected && !this.isConnecting) {
        console.log(`Executing retry attempt ${this.retryAttempts}`);
        try {
          await this.connect();
          console.log(`Retry attempt ${this.retryAttempts} successful`);
        } catch (error) {
          console.error(`Retry attempt ${this.retryAttempts} failed:`, error);
          this.connectionMetadata.lastError = error.message;
          // Schedule next retry
          this._startRetryProcess();
        }
      }
    }, delay);
  }

  /**
   * Start automatic reconnection attempts (legacy method for compatibility)
   */
  _startAutoReconnect() {
    this._startRetryProcess();
  }

  /**
   * Process queued messages after reconnection
   */
  _processMessageQueue() {
    if (this.messageQueue.length > 0) {
      console.log(`Processing ${this.messageQueue.length} queued messages`);

      const queue = [...this.messageQueue];
      this.messageQueue = [];

      queue.forEach(({ message, resolve, reject }) => {
        this._sendMessage(message)
          .then(resolve)
          .catch(reject);
      });
    }
  }

  /**
   * Add message to queue when disconnected
   */
  _queueMessage(message) {
    return new Promise((resolve, reject) => {
      if (this.messageQueue.length >= this.maxQueueSize) {
        // Remove oldest message to make room
        const removed = this.messageQueue.shift();
        if (removed) {
          removed.reject(new Error('Message dropped due to queue overflow'));
        }
      }

      this.messageQueue.push({ message, resolve, reject });
      console.log(`Message queued (${this.messageQueue.length}/${this.maxQueueSize})`);
    });
  }

  /**
   * Disconnect from the Python backend
   */
  disconnect() {
    console.log('Disconnecting from backend...');

    // Disable auto-reconnect
    this.shouldReconnect = false;

    // Clear any pending retry attempts
    this._clearRetryTimeout();
    this._resetInitialConnectionFailures();

    // Clear startup grace period
    this._endStartupGracePeriod();

    // Stop heartbeat
    this.stopHeartbeat();

    // Close the socket connection
    if (this.socket) {
      this.socket.close(1000, 'Client disconnect'); // Clean close
      this.socket = null;
    }

    // Reset connection state
    this.isConnected = false;
    this.isConnecting = false;
    this.connectionStartTime = null;
    this.resetRetryAttempts();
    this.missedHeartbeats = 0;
    this.connectionMetadata.lastError = null;

    this._updateConnectionState(this.CONNECTION_STATES.DISCONNECTED, {
      reason: 'Manual disconnect'
    });
  }

  /**
   * Reconnect to the backend (manual reconnection)
   */
  async reconnect() {
    console.log('Manual reconnect requested...');

    // Enable auto-reconnect
    this.shouldReconnect = true;

    // Reset retry state
    this.resetRetryAttempts();
    this._clearRetryTimeout();

    // Disconnect first if connected
    if (this.isConnected || this.isConnecting) {
      this.disconnect();
      // Wait a bit before reconnecting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Re-enable auto-reconnect after disconnect
    this.shouldReconnect = true;

    // Attempt connection
    return this.connect();
  }

  /**
   * Handle incoming messages from the backend with adaptive timeout tracking
   */
  _handleMessage(data) {
    try {
      const message = JSON.parse(data);
      const { type, id } = message;

      // Handle connection rejection messages immediately
      if (type === 'connection_rejected') {
        this._handleConnectionRejection(message);
        return;
      }

      // Handle authentication failure messages immediately
      if (type === 'authentication_failure') {
        this._handleAuthenticationFailure(message);
        return;
      }

      // Handle response to a pending request
      if (id && this.pendingRequests.has(id)) {
        const pendingRequest = this.pendingRequests.get(id);
        const { resolve, timeoutHandle } = pendingRequest;

        // Clear timeout and remove from pending requests
        if (timeoutHandle) {
          clearTimeout(timeoutHandle);
        }
        this.pendingRequests.delete(id);

        // Update adaptive timeout state for successful response
        const command = message.command || (type === 'pong' ? 'ping' : 'unknown');
        this._updateAdaptiveTimeoutState(command, true);

        resolve(message);
        return;
      }

      // Handle broadcast messages
      if (this.messageHandlers.has(type)) {
        const handlers = this.messageHandlers.get(type);
        // Support both single handler (legacy) and multiple handlers (new)
        if (Array.isArray(handlers)) {
          handlers.forEach(handler => {
            try {
              handler(message);
            } catch (error) {
              console.error(`Error in message handler for ${type}:`, error);
            }
          });
        } else {
          // Legacy single handler support
          try {
            handlers(message);
          } catch (error) {
            console.error(`Error in message handler for ${type}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Error handling message:', error);
    }
  }

  /**
   * Send a command to the Python backend with adaptive timeout handling
   */
  sendCommand(command, params = {}) {
    return new Promise((resolve, reject) => {
      const requestId = Date.now().toString();
      const message = {
        command,
        params,
        id: requestId
      };

      // If not connected, queue the message for later
      if (!this.isConnected) {
        if (this.currentState === this.CONNECTION_STATES.RECONNECTING) {
          // Queue message for when reconnection succeeds
          this._queueMessage(message)
            .then(resolve)
            .catch(reject);
          return;
        } else {
          reject(new Error('Not connected to backend'));
          return;
        }
      }

      this._sendMessage(message)
        .then(resolve)
        .catch(reject);
    });
  }

  /**
   * Calculate adaptive timeout based on current conditions
   */
  _calculateAdaptiveTimeout(command) {
    const { adaptiveTimeouts } = this;

    // Use base timeout for non-ping commands
    if (command !== 'ping') {
      return adaptiveTimeouts.baseTimeout;
    }

    // For ping commands, use adaptive timeout
    let timeout = adaptiveTimeouts.pingTimeout;

    // Increase timeout if QueueIT tasks are active
    if (adaptiveTimeouts.queueTasksActive) {
      timeout = Math.min(timeout * 2, adaptiveTimeouts.maxTimeout);
    }

    // Increase timeout based on consecutive failures
    if (adaptiveTimeouts.consecutiveTimeouts > 0) {
      const failureMultiplier = 1 + (adaptiveTimeouts.consecutiveTimeouts * 0.5);
      timeout = Math.min(timeout * failureMultiplier, adaptiveTimeouts.maxTimeout);
    }

    // Increase timeout if it's been a while since last successful ping
    const timeSinceLastSuccess = Date.now() - adaptiveTimeouts.lastSuccessfulPing;
    if (timeSinceLastSuccess > 60000) { // More than 1 minute
      timeout = Math.min(timeout * 1.5, adaptiveTimeouts.maxTimeout);
    }

    return Math.floor(timeout);
  }

  /**
   * Update adaptive timeout state based on command result
   */
  _updateAdaptiveTimeoutState(command, success, error = null) {
    const { adaptiveTimeouts } = this;

    if (command === 'ping') {
      if (success) {
        // Reset on successful ping
        adaptiveTimeouts.consecutiveTimeouts = 0;
        adaptiveTimeouts.lastSuccessfulPing = Date.now();
        adaptiveTimeouts.adaptiveMultiplier = Math.max(1.0, adaptiveTimeouts.adaptiveMultiplier * 0.9);

        // Gradually reduce ping timeout back to base level
        adaptiveTimeouts.pingTimeout = Math.max(
          adaptiveTimeouts.baseTimeout,
          adaptiveTimeouts.pingTimeout * 0.95
        );
      } else if (error && error.includes('timeout')) {
        // Increase timeout on ping timeout
        adaptiveTimeouts.consecutiveTimeouts++;
        adaptiveTimeouts.adaptiveMultiplier = Math.min(4.0, adaptiveTimeouts.adaptiveMultiplier * 1.2);
        adaptiveTimeouts.pingTimeout = Math.min(
          adaptiveTimeouts.maxTimeout,
          adaptiveTimeouts.pingTimeout * 1.3
        );

        console.warn(`Ping timeout detected. Adaptive timeout increased to ${adaptiveTimeouts.pingTimeout}ms`);
      }
    }
  }

  /**
   * Set QueueIT task activity state for adaptive timeout management
   */
  setQueueTasksActive(active) {
    const wasActive = this.adaptiveTimeouts.queueTasksActive;
    this.adaptiveTimeouts.queueTasksActive = active;

    if (active && !wasActive) {
      console.log('QueueIT tasks started - enabling adaptive timeout mode');
      // Immediately increase ping timeout when tasks start
      this.adaptiveTimeouts.pingTimeout = Math.min(
        this.adaptiveTimeouts.maxTimeout,
        this.adaptiveTimeouts.pingTimeout * 1.5
      );

      // Register for queue progress updates to detect completion
      this._registerQueueProgressHandler();
    } else if (!active && wasActive) {
      console.log('QueueIT tasks stopped - returning to normal timeout mode');
      // Gradually return to normal timeout
      setTimeout(() => {
        this.adaptiveTimeouts.pingTimeout = this.adaptiveTimeouts.baseTimeout;
        this.adaptiveTimeouts.consecutiveTimeouts = 0;
      }, 5000); // Wait 5 seconds before reducing timeout

      // Unregister queue progress handler
      this._unregisterQueueProgressHandler();
    }
  }

  /**
   * Register handler for queue progress updates to detect task completion
   */
  _registerQueueProgressHandler() {
    if (!this._queueProgressHandler) {
      this._queueProgressHandler = (message) => {
        this._handleQueueProgressUpdate(message);
      };
      this.registerHandler('queue_progress_update', this._queueProgressHandler);
    }
  }

  /**
   * Unregister queue progress handler
   */
  _unregisterQueueProgressHandler() {
    if (this._queueProgressHandler) {
      this.unregisterHandler('queue_progress_update', this._queueProgressHandler);
      this._queueProgressHandler = null;
    }
  }

  /**
   * Handle queue progress updates to detect task completion
   */
  _handleQueueProgressUpdate(message) {
    try {
      const { data } = message;

      // Check if all tasks are completed or failed
      if (data && data.overall_status) {
        const { overall_status } = data;

        // If tasks are completed or failed, disable adaptive mode
        if (overall_status === 'completed' || overall_status === 'failed' || overall_status === 'stopped') {
          console.log(`QueueIT tasks ${overall_status} - disabling adaptive ping mode`);
          this.setQueueTasksActive(false);
        }
      }
    } catch (error) {
      console.warn('Error handling queue progress update for adaptive timeouts:', error);
    }
  }

  /**
   * Get current adaptive timeout status for debugging and monitoring
   */
  getAdaptiveTimeoutStatus() {
    return {
      ...this.adaptiveTimeouts,
      currentPingTimeout: this._calculateAdaptiveTimeout('ping'),
      isAdaptiveModeActive: this.adaptiveTimeouts.queueTasksActive || this.adaptiveTimeouts.consecutiveTimeouts > 0
    };
  }

  /**
   * Setup global error handling for uncaught ping timeout errors
   */
  _setupGlobalErrorHandling() {
    // Handle uncaught promise rejections that might be ping timeouts
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        if (event.reason && event.reason.message && event.reason.message.includes('Request timeout for command: ping')) {
          // Prevent the error from being logged to console
          event.preventDefault();

          // Handle the ping timeout gracefully
          this._handleGracefulPingTimeout();
        }
      });

      // Handle uncaught errors
      window.addEventListener('error', (event) => {
        if (event.error && event.error.message && event.error.message.includes('Request timeout for command: ping')) {
          // Prevent the error from being logged to console
          event.preventDefault();

          // Handle the ping timeout gracefully
          this._handleGracefulPingTimeout();
        }
      });
    }
  }

  /**
   * Handle ping timeouts gracefully without flooding the console
   */
  _handleGracefulPingTimeout() {
    const now = Date.now();

    // Only show timeout warnings at most once every 30 seconds
    if (now - this.errorHandling.lastTimeoutWarning > this.errorHandling.timeoutWarningInterval) {
      console.warn('🔄 Backend under load - ping timeouts detected. Adaptive timeout mode active.');
      this.errorHandling.lastTimeoutWarning = now;

      // Notify error callbacks
      this.errorHandling.errorCallbacks.forEach(callback => {
        try {
          callback({
            type: 'ping_timeout',
            message: 'Backend experiencing high load',
            adaptiveMode: this.adaptiveTimeouts.queueTasksActive,
            currentTimeout: this._calculateAdaptiveTimeout('ping')
          });
        } catch (error) {
          console.error('Error in ping timeout callback:', error);
        }
      });
    }
  }

  /**
   * Register callback for error notifications
   */
  onError(callback) {
    this.errorHandling.errorCallbacks.add(callback);
    return () => this.errorHandling.errorCallbacks.delete(callback);
  }

  /**
   * Internal method to send a message with adaptive timeout handling
   */
  _sendMessage(message) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected || !this.socket || this.socket.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket not ready'));
        return;
      }

      const requestId = message.id;
      const command = message.command;

      // Calculate adaptive timeout for this command
      const timeout = this._calculateAdaptiveTimeout(command);

      // Store the promise resolvers
      this.pendingRequests.set(requestId, { resolve, reject });

      // Send the message
      try {
        this.socket.send(JSON.stringify(message));
        console.log(`Sent command: ${command} (ID: ${requestId}, timeout: ${timeout}ms)`);
      } catch (error) {
        this.pendingRequests.delete(requestId);
        this._updateAdaptiveTimeoutState(command, false, error.message);
        reject(error);
        return;
      }

      // Set adaptive timeout for the request
      const timeoutHandle = setTimeout(() => {
        if (this.pendingRequests.has(requestId)) {
          this.pendingRequests.delete(requestId);
          const timeoutError = `Request timeout for command: ${command}`;
          this._updateAdaptiveTimeoutState(command, false, timeoutError);
          reject(new Error(timeoutError));
        }
      }, timeout);

      // Store timeout handle for potential cleanup
      const pendingRequest = this.pendingRequests.get(requestId);
      if (pendingRequest) {
        pendingRequest.timeoutHandle = timeoutHandle;
      }
    });
  }

  /**
   * Register a handler for a specific message type
   * Supports multiple handlers per message type
   */
  registerHandler(messageType, handler) {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, []);
    }
    const handlers = this.messageHandlers.get(messageType);
    if (!handlers.includes(handler)) {
      handlers.push(handler);
    }
  }

  /**
   * Unregister a specific message handler
   * If no handler specified, removes all handlers for the message type
   */
  unregisterHandler(messageType, handler = null) {
    if (!this.messageHandlers.has(messageType)) {
      return;
    }

    if (handler === null) {
      // Remove all handlers for this message type
      this.messageHandlers.delete(messageType);
    } else {
      // Remove specific handler
      const handlers = this.messageHandlers.get(messageType);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
      // If no handlers left, remove the message type
      if (handlers.length === 0) {
        this.messageHandlers.delete(messageType);
      }
    }
  }

  /**
   * Send a ping to check connection health with adaptive timeout handling
   *
   * Enhanced to work with 2-second ping intervals and improved license validation.
   * The backend now validates licenses every 30 seconds during ping cycles.
   * Includes adaptive timeout management for better handling during QueueIT tasks.
   */
  async ping() {
    try {
      const currentTimeout = this._calculateAdaptiveTimeout('ping');
      console.debug(`Sending ping with adaptive timeout: ${currentTimeout}ms`);

      const response = await this.sendCommand('ping');

      // Handle enhanced pong response format
      if (response.type === 'pong') {
        if (response.license_status === 'invalid') {
          // License validation failed
          console.warn('License validation failed during ping:', response.license_error);

          // Dispatch authentication failure event
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('authenticationFailure', {
              detail: {
                reason: 'invalid_license',
                message: response.license_error || 'License validation failed during health check'
              }
            }));
          }

          return response;
        } else if (response.license_status === 'valid') {
          // License is valid, connection healthy
          console.debug('Ping successful - connection and license healthy');
          return response;
        }
      }

      // Handle legacy response formats for backward compatibility
      if (response.license_status === 'none') {
        // No license key set, this is normal
        return response;
      }

      // Check for authentication failure (legacy format)
      if (response.error === 'Authentication required' || response.error === 'License expired') {
        // Clear stored license
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('authenticationFailure', {
            detail: {
              reason: response.error === 'License expired' ? 'expired_license' : 'invalid_license',
              message: response.error === 'License expired' ?
                'License has expired. Please renew your subscription.' :
                'License validation failed. Please log in again.'
            }
          }));
        }
        return response;
      }

      return response;
    } catch (error) {
      // Enhanced error handling for ping timeouts
      if (error.message && error.message.includes('timeout')) {
        console.warn(`Ping timeout (adaptive timeout: ${this._calculateAdaptiveTimeout('ping')}ms):`, error.message);

        // Don't throw timeout errors to prevent uncaught runtime errors
        // Instead, return a synthetic error response
        return {
          type: 'pong',
          server_status: 'timeout',
          error: 'Ping timeout - backend may be under heavy load',
          timeout_duration: this._calculateAdaptiveTimeout('ping')
        };
      } else {
        console.error('Ping failed with non-timeout error:', error);
        throw error;
      }
    }
  }

  /**
   * Get connection status from backend
   */
  async getConnectionStatus() {
    return this.sendCommand('get_connection_status');
  }

  /**
   * Get documentation content from backend
   */
  async getDocumentation() {
    return this.sendCommand('get_documentation');
  }

  /**
   * Get settings data from backend
   */
  async getSettings() {
    return this.sendCommand('get_settings');
  }

  /**
   * Refresh documentation content from backend when menu is opened
   */
  async refreshDocumentation() {
    return this.sendCommand('refresh_documentation');
  }

  /**
   * Refresh settings data from backend when menu is opened
   */
  async refreshSettings() {
    return this.sendCommand('refresh_settings');
  }

  /**
   * Update settings on backend
   */
  async updateSettings(settings) {
    return this.sendCommand('update_settings', settings);
  }

  /**
   * Start a queue entry task with adaptive ping management
   */
  async startQueueEntry(task) {
    try {
      console.log('Starting QueueIT task - enabling adaptive ping mode');

      // Enable adaptive timeout mode before starting task
      this.setQueueTasksActive(true);

      const response = await this.sendCommand('start_queue_entry', task);

      // If task started successfully, keep adaptive mode enabled
      if (response.type === 'queue_entry_started') {
        console.log(`QueueIT task started successfully (ID: ${response.task_id}) - adaptive ping mode active`);
      } else {
        // If task failed to start, disable adaptive mode
        console.log('QueueIT task failed to start - disabling adaptive ping mode');
        this.setQueueTasksActive(false);
      }

      return response;
    } catch (error) {
      // If error occurred, disable adaptive mode
      console.log('QueueIT task start error - disabling adaptive ping mode');
      this.setQueueTasksActive(false);
      throw error;
    }
  }

  /**
   * Stop a queue entry task with adaptive ping management
   */
  async stopQueueEntry(entryId) {
    try {
      console.log('Stopping QueueIT task - preparing to disable adaptive ping mode');

      const response = await this.sendCommand('stop_queue_entry', { entry_id: entryId });

      // Disable adaptive mode after stopping task
      if (response.type === 'queue_entry_stopped' || response.type === 'error') {
        console.log('QueueIT task stopped - disabling adaptive ping mode');
        this.setQueueTasksActive(false);
      }

      return response;
    } catch (error) {
      // Even if stop command fails, disable adaptive mode
      console.log('QueueIT task stop error - disabling adaptive ping mode anyway');
      this.setQueueTasksActive(false);
      throw error;
    }
  }

  /**
   * Logout and clear all stored authentication data from backend
   */
  async logOut() {
    try {
      const response = await this.sendCommand('logout', {});

      if (response.error) {
        // Clear stored license on error
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('authenticationFailure', {
            detail: {
              reason: 'invalid_license',
              message: 'License validation failed. Please log in again.'
            }
          }));
        }
        return response;
      }
      
      return response;
    } catch (error) {
      console.error('Failed to set license key:', error);
      throw error;
    }
  }

  /**
   * Validate license key with WHOP API
   */
  async validateLicense(licenseKey) {
    try {
      const response = await this.sendCommand('validate_license', { license_key: licenseKey });
      
      if (response.error) {
        // Clear stored license on error
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('authenticationFailure', {
            detail: {
              reason: 'invalid_license',
              message: 'License validation failed. Please log in again.'
            }
          }));
        }
        return response;
      }
      
      return response;
    } catch (error) {
      console.error('Failed to validate license:', error);
      throw error;
    }
  }

  /**
   * Test webhook configuration by sending a test message
   */
  async testWebhook() {
    try {
      const response = await this.sendCommand('test_webhook', {});
      return response;
    } catch (error) {
      console.error('Failed to test webhook:', error);
      throw error;
    }
  }

  /**
   * Handle connection rejection from backend (single-instance policy)
   */
  _handleConnectionRejection(message) {
    console.error('Connection rejected by backend:', message);

    // Update connection state
    this.isConnected = false;
    this.isConnecting = false;
    this._updateConnectionState(this.CONNECTION_STATES.DISCONNECTED);

    // Trigger connection rejection event for the app to handle
    if (this.messageHandlers.has('connection_rejected')) {
      const handler = this.messageHandlers.get('connection_rejected');
      handler(message);
    }

    // Also emit a custom event that can be listened to globally
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('connectionRejected', {
        detail: {
          reason: message.reason,
          message: message.message,
          primary_client_id: message.primary_client_id,
          primary_client_connected_since: message.primary_client_connected_since
        }
      }));
    }
  }

  /**
   * Handle authentication failure from backend
   */
  _handleAuthenticationFailure(message) {
    console.warn('Authentication failure received from backend:', message);

    // Trigger authentication failure event for the app to handle
    if (this.messageHandlers.has('authentication_failure')) {
      const handler = this.messageHandlers.get('authentication_failure');
      handler(message);
    }

    // Also emit a custom event that can be listened to globally
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('authenticationFailure', {
        detail: {
          reason: message.reason,
          message: message.message,
          error: message.error
        }
      }));
    }
  }

  /**
   * Start periodic ping to maintain connection with enhanced health monitoring
   *
   * With the backend now using 2-second ping intervals, we can reduce our
   * frontend heartbeat frequency to be more responsive while still being
   * conservative to avoid overwhelming the connection.
   * Includes adaptive timeout handling to prevent errors during QueueIT tasks.
   */
  startHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }

    // Send initial ping with error handling
    this._performHeartbeatPing();

    // Set up interval for every 30 seconds to complement the 2-second server pings
    // This provides a good balance between responsiveness and not overwhelming the connection
    this.heartbeatInterval = setInterval(() => {
      this._performHeartbeatPing();
    }, 30000); // 30 seconds in milliseconds
  }

  /**
   * Perform a heartbeat ping with enhanced error handling
   */
  async _performHeartbeatPing() {
    try {
      const response = await this.ping();

      // Log successful pings only in debug mode to reduce console noise
      if (response.server_status === 'timeout') {
        console.debug('Heartbeat ping timed out - backend may be under load');
      } else {
        console.debug('Heartbeat ping successful');
      }
    } catch (error) {
      // Log heartbeat errors but don't throw to prevent uncaught exceptions
      console.warn('Heartbeat ping failed:', error.message);

      // If we're getting consistent failures, consider triggering reconnection
      if (this.adaptiveTimeouts.consecutiveTimeouts > 5) {
        console.warn('Multiple consecutive ping failures detected - connection may be unstable');
      }
    }
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }

    console.log('Heartbeat stopped');
  }

  /**
   * Auto-reconnect functionality (legacy method for compatibility)
   */
  async attemptReconnect() {
    console.log('Legacy attemptReconnect called, using new retry system');
    if (!this.shouldReconnect) {
      return false;
    }

    this._startRetryProcess();
    return true;
  }
}

// Create and export the singleton instance
const backendService = new BackendService();
export default backendService;