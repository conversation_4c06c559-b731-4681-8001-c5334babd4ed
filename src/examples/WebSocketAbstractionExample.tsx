/**
 * WebSocket Abstraction Layer Integration Example
 * 
 * This example demonstrates how to use the new WebSocket abstraction layer
 * in a React component, showcasing type safety, error handling, and
 * real-time communication features.
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  enhancedBackendService,
  ConnectionState,
  QueueEntryParams,
  QueueStatus,
  ConnectionStatus
} from '../services/EnhancedBackendService';
import { ErrorCode } from '../shared/messageSchemas';

interface ExampleState {
  connectionState: ConnectionState;
  connectionStatus?: ConnectionStatus;
  queueStatus?: QueueStatus;
  licenseValid: boolean;
  error?: string;
  logs: string[];
}

export const WebSocketAbstractionExample: React.FC = () => {
  const [state, setState] = useState<ExampleState>({
    connectionState: ConnectionState.DISCONNECTED,
    licenseValid: false,
    logs: []
  });

  const addLog = useCallback((message: string) => {
    setState(prev => ({
      ...prev,
      logs: [...prev.logs.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]
    }));
  }, []);

  // Initialize the service and set up event handlers
  useEffect(() => {
    const initializeService = async () => {
      try {
        addLog('Initializing enhanced backend service...');
        
        // Enable debug logging
        enhancedBackendService.setDebug(true);
        
        // Configure rate limiting
        enhancedBackendService.setRateLimit(50, 60000); // 50 messages per minute
        
        // Initialize connection
        await enhancedBackendService.initialize();
        addLog('Service initialized successfully');
        
      } catch (error) {
        addLog(`Initialization failed: ${error}`);
        setState(prev => ({ ...prev, error: `Initialization failed: ${error}` }));
      }
    };

    initializeService();

    // Set up event handlers
    const unsubscribeConnection = enhancedBackendService.onConnectionStateChange(
      (connectionState, metadata) => {
        setState(prev => ({ ...prev, connectionState }));
        addLog(`Connection state changed: ${connectionState}`);
        
        if (metadata?.error) {
          addLog(`Connection error: ${metadata.error}`);
        }
      }
    );

    const unsubscribeQueueProgress = enhancedBackendService.onQueueProgress(
      (update) => {
        addLog(`Queue progress: ${update.overall_status} (${update.pass_links_count}/${update.ticket_count})`);
        setState(prev => ({ ...prev, queueStatus: update }));
      }
    );

    const unsubscribeLicenseUpdate = enhancedBackendService.onLicenseUpdate(
      (licenseData) => {
        addLog(`License updated: ${licenseData.valid ? 'valid' : 'invalid'}`);
        setState(prev => ({ ...prev, licenseValid: licenseData.valid }));
      }
    );

    return () => {
      unsubscribeConnection();
      unsubscribeQueueProgress();
      unsubscribeLicenseUpdate();
    };
  }, [addLog]);

  // Test connection status
  const testConnectionStatus = async () => {
    try {
      addLog('Testing connection status...');
      const status = await enhancedBackendService.getConnectionStatus();
      setState(prev => ({ ...prev, connectionStatus: status }));
      addLog(`Connection status received: ${status.connected_clients} clients connected`);
    } catch (error) {
      addLog(`Connection status failed: ${error}`);
    }
  };

  // Test ping
  const testPing = async () => {
    try {
      addLog('Sending ping...');
      const response = await enhancedBackendService.ping();
      addLog(`Ping response: ${response.server_status}`);
    } catch (error) {
      addLog(`Ping failed: ${error}`);
    }
  };

  // Test license validation
  const testLicenseValidation = async () => {
    const licenseKey = prompt('Enter license key:');
    if (!licenseKey) return;

    try {
      addLog('Validating license...');
      const result = await enhancedBackendService.validateLicense(licenseKey);
      setState(prev => ({ ...prev, licenseValid: result.valid }));
      addLog(`License validation: ${result.valid ? 'valid' : 'invalid'}`);
      
      if (!result.valid && result.error) {
        addLog(`License error: ${result.error}`);
      }
    } catch (error) {
      addLog(`License validation failed: ${error}`);
    }
  };

  // Test queue entry
  const testQueueEntry = async () => {
    const url = prompt('Enter Queue-IT URL:', 'https://example.queue-it.net');
    const tickets = prompt('Enter number of tickets:', '3');
    
    if (!url || !tickets) return;

    const params: QueueEntryParams = {
      url,
      tickets: parseInt(tickets, 10)
    };

    try {
      addLog(`Starting queue entry: ${params.tickets} tickets for ${params.url}`);
      const result = await enhancedBackendService.startQueueEntry(params);
      addLog(`Queue entry started: ${result.task_id}`);
    } catch (error: any) {
      addLog(`Queue entry failed: ${error.message || error}`);
      
      // Handle specific error codes
      if (error.code === ErrorCode.AUTHENTICATION_REQUIRED) {
        addLog('Authentication required - please validate license first');
      } else if (error.code === ErrorCode.RATE_LIMITED) {
        addLog('Rate limited - please wait before sending more requests');
      }
    }
  };

  // Test queue status
  const testQueueStatus = async () => {
    try {
      addLog('Getting queue status...');
      const status = await enhancedBackendService.getQueueStatus();
      setState(prev => ({ ...prev, queueStatus: status }));
      addLog(`Queue status: ${status.overall_status} (${status.pass_links_count}/${status.ticket_count})`);
    } catch (error) {
      addLog(`Queue status failed: ${error}`);
    }
  };

  // Stop queue entry
  const stopQueueEntry = async () => {
    try {
      addLog('Stopping queue entry...');
      const result = await enhancedBackendService.stopQueueEntry();
      addLog(`Queue entry stopped: ${result.success ? 'success' : 'failed'}`);
    } catch (error) {
      addLog(`Stop queue failed: ${error}`);
    }
  };

  // Get service statistics
  const getStats = () => {
    const stats = enhancedBackendService.getStats();
    addLog(`Stats: ${stats.messagesSent} sent, ${stats.messagesReceived} received, ${stats.errorsEncountered} errors`);
  };

  // Clear logs
  const clearLogs = () => {
    setState(prev => ({ ...prev, logs: [] }));
  };

  const isConnected = enhancedBackendService.isConnected();

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>WebSocket Abstraction Layer Example</h2>
      
      {/* Connection Status */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f5f5f5' }}>
        <h3>Connection Status</h3>
        <div>State: <strong style={{ color: isConnected ? 'green' : 'red' }}>
          {state.connectionState}
        </strong></div>
        <div>License: <strong style={{ color: state.licenseValid ? 'green' : 'red' }}>
          {state.licenseValid ? 'Valid' : 'Invalid'}
        </strong></div>
        {state.connectionStatus && (
          <div>
            <div>Server Time: {new Date(state.connectionStatus.server_time * 1000).toLocaleString()}</div>
            <div>Connected Clients: {state.connectionStatus.connected_clients}</div>
            <div>Primary Client: {state.connectionStatus.is_primary_client ? 'Yes' : 'No'}</div>
          </div>
        )}
      </div>

      {/* Queue Status */}
      {state.queueStatus && (
        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#e8f4fd' }}>
          <h3>Queue Status</h3>
          <div>Task ID: {state.queueStatus.task_id}</div>
          <div>Status: <strong>{state.queueStatus.overall_status}</strong></div>
          <div>Progress: {state.queueStatus.pass_links_count}/{state.queueStatus.ticket_count} tickets</div>
          <div>Running: {state.queueStatus.is_running ? 'Yes' : 'No'}</div>
          {state.queueStatus.error_message && (
            <div style={{ color: 'red' }}>Error: {state.queueStatus.error_message}</div>
          )}
        </div>
      )}

      {/* Control Buttons */}
      <div style={{ marginBottom: '20px' }}>
        <h3>Test Functions</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '10px' }}>
          <button onClick={testConnectionStatus} disabled={!isConnected}>
            Test Connection Status
          </button>
          <button onClick={testPing} disabled={!isConnected}>
            Test Ping
          </button>
          <button onClick={testLicenseValidation} disabled={!isConnected}>
            Test License Validation
          </button>
          <button onClick={testQueueEntry} disabled={!isConnected || !state.licenseValid}>
            Start Queue Entry
          </button>
          <button onClick={testQueueStatus} disabled={!isConnected}>
            Get Queue Status
          </button>
          <button onClick={stopQueueEntry} disabled={!isConnected}>
            Stop Queue Entry
          </button>
          <button onClick={getStats}>
            Get Statistics
          </button>
          <button onClick={clearLogs}>
            Clear Logs
          </button>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#ffe6e6', color: 'red' }}>
          <h3>Error</h3>
          <div>{state.error}</div>
        </div>
      )}

      {/* Logs */}
      <div>
        <h3>Activity Log</h3>
        <div style={{ 
          height: '300px', 
          overflow: 'auto', 
          border: '1px solid #ccc', 
          padding: '10px',
          backgroundColor: '#f9f9f9',
          fontSize: '12px'
        }}>
          {state.logs.map((log, index) => (
            <div key={index} style={{ marginBottom: '2px' }}>
              {log}
            </div>
          ))}
          {state.logs.length === 0 && (
            <div style={{ color: '#666' }}>No activity yet...</div>
          )}
        </div>
      </div>

      {/* Usage Instructions */}
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f8ff' }}>
        <h3>Usage Instructions</h3>
        <ol>
          <li>The service automatically connects when the component mounts</li>
          <li>Test the connection status to verify communication</li>
          <li>Validate a license key to enable queue operations</li>
          <li>Start a queue entry with a valid Queue-IT URL</li>
          <li>Monitor progress updates in real-time</li>
          <li>Check statistics to see message counts and performance</li>
        </ol>
      </div>
    </div>
  );
};

export default WebSocketAbstractionExample;
