{"name": "kasper-q", "version": "1.0.0", "description": "Advanced AI-powered penetration testing tool with multiple operational modes", "main": "electron/main.js", "homepage": "./", "private": true, "author": {"name": "<PERSON><PERSON>-Q Team", "email": "<EMAIL>"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "backend": "cd backend && python start_server.py", "backend-test": "cd backend && python test_websocket.py", "dev-full": "concurrently \"npm run backend\" \"npm start\" \"wait-on http://localhost:3000 && wait-on ws://localhost:8765 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build", "dist": "npm run build && electron-builder --publish=never", "dist-mac": "npm run build && electron-builder --mac", "dist-win": "npm run build && electron-builder --win", "dist-linux": "npm run build && electron-builder --linux"}, "dependencies": {"@fontsource/inter": "^5.2.5", "@headlessui/react": "^2.2.4", "@whop/iframe": "^0.0.3", "antd": "^5.26.1", "axios": "^1.6.0", "classnames": "^2.3.2", "electron-is-dev": "^2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.6.4", "wait-on": "^7.2.0", "web-vitals": "^2.1.4"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.kasperq.app", "productName": "<PERSON><PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": "nsis", "arch": ["x64"]}, "linux": {"target": "AppImage", "arch": ["x64"]}}}