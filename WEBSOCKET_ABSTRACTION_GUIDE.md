# WebSocket Communication Abstraction Layer

## Overview

This document describes the new centralized WebSocket communication abstraction layer for Kasper-Q. The abstraction provides standardized message handling, type safety, transformation pipelines, and enhanced error handling between the React frontend and Python backend.

## Architecture

### Core Components

1. **Shared Message Schemas** (`shared/message_schemas.py` & `src/shared/messageSchemas.ts`)
   - Standardized message format with type, payload, metadata, and error handling
   - Message versioning for backward compatibility
   - Request/response correlation using unique message IDs

2. **Backend Message Protocol** (`backend/message_protocol.py`)
   - Centralized message processing with validation and transformation
   - Extensible hook system for payload transformation
   - Rate limiting and error handling with recovery suggestions

3. **Frontend WebSocket Manager** (`src/services/WebSocketManager.ts`)
   - Type-safe WebSocket communication wrapper
   - Automatic retry logic with exponential backoff
   - Connection state management and message queuing

4. **Transformation Hooks** (`backend/transform_hooks.py` & `src/services/transformHooks.ts`)
   - Compression for large messages
   - Encryption for secure transmission
   - Logging and metrics collection
   - Message validation

## Message Format

### Standard Message Structure

```typescript
interface BaseMessage {
  type: MessageType;
  metadata: {
    version: string;
    timestamp: number;
    correlationId?: string;
    retryCount: number;
    priority: number;
  };
  payload: Record<string, any>;
  error?: {
    code: ErrorCode;
    message: string;
    details?: Record<string, any>;
    recoverySuggestions?: string[];
  };
}
```

### Request/Response Pattern

```typescript
// Request
{
  type: "request",
  metadata: { version: "1.0", timestamp: 1234567890 },
  payload: {
    command: "start_queue_entry",
    params: { url: "...", tickets: 5 },
    requestId: "uuid-123"
  }
}

// Response
{
  type: "response",
  metadata: { version: "1.0", timestamp: 1234567891 },
  payload: {
    requestId: "uuid-123",
    success: true,
    data: { task_id: "task_456", status: "started" }
  }
}
```

## Migration Guide

### Frontend Migration

#### Before (Legacy BackendService)
```javascript
import BackendService from './services/BackendService';

// Connect
await BackendService.connect();

// Send command
const result = await BackendService.sendCommand('start_queue_entry', {
  url: 'https://example.com',
  tickets: 5
});

// Register handler
BackendService.registerHandler('queue_progress_update', (message) => {
  console.log('Progress:', message);
});
```

#### After (Enhanced BackendService)
```typescript
import { enhancedBackendService } from './services/EnhancedBackendService';

// Initialize
await enhancedBackendService.initialize();

// Send command (type-safe)
const result = await enhancedBackendService.startQueueEntry({
  url: 'https://example.com',
  tickets: 5
});

// Register handler (type-safe)
const unsubscribe = enhancedBackendService.onQueueProgress((update) => {
  console.log('Progress:', update);
});
```

### Backend Integration

The backend WebSocket server automatically uses the new message protocol while maintaining backward compatibility:

```python
# The server now includes:
from message_protocol import MessageProtocol
from transform_hooks import CompressionHook, ValidationHook

# In WebSocketServer.__init__():
self.message_protocol = MessageProtocol(enable_rate_limiting=True)
self.message_protocol.add_transform_hook(ValidationHook())
self.message_protocol.add_transform_hook(CompressionHook())
```

## Features

### 1. Type Safety

**Frontend:**
```typescript
// Type-safe command methods
async startQueueEntry(params: QueueEntryParams): Promise<QueueStartResult>
async getQueueStatus(): Promise<QueueStatus>
async validateLicense(key: string): Promise<LicenseValidationResult>
```

**Backend:**
```python
# Type hints for message handling
async def process_message(self, raw_message: str, client_id: str) -> Optional[BaseMessage]
def register_command_handler(self, command: str, handler: Callable)
```

### 2. Transformation Pipeline

**Compression Hook:**
```typescript
// Automatically compresses messages > 1KB
const compressionHook = new CompressionHook();
wsManager.addTransformHook(compressionHook);
```

**Encryption Hook:**
```typescript
// Encrypts sensitive data
const encryptionHook = new EncryptionHook('secret-key');
wsManager.addTransformHook(encryptionHook);
```

**Metrics Collection:**
```typescript
// Collects performance metrics
const metricsHook = new MetricsHook();
wsManager.addTransformHook(metricsHook);
console.log(metricsHook.getMetrics());
```

### 3. Enhanced Error Handling

```typescript
// Standardized error codes and recovery suggestions
try {
  await enhancedBackendService.startQueueEntry(params);
} catch (error) {
  if (error.code === ErrorCode.AUTHENTICATION_REQUIRED) {
    // Show login dialog
    console.log('Recovery:', error.recoverySuggestions);
  }
}
```

### 4. Connection Management

```typescript
// Automatic retry with exponential backoff
const wsManager = new WebSocketManager({
  url: 'ws://localhost:8765',
  maxRetries: Infinity,
  retryDelay: 1000,
  maxRetryDelay: 30000,
  backoffFactor: 2
});

// Connection state monitoring
wsManager.onStateChange((state, metadata) => {
  switch (state) {
    case ConnectionState.CONNECTED:
      console.log('Connected to backend');
      break;
    case ConnectionState.RECONNECTING:
      console.log('Reconnecting...');
      break;
    case ConnectionState.ERROR:
      console.error('Connection error:', metadata.error);
      break;
  }
});
```

### 5. Rate Limiting

```python
# Backend rate limiting
rate_limiter = RateLimitingHook(max_messages=100, window_seconds=60)
message_protocol.add_transform_hook(rate_limiter)
```

```typescript
// Frontend rate limiting
enhancedBackendService.setRateLimit(100, 60000); // 100 messages per minute
```

## Integration Examples

### React Component Integration

```tsx
import React, { useEffect, useState } from 'react';
import { enhancedBackendService, ConnectionState } from '../services/EnhancedBackendService';

export const QueueMonitor: React.FC = () => {
  const [connectionState, setConnectionState] = useState<ConnectionState>(
    enhancedBackendService.getConnectionState()
  );
  const [queueStatus, setQueueStatus] = useState<any>(null);

  useEffect(() => {
    // Initialize service
    enhancedBackendService.initialize();

    // Monitor connection state
    const unsubscribeConnection = enhancedBackendService.onConnectionStateChange(
      (state) => setConnectionState(state)
    );

    // Monitor queue progress
    const unsubscribeProgress = enhancedBackendService.onQueueProgress(
      (update) => setQueueStatus(update)
    );

    return () => {
      unsubscribeConnection();
      unsubscribeProgress();
    };
  }, []);

  const startQueue = async () => {
    try {
      await enhancedBackendService.startQueueEntry({
        url: 'https://example.queue-it.net',
        tickets: 5
      });
    } catch (error) {
      console.error('Failed to start queue:', error);
    }
  };

  return (
    <div>
      <div>Connection: {connectionState}</div>
      <div>Queue Status: {queueStatus?.overall_status}</div>
      <button onClick={startQueue} disabled={!enhancedBackendService.isConnected()}>
        Start Queue Entry
      </button>
    </div>
  );
};
```

### Custom Transformation Hook

```typescript
// Custom logging hook
class CustomLoggingHook implements MessageTransformHook {
  name = 'custom-logging';
  priority = 50;

  async encode(message: BaseMessage): Promise<BaseMessage> {
    console.log(`[OUT] ${message.type}:`, message.payload);
    return message;
  }

  async decode(message: BaseMessage): Promise<BaseMessage> {
    console.log(`[IN] ${message.type}:`, message.payload);
    return message;
  }
}

// Add to service
enhancedBackendService.addTransformHook(new CustomLoggingHook());
```

## Performance Considerations

1. **Message Compression**: Automatically compresses messages larger than 1KB
2. **Connection Pooling**: Single connection with multiplexed requests
3. **Rate Limiting**: Prevents message flooding and server overload
4. **Message Queuing**: Queues messages during disconnection for later delivery
5. **Efficient Serialization**: Optimized JSON serialization with error handling

## Security Features

1. **Message Validation**: Schema validation on both frontend and backend
2. **Encryption Support**: Optional message encryption for sensitive data
3. **Rate Limiting**: Protection against DoS attacks
4. **Error Sanitization**: Prevents information leakage in error messages
5. **Connection Authentication**: Integration with existing license validation

## Backward Compatibility

The new abstraction layer maintains full backward compatibility:

1. **Legacy Message Format**: Old message format still supported
2. **Existing Handlers**: Current message handlers continue to work
3. **Gradual Migration**: Components can be migrated incrementally
4. **Fallback Mechanism**: Automatic fallback to legacy processing

## Future Enhancements

1. **Message Encryption**: Full end-to-end encryption support
2. **Schema Evolution**: Automatic schema migration and versioning
3. **Performance Monitoring**: Built-in performance metrics and alerting
4. **Load Balancing**: Support for multiple backend instances
5. **Offline Support**: Enhanced offline message queuing and synchronization
