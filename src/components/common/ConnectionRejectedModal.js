import React from 'react';
import './ConnectionRejectedModal.css';

const ConnectionRejectedModal = ({ isOpen, onClose, rejectionDetails }) => {
  if (!isOpen) return null;

  const formatTimestamp = (timestamp) => {
    if (!timestamp) return 'Unknown';
    return new Date(timestamp * 1000).toLocaleString();
  };

  const handleRetry = () => {
    // Reload the page to attempt reconnection
    window.location.reload();
  };

  const handleClose = () => {
    // Close the window
    window.close();
  };

  return (
    <div className="connection-rejected-overlay">
      <div className="connection-rejected-modal">
        <div className="connection-rejected-header">
          <div className="connection-rejected-icon">⚠️</div>
          <h2><strong>Connection</strong> Rejected</h2>
        </div>
        
        <div className="connection-rejected-content">
          <p className="connection-rejected-message">
            {rejectionDetails?.message || 'Only <strong>one instance</strong> of Kasper-<PERSON> can be active at a time. <strong>Please close</strong> any other instances before continuing.'}
          </p>
          
          <div className="connection-rejected-details">
            <h3>Connection Details:</h3>
            <ul>
              <li><strong>Reason:</strong> {rejectionDetails?.reason || 'single_instance_policy'}</li>
              {rejectionDetails?.primary_client_id && (
                <li><strong>Active Client ID:</strong> {rejectionDetails.primary_client_id}</li>
              )}
              {rejectionDetails?.primary_client_connected_since && (
                <li><strong>Connected Since:</strong> {formatTimestamp(rejectionDetails.primary_client_connected_since)}</li>
              )}
            </ul>
          </div>
          
          <div className="connection-rejected-instructions">
            <h3>What to do:</h3>
            <ol>
              <li>Close any <strong>other running instances</strong> of Kasper-Q</li>
              <li>Make sure <strong>no other browser tabs</strong> have Kasper-Q open</li>
              <li>Wait a few seconds and <strong>try again</strong></li>
            </ol>
          </div>
        </div>
        
        <div className="connection-rejected-actions">
          <button 
            className="connection-rejected-retry-btn"
            onClick={handleRetry}
          >
            Retry Connection
          </button>
          <button 
            className="connection-rejected-close-btn"
            onClick={handleClose}
          >
            Close Window
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConnectionRejectedModal;
