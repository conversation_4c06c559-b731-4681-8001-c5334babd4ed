import React, { useState, useCallback, useEffect, useRef } from 'react';
import './BaseMode.css';

const BaseMode = ({ onEnterQueue }) => {
  const [eventUrl, setEventUrl] = useState('');
  const [ticketQuantity, setTicketQuantity] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const containerRef = useRef(null);

  const handleMouseMove = useCallback((e) => {
    if (!containerRef.current) return;
    
    const x = (e.clientX / window.innerWidth) * 100;
    const y = (e.clientY / window.innerHeight) * 100;
    
    containerRef.current.style.setProperty('--mouse-x', `${x}%`);
    containerRef.current.style.setProperty('--mouse-y', `${y}%`);
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
    };
  }, [handleMouseMove]);

  const handleStart = () => {
    const urlInput = document.getElementById('base-event-url');
    const qtyInput = document.getElementById('base-ticket-qty');
    const urlError = document.getElementById('base-url-error');
    const qtyError = document.getElementById('base-qty-error');
    const button = document.getElementById('base-start-btn');
    const inputCard = document.getElementById('base-input-card');
    const statusCard = document.getElementById('base-status-card');
    let hasError = false;

    // Reset previous errors
    if (urlError) urlError.classList.add('hidden');
    if (qtyError) qtyError.classList.add('hidden');
    setError('');

    // Validate URL
    if (!eventUrl) {
      if (urlError) {
        urlError.textContent = 'Please enter an event URL';
        urlError.classList.remove('hidden');
      }
      if (urlInput) {
        urlInput.classList.add('animate-shake');
        setTimeout(() => urlInput.classList.remove('animate-shake'), 120);
      }
      hasError = true;
    } else if (!isValidUrl(eventUrl)) {
      if (urlError) {
        urlError.textContent = 'Please enter a valid URL (e.g., https://...)';
        urlError.classList.remove('hidden');
      }
      if (urlInput) {
        urlInput.classList.add('animate-shake');
        setTimeout(() => urlInput.classList.remove('animate-shake'), 120);
      }
      hasError = true;
    }

    // Validate ticket quantity
    if (!ticketQuantity) {
      if (qtyError) {
        qtyError.textContent = 'Please enter ticket quantity';
        qtyError.classList.remove('hidden');
      }
      if (qtyInput) {
        qtyInput.classList.add('animate-shake');
        setTimeout(() => qtyInput.classList.remove('animate-shake'), 120);
      }
      hasError = true;
    } else {
      const qty = Number(ticketQuantity);
      if (isNaN(qty) || qty < 1) {
        if (qtyError) {
          qtyError.textContent = 'Quantity must be at least 1';
          qtyError.classList.remove('hidden');
        }
        if (qtyInput) {
          qtyInput.classList.add('animate-shake');
          setTimeout(() => qtyInput.classList.remove('animate-shake'), 120);
        }
        hasError = true;
      } else if (qty > 100) {
        if (qtyError) {
          qtyError.textContent = 'Maximum 10 tickets allowed';
          qtyError.classList.remove('hidden');
        }
        if (qtyInput) {
          qtyInput.classList.add('animate-shake');
          setTimeout(() => qtyInput.classList.remove('animate-shake'), 120);
        }
        hasError = true;
      }
    }

    if (hasError) return;

    setIsLoading(true);

    // Create ripple effect
    if (button) {
      const ripple = document.createElement('div');
      ripple.style.position = 'absolute';
      ripple.style.top = '50%';
      ripple.style.left = '50%';
      ripple.style.width = '0';
      ripple.style.height = '0';
      ripple.style.background = 'rgba(124, 77, 255, 0.2)';
      ripple.style.borderRadius = '50%';
      ripple.style.transform = 'translate(-50%, -50%)';
      ripple.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
      button.appendChild(ripple);

      // Animate ripple
      requestAnimationFrame(() => {
        ripple.style.width = '200%';
        ripple.style.height = '200%';
        ripple.style.opacity = '0';
      });

      // Add button click animation
      button.style.transform = 'scale(0.95)';
      button.style.opacity = '0.8';
    }

    // Fade out input card
    if (inputCard) {
      inputCard.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
      inputCard.style.opacity = '0';
      inputCard.style.transform = 'translateY(-20px)';
    }

    // Prepare status card
    if (statusCard) {
      statusCard.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
      statusCard.style.opacity = '0';
      statusCard.style.transform = 'translateY(20px)';
    }

    // Start monitoring after animations
    setTimeout(() => {
      // Pass the queue entry parameters to the parent
      onEnterQueue({
        eventUrl: eventUrl,
        ticketQuantity: Number(ticketQuantity)
      });
      setIsLoading(false);
      
      // Reset button state
      if (button) {
        button.style.transform = 'scale(1)';
        button.style.opacity = '1';
      }
      
      // Collapse input card
      if (inputCard) {
        inputCard.style.height = '48px';
        inputCard.style.overflow = 'hidden';
        inputCard.style.transform = 'translateY(0)';
      }
      
      // Expand status card
      if (statusCard) {
        statusCard.classList.add('expanded');
        statusCard.style.height = '400px';
        statusCard.style.opacity = '1';
        statusCard.style.transform = 'translateY(0)';
      }
    }, 600);
  };

  const isValidUrl = (str) => {
    try { new URL(str); return true; } catch { return false; }
  };

  return (
    <div className="base-mode-container" ref={containerRef}>
      <div id="base-input-card">
        <div className="card-header">
          <h1 className="card-title">Free the ghosts</h1>
          <p className="card-subtitle">Enter the event URL and ticket quantity to start monitoring</p>
        </div>

        <input
          type="text"
          placeholder="Paste Event URL here"
          id="base-event-url"
          className="glass-input"
          value={eventUrl}
          onChange={(e) => setEventUrl(e.target.value)}
          disabled={isLoading}
        />
        <p id="base-url-error" className="error-message hidden"></p>

        <input
          type="number"
          placeholder="Ticket quantity (e.g. 2)"
          min="1"
          id="base-ticket-qty"
          className="glass-input"
          value={ticketQuantity}
          onChange={(e) => setTicketQuantity(e.target.value)}
          disabled={isLoading}
        />
        <p id="base-qty-error" className="error-message hidden"></p>

        <button
          id="base-start-btn"
          onClick={handleStart}
          aria-label="Start monitoring event"
          className="start-button"
          style={{
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            background: 'rgba(124, 77, 255, 0.1)',
            border: '1px solid rgba(124, 77, 255, 0.3)',
            padding: '14px 28px',
            borderRadius: '12px',
            color: 'var(--color-accent)',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)',
            boxShadow: '0 0 0 0 rgba(124, 77, 255, 0)',
            transform: 'scale(1)',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(45deg, transparent, rgba(124, 77, 255, 0.1), transparent)',
              transform: 'translateX(-100%)',
              transition: 'transform 0.6s ease'
            }
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.02)';
            e.currentTarget.style.boxShadow = '0 0 20px rgba(124, 77, 255, 0.2)';
            e.currentTarget.style.border = '1px solid rgba(124, 77, 255, 0.5)';
            e.currentTarget.style.background = 'rgba(124, 77, 255, 0.15)';
            
            // Add shimmer effect
            const shimmer = document.createElement('div');
            shimmer.style.position = 'absolute';
            shimmer.style.top = '0';
            shimmer.style.left = '0';
            shimmer.style.right = '0';
            shimmer.style.bottom = '0';
            shimmer.style.background = 'linear-gradient(45deg, transparent, rgba(124, 77, 255, 0.2), transparent)';
            shimmer.style.transform = 'translateX(-100%)';
            shimmer.style.transition = 'transform 0.6s ease';
            e.currentTarget.appendChild(shimmer);
            
            requestAnimationFrame(() => {
              shimmer.style.transform = 'translateX(100%)';
            });
            
            setTimeout(() => {
              shimmer.remove();
            }, 600);
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.boxShadow = '0 0 0 0 rgba(124, 77, 255, 0)';
            e.currentTarget.style.border = '1px solid rgba(124, 77, 255, 0.3)';
            e.currentTarget.style.background = 'rgba(124, 77, 255, 0.1)';
          }}
          onMouseDown={(e) => {
            e.currentTarget.style.transform = 'scale(0.98)';
            e.currentTarget.style.background = 'rgba(124, 77, 255, 0.2)';
          }}
          onMouseUp={(e) => {
            e.currentTarget.style.transform = 'scale(1.02)';
            e.currentTarget.style.background = 'rgba(124, 77, 255, 0.15)';
          }}
          disabled={isLoading}
        >
          {isLoading ? 'Starting...' : 'Enter Queue'}
        </button>
      </div>
    </div>
  );
};

export default BaseMode; 