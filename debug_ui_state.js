// Debug script to check UI state - paste this into browser console
console.log('🔍 UI State Diagnostic');
console.log('='.repeat(50));

// Check if React DevTools are available
if (window.React) {
  console.log('✅ React detected');
} else {
  console.log('❌ React not detected');
}

// Check for common state variables in window
const stateVars = [
  'isValidatingStoredLicense',
  'isAutoLoginInProgress', 
  'isAuthenticated',
  'connectionState',
  'authError'
];

console.log('\n📊 Checking for state variables in window:');
stateVars.forEach(varName => {
  if (window[varName] !== undefined) {
    console.log(`✅ ${varName}:`, window[varName]);
  } else {
    console.log(`❌ ${varName}: not found in window`);
  }
});

// Check for BackendService
if (window.BackendService) {
  console.log('\n🔗 BackendService Status:');
  console.log('Connection State:', window.BackendService.getConnectionState());
  console.log('Connection Metadata:', window.BackendService.getConnectionMetadata());
  
  // Check for recent messages
  if (window.BackendService.getRecentMessages) {
    console.log('Recent Messages:', window.BackendService.getRecentMessages());
  }
} else {
  console.log('❌ BackendService not found in window');
}

// Check for LicenseStorage
if (window.LicenseStorage) {
  console.log('\n🔐 LicenseStorage Status:');
  console.log('Has Stored License:', window.LicenseStorage.hasStoredLicense());
  if (window.LicenseStorage.hasStoredLicense()) {
    console.log('License Key:', window.LicenseStorage.getLicenseKey()?.substring(0, 8) + '...');
    console.log('License Info:', window.LicenseStorage.getLicenseInfo());
  }
} else {
  console.log('❌ LicenseStorage not found in window');
}

// Check for any error messages in console
console.log('\n🚨 Checking for recent errors...');
console.log('Check the Console tab for any red error messages');

// Check DOM for loading indicators
const loadingElements = document.querySelectorAll('[style*="spin"], .loading, [class*="loading"]');
console.log('\n🔄 Loading Elements Found:', loadingElements.length);
loadingElements.forEach((el, i) => {
  console.log(`Loading Element ${i + 1}:`, el.textContent?.trim() || el.outerHTML.substring(0, 100));
});

// Check for React Fiber (if available)
const reactRoot = document.querySelector('#root');
if (reactRoot && reactRoot._reactInternalFiber) {
  console.log('\n⚛️ React Fiber detected - checking component state');
} else if (reactRoot && reactRoot._reactInternalInstance) {
  console.log('\n⚛️ React instance detected - checking component state');
} else {
  console.log('\n❌ React instance not found on root element');
}

console.log('\n✅ Diagnostic complete. Check the output above for issues.');
console.log('💡 If UI is stuck, look for:');
console.log('   - isValidatingStoredLicense: true');
console.log('   - isAutoLoginInProgress: true');
console.log('   - Connection errors');
console.log('   - JavaScript errors in console');
