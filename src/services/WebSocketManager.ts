/**
 * WebSocket Manager for Kasper-Q Frontend Communication
 * 
 * This module provides a standardized WebSocket communication layer for the React frontend.
 * It handles connection management, message transformation, retry logic, and type-safe
 * communication with the Python backend.
 * 
 * Key Features:
 * - Type-safe message handling with TypeScript interfaces
 * - Automatic retry logic with exponential backoff
 * - Message transformation pipeline with hooks
 * - Request/response correlation tracking
 * - Connection state management
 * - Rate limiting and message queuing
 * - Debug logging and message tracing
 * 
 * Author: Kasper-Q Development Team
 * Created: 2024
 * Version: 1.0
 */

import {
  BaseMessage,
  RequestMessage,
  ResponseMessage,
  BroadcastMessage,
  MessageType,
  ErrorCode,
  ErrorDetails,
  CommandName,
  Commands,
  MessageTransformHook,
  RateLimitConfig,
  createRequestMessage,
  createMessageMetadata,
  validateMessageSchema,
  isRequestMessage,
  isResponseMessage,
  isBroadcastMessage
} from '../shared/messageSchemas';

// Connection states
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// Connection configuration
export interface ConnectionConfig {
  url: string;
  protocols?: string[];
  timeout: number;
  maxRetries: number;
  retryDelay: number;
  maxRetryDelay: number;
  backoffFactor: number;
  jitterFactor: number;
}

// Message handler type
export type MessageHandler = (message: BroadcastMessage) => void | Promise<void>;

// Connection state callback type
export type ConnectionStateCallback = (state: ConnectionState, metadata?: any) => void;

// Default configuration
const DEFAULT_CONFIG: ConnectionConfig = {
  url: 'ws://localhost:8765',
  timeout: 10000,
  maxRetries: Infinity,
  retryDelay: 1000,
  maxRetryDelay: 30000,
  backoffFactor: 2,
  jitterFactor: 0.1
};

/**
 * WebSocket Manager class for standardized communication
 */
export class WebSocketManager {
  private socket: WebSocket | null = null;
  private config: ConnectionConfig;
  private state: ConnectionState = ConnectionState.DISCONNECTED;
  private retryCount = 0;
  private retryTimeout: NodeJS.Timeout | null = null;
  
  // Message handling
  private messageHandlers = new Map<MessageType, Set<MessageHandler>>();
  private pendingRequests = new Map<string, {
    resolve: (response: ResponseMessage) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  
  // Connection state callbacks
  private stateCallbacks = new Set<ConnectionStateCallback>();
  
  // Message transformation
  private transformHooks: MessageTransformHook[] = [];
  
  // Message queue for offline messages
  private messageQueue: RequestMessage[] = [];
  private maxQueueSize = 100;
  
  // Rate limiting
  private rateLimitConfig: RateLimitConfig = {
    maxMessages: 100,
    windowMs: 60000,
    enabled: true
  };
  private messageWindow: number[] = [];
  
  // Statistics
  private stats = {
    messagesSent: 0,
    messagesReceived: 0,
    errorsEncountered: 0,
    reconnectAttempts: 0
  };
  
  // Debug mode
  private debug = false;

  constructor(config: Partial<ConnectionConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.setupEventHandlers();
  }

  /**
   * Connect to the WebSocket server
   */
  async connect(): Promise<void> {
    if (this.state === ConnectionState.CONNECTING || this.state === ConnectionState.CONNECTED) {
      return;
    }

    this.setState(ConnectionState.CONNECTING);
    this.clearRetryTimeout();

    return new Promise((resolve, reject) => {
      try {
        // Close existing connection
        if (this.socket) {
          this.socket.close();
          this.socket = null;
        }

        // Create new WebSocket connection
        this.socket = new WebSocket(this.config.url, this.config.protocols);
        
        // Set up timeout
        const timeout = setTimeout(() => {
          if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
            this.socket.close();
            this.setState(ConnectionState.ERROR, { error: 'Connection timeout' });
            reject(new Error('Connection timeout'));
          }
        }, this.config.timeout);

        // Connection opened
        this.socket.onopen = () => {
          clearTimeout(timeout);
          this.setState(ConnectionState.CONNECTED);
          this.retryCount = 0;
          this.processMessageQueue();
          this.log('Connected to WebSocket server');
          resolve();
        };

        // Connection closed
        this.socket.onclose = (event) => {
          clearTimeout(timeout);
          this.socket = null;
          
          if (this.state === ConnectionState.CONNECTED) {
            this.setState(ConnectionState.RECONNECTING);
            this.scheduleReconnect();
          } else {
            this.setState(ConnectionState.DISCONNECTED);
          }
          
          this.log(`Connection closed: ${event.code} - ${event.reason}`);
        };

        // Connection error
        this.socket.onerror = (error) => {
          clearTimeout(timeout);
          this.stats.errorsEncountered++;
          this.setState(ConnectionState.ERROR, { error });
          this.log('WebSocket error:', error);
          
          if (this.state === ConnectionState.CONNECTING) {
            reject(new Error('Connection failed'));
          }
        };

        // Message received
        this.socket.onmessage = (event) => {
          this.handleIncomingMessage(event.data);
        };

      } catch (error) {
        this.setState(ConnectionState.ERROR, { error });
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    this.clearRetryTimeout();
    
    if (this.socket) {
      this.socket.close(1000, 'Client disconnect');
      this.socket = null;
    }
    
    this.setState(ConnectionState.DISCONNECTED);
    this.clearPendingRequests();
  }

  /**
   * Send a command request to the server
   */
  async sendCommand<T = any>(
    command: CommandName,
    params: Record<string, any> = {},
    timeout = 30000
  ): Promise<T> {
    const request = createRequestMessage(command, params);
    
    return new Promise((resolve, reject) => {
      // Check rate limiting
      if (!this.checkRateLimit()) {
        reject(new Error('Rate limit exceeded'));
        return;
      }

      // If not connected, queue the message
      if (this.state !== ConnectionState.CONNECTED) {
        if (this.messageQueue.length >= this.maxQueueSize) {
          reject(new Error('Message queue full'));
          return;
        }
        
        this.messageQueue.push(request);
        this.log(`Queued message: ${command}`);
        
        // If reconnecting, the message will be sent when connection is restored
        if (this.state === ConnectionState.RECONNECTING) {
          // Set up pending request for when connection is restored
          const timeoutHandle = setTimeout(() => {
            this.pendingRequests.delete(request.payload.requestId);
            reject(new Error('Request timeout'));
          }, timeout);

          this.pendingRequests.set(request.payload.requestId, {
            resolve: (response) => resolve(response.payload.data),
            reject,
            timeout: timeoutHandle
          });
          return;
        }
        
        reject(new Error('Not connected to server'));
        return;
      }

      // Set up timeout
      const timeoutHandle = setTimeout(() => {
        this.pendingRequests.delete(request.payload.requestId);
        reject(new Error('Request timeout'));
      }, timeout);

      // Store pending request
      this.pendingRequests.set(request.payload.requestId, {
        resolve: (response) => resolve(response.payload.data),
        reject,
        timeout: timeoutHandle
      });

      // Send the message
      this.sendMessage(request).catch(reject);
    });
  }

  /**
   * Register a handler for broadcast messages
   */
  registerHandler(messageType: MessageType, handler: MessageHandler): void {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, new Set());
    }
    this.messageHandlers.get(messageType)!.add(handler);
    this.log(`Registered handler for ${messageType}`);
  }

  /**
   * Unregister a message handler
   */
  unregisterHandler(messageType: MessageType, handler?: MessageHandler): void {
    if (handler) {
      this.messageHandlers.get(messageType)?.delete(handler);
    } else {
      this.messageHandlers.delete(messageType);
    }
    this.log(`Unregistered handler for ${messageType}`);
  }

  /**
   * Register a connection state callback
   */
  onStateChange(callback: ConnectionStateCallback): () => void {
    this.stateCallbacks.add(callback);
    return () => this.stateCallbacks.delete(callback);
  }

  /**
   * Add a message transformation hook
   */
  addTransformHook(hook: MessageTransformHook): void {
    this.transformHooks.push(hook);
    this.transformHooks.sort((a, b) => b.priority - a.priority);
    this.log(`Added transform hook: ${hook.name}`);
  }

  /**
   * Get current connection state
   */
  getState(): ConnectionState {
    return this.state;
  }

  /**
   * Get connection statistics
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * Enable/disable debug logging
   */
  setDebug(enabled: boolean): void {
    this.debug = enabled;
  }

  // Private methods

  private async sendMessage(message: BaseMessage): Promise<void> {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not ready');
    }

    try {
      // Apply encoding transformations
      let transformedMessage = message;
      for (const hook of this.transformHooks) {
        if (hook.encode) {
          transformedMessage = await hook.encode(transformedMessage);
        }
      }

      // Serialize and send
      const serialized = JSON.stringify(transformedMessage);
      this.socket.send(serialized);
      this.stats.messagesSent++;
      this.log(`Sent message: ${message.type}`);

    } catch (error) {
      this.stats.errorsEncountered++;
      throw error;
    }
  }

  private async handleIncomingMessage(data: string): Promise<void> {
    try {
      this.stats.messagesReceived++;
      
      // Parse JSON
      const parsed = JSON.parse(data);
      
      // Validate schema
      if (!validateMessageSchema(parsed)) {
        this.log('Invalid message schema received');
        return;
      }

      let message = parsed as BaseMessage;

      // Apply decoding transformations
      for (const hook of [...this.transformHooks].reverse()) {
        if (hook.decode) {
          message = await hook.decode(message);
        }
      }

      this.log(`Received message: ${message.type}`);

      // Handle response messages
      if (isResponseMessage(message)) {
        this.handleResponse(message);
        return;
      }

      // Handle broadcast messages
      if (isBroadcastMessage(message)) {
        this.handleBroadcast(message);
        return;
      }

    } catch (error) {
      this.stats.errorsEncountered++;
      this.log('Error handling incoming message:', error);
    }
  }

  private handleResponse(response: ResponseMessage): void {
    const requestId = response.payload.requestId;
    const pending = this.pendingRequests.get(requestId);
    
    if (pending) {
      clearTimeout(pending.timeout);
      this.pendingRequests.delete(requestId);
      
      if (response.payload.success) {
        pending.resolve(response);
      } else {
        const error = new Error(response.error?.message || 'Request failed');
        pending.reject(error);
      }
    }
  }

  private handleBroadcast(message: BroadcastMessage): void {
    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          this.log(`Error in message handler for ${message.type}:`, error);
        }
      });
    }
  }

  private setState(state: ConnectionState, metadata?: any): void {
    if (this.state !== state) {
      this.state = state;
      this.stateCallbacks.forEach(callback => {
        try {
          callback(state, metadata);
        } catch (error) {
          this.log('Error in state callback:', error);
        }
      });
    }
  }

  private scheduleReconnect(): void {
    if (this.retryCount >= this.config.maxRetries) {
      this.setState(ConnectionState.ERROR, { error: 'Max retries exceeded' });
      return;
    }

    const delay = Math.min(
      this.config.retryDelay * Math.pow(this.config.backoffFactor, this.retryCount),
      this.config.maxRetryDelay
    );

    // Add jitter
    const jitter = delay * this.config.jitterFactor * (Math.random() - 0.5);
    const finalDelay = delay + jitter;

    this.retryTimeout = setTimeout(() => {
      this.retryCount++;
      this.stats.reconnectAttempts++;
      this.log(`Reconnect attempt ${this.retryCount}`);
      this.connect().catch(() => {
        // Will schedule another reconnect if needed
      });
    }, finalDelay);
  }

  private clearRetryTimeout(): void {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
  }

  private processMessageQueue(): void {
    const queue = [...this.messageQueue];
    this.messageQueue = [];
    
    queue.forEach(message => {
      this.sendMessage(message).catch(error => {
        this.log('Error sending queued message:', error);
      });
    });
  }

  private clearPendingRequests(): void {
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('Connection closed'));
    });
    this.pendingRequests.clear();
  }

  private checkRateLimit(): boolean {
    if (!this.rateLimitConfig.enabled) {
      return true;
    }

    const now = Date.now();
    const windowStart = now - this.rateLimitConfig.windowMs;
    
    // Remove old entries
    this.messageWindow = this.messageWindow.filter(time => time > windowStart);
    
    // Check limit
    if (this.messageWindow.length >= this.rateLimitConfig.maxMessages) {
      return false;
    }
    
    this.messageWindow.push(now);
    return true;
  }

  private setupEventHandlers(): void {
    // Handle page visibility changes
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible' && this.state === ConnectionState.DISCONNECTED) {
          this.connect().catch(() => {
            // Reconnection will be handled by retry logic
          });
        }
      });
    }

    // Handle online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        if (this.state === ConnectionState.DISCONNECTED) {
          this.connect().catch(() => {
            // Reconnection will be handled by retry logic
          });
        }
      });
    }
  }

  private log(message: string, ...args: any[]): void {
    if (this.debug) {
      console.log(`[WebSocketManager] ${message}`, ...args);
    }
  }

  /**
   * Configure rate limiting
   */
  setRateLimit(config: Partial<RateLimitConfig>): void {
    this.rateLimitConfig = { ...this.rateLimitConfig, ...config };
  }

  /**
   * Clear message queue
   */
  clearMessageQueue(): void {
    this.messageQueue = [];
  }

  /**
   * Get queued message count
   */
  getQueuedMessageCount(): number {
    return this.messageQueue.length;
  }

  /**
   * Force reconnect (useful for testing)
   */
  forceReconnect(): void {
    if (this.socket) {
      this.socket.close();
    }
    this.connect().catch(() => {
      // Reconnection will be handled by retry logic
    });
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.state === ConnectionState.CONNECTED;
  }

  /**
   * Check if connecting
   */
  isConnecting(): boolean {
    return this.state === ConnectionState.CONNECTING;
  }

  /**
   * Check if reconnecting
   */
  isReconnecting(): boolean {
    return this.state === ConnectionState.RECONNECTING;
  }

  /**
   * Get pending request count
   */
  getPendingRequestCount(): number {
    return this.pendingRequests.size;
  }
}
