import React from 'react';
import Card from './Card';
import './StatusBar.css';

const StatusBar = ({ isConnected }) => {
  const [time, setTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <Card style={{ padding: '16px 24px', borderRadius: 12, marginBottom: 0 }}>
      <div className="kasper-statusbar">
        <span className={`kasper-statusbar__connection ${isConnected ? 'online' : 'offline'}`}>{isConnected ? 'Connected' : 'Disconnected'}</span>
        <span className="kasper-statusbar__system">System Ready</span>
        <span className="kasper-statusbar__time">{time.toLocaleTimeString()}</span>
      </div>
    </Card>
  );
};

export default StatusBar;