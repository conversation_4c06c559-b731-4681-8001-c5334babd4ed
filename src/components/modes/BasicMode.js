import React, { useState } from 'react';
import { useAppContext } from '../../context/AppContext';
import BackendService from '../../services/BackendService';
import './BasicMode.css';

const BasicMode = () => {
  const { addQueueTask } = useAppContext();
  const [eventUrl, setEventUrl] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [status, setStatus] = useState('idle'); // idle, waiting, running, success, error
  const [statusMessage, setStatusMessage] = useState('Enter a Queue-IT event URL to begin');

  // Handle quantity change
  const handleDecreaseQuantity = () => {
    if (quantity > 1) setQuantity(quantity - 1);
  };

  const handleIncreaseQuantity = () => {
    if (quantity < 10) setQuantity(quantity + 1);
  };

  // Start queue entry
  const handleStartQueueEntry = async () => {
    if (!eventUrl) {
      setStatus('error');
      setStatusMessage('Please enter a valid Queue-IT event URL');
      return;
    }

    try {
      setStatus('waiting');
      setStatusMessage('Initializing queue entry...');

      // Create a new task
      const task = {
        eventUrl,
        quantity,
        timestamp: new Date().toISOString()
      };

      // Add to context
      addQueueTask(task);

      // Send to backend
      const response = await BackendService.startQueueEntry(task);

      // Handle backend response format: { type: 'queue_entry_started' | 'error', ... }
      if (response.type === 'queue_entry_started') {
        setStatus('running');
        setStatusMessage(`Queue entry started. Task ID: ${response.task_id}, Tasks: ${response.task_count}`);
      } else if (response.type === 'error') {
        setStatus('error');
        setStatusMessage(`Error: ${response.error}`);
      } else {
        setStatus('error');
        setStatusMessage(`Unexpected response format: ${JSON.stringify(response)}`);
      }
    } catch (error) {
      setStatus('error');
      setStatusMessage(`Failed to start queue entry: ${error.message}`);
    }
  };

  return (
    <div className="basic-mode-container">
      <div className="queue-entry-card">
        <h2 className="card-title">Queue Entry</h2>

        <div className="input-group">
          <label htmlFor="event-url">Event URL</label>
          <input
            type="text"
            id="event-url"
            className="neo-input"
            placeholder="https://queue-it.net/..."
            value={eventUrl}
            onChange={(e) => setEventUrl(e.target.value)}
          />
        </div>

        <div className="input-group">
          <label htmlFor="ticket-quantity">Ticket Quantity</label>
          <div className="quantity-control">
            <button
              className="quantity-btn decrease"
              onClick={handleDecreaseQuantity}
              disabled={quantity <= 1}
            >
              -
            </button>
            <span className="quantity-display">{quantity}</span>
            <button
              className="quantity-btn increase"
              onClick={handleIncreaseQuantity}
              disabled={quantity >= 10}
            >
              +
            </button>
          </div>
        </div>

        <button
          className="start-button"
          onClick={handleStartQueueEntry}
          disabled={status === 'waiting' || status === 'running'}
        >
          <span className="start-icon">▶</span>
          <span className="button-text">START QUEUE ENTRY</span>
        </button>
      </div>

      <div className="status-panel">
        <div className="status-header">
          <h3>Status</h3>
          <span className={`status-indicator ${status}`}>
            {status === 'idle' && 'Waiting for input'}
            {status === 'waiting' && 'Initializing...'}
            {status === 'running' && 'Running'}
            {status === 'success' && 'Success'}
            {status === 'error' && 'Error'}
          </span>
        </div>
        <div className="status-content">
          <div className={`ghost-loader ${status === 'running' ? 'animated' : ''}`}></div>
          <p className="status-message">{statusMessage}</p>
        </div>
      </div>
    </div>
  );
};

export default BasicMode;