.monitoring-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 40px);
  margin-top: 40px;
  position: relative;
  overflow: hidden;
  background: #0B0A12;
}

.monitoring-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    800px circle at var(--mouse-x) var(--mouse-y),
    rgba(124, 77, 255, 0.05),
    transparent 40%
  );
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.monitoring-layout:hover::before {
  opacity: 1;
}

.card-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid rgba(124, 77, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 1200px;
  background: rgba(11, 10, 18, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
}

.card-section:not(:last-child) {
  border-bottom: 1px solid rgba(124, 77, 255, 0.1);
  padding-bottom: 1rem;
}


.progress-container {
  padding: 1.5rem 2rem 0.5rem;
  border-radius: 12px;
  margin-bottom: 0;
}

.progress-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1000px;
  position: relative;
  overflow: visible;
}

.progress-stages {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  padding: 0 2rem;
  justify-content: center;
  overflow: visible;
}

.progress-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 0 1 auto;
  transition: all 0.3s ease;
  min-width: 80px;
  overflow: visible;
}

.progress-stage-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(138, 0, 238, 0.3);
  border-radius: 50%;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  color: rgba(255, 255, 255, 0.5);
  background: rgba(11, 10, 18, 0.95);
  transform-origin: center;
  margin: 10px 0;
}

.progress-stage.active .progress-stage-icon {
  border-color: rgba(138, 0, 238, 0.8);
  color: #fff;
  box-shadow: 0 0 20px rgba(138, 0, 238, 0.3);
  transform: scale(1.1);
  z-index: 3;
}

.progress-stage.completed .progress-stage-icon {
  border-color: rgba(138, 0, 238, 0.8);
  color: rgba(138, 0, 238, 1);
  box-shadow: 0 0 20px rgba(138, 0, 238, 0.2);
}

.progress-stage-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
  transition: all 0.3s ease;
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  font-weight: 500;
  width: 100%;
}

.progress-stage.active .progress-stage-label {
  color: #fff;
}

.progress-stage.completed .progress-stage-label {
  color: rgba(138, 0, 238, 0.8);
}

.progress-connector {
  position: absolute;
  top: 30px;
  left: calc(-50% + 20px);
  right: calc(50% + 20px);
  height: 2px;
  background: rgba(138, 0, 238, 0.2);
  transition: all 0.3s ease;
  z-index: 1;
  transform: translateZ(0);
}

.progress-stage:first-child .progress-connector {
  display: none;
}

.progress-connector.active {
  background: rgba(138, 0, 238, 0.4);
  box-shadow: 0 0 10px rgba(138, 0, 238, 0.2);
}

.progress-stage-tooltip {
  position: absolute;
  top: -45px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(138, 0, 238, 0.2);
  background: rgba(11, 10, 18, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 20;
  font-weight: 500;
  pointer-events: none;
}

.progress-stage:hover .progress-stage-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
}

.traffic-lights {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  position: relative;
  z-index: 10;
  padding: 0.5rem;
  border-radius: 8px;
  
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: 1px solid rgba(124, 77, 255, 0.2);
  background: rgba(124, 77, 255, 0.1);
  color: #7C4DFF;
  cursor: pointer;
  transition: all 0.18s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-button.start {
  background: rgba(40, 200, 64, 0.1);
  border-color: rgba(40, 200, 64, 0.2);
  color: #28C840;
}

.action-button.start:hover {
  background: rgba(40, 200, 64, 0.2);
  border-color: rgba(40, 200, 64, 0.3);
  color: #28C840;
  transform: scale(1.1);
}

.action-button.stop {
  background: rgba(255, 189, 46, 0.1);
  border-color: rgba(255, 189, 46, 0.2);
  color: #FFBD2E;
}

.action-button.stop:hover {
  background: rgba(255, 189, 46, 0.2);
  border-color: rgba(255, 189, 46, 0.3);
  color: #FFBD2E;
  transform: scale(1.1);
}

.action-button.cancel {
  background: rgba(255, 95, 87, 0.1);
  border-color: rgba(255, 95, 87, 0.2);
  color: #FF5F57;
}

.action-button.cancel:hover {
  background: rgba(255, 95, 87, 0.2);
  border-color: rgba(255, 95, 87, 0.3);
  color: #FF5F57;
  transform: scale(1.1);
}

.action-button svg {
  width: 1.1rem;
  height: 1.1rem;
  transition: transform 0.3s ease;
}

.action-button:hover svg {
  transform: scale(1.1);
}

.status-display {
  padding: 0.5rem 2rem;
  text-align: center;
  background: rgba(138, 0, 238, 0.05);
  border-top: 1px solid rgba(138, 0, 238, 0.1);
  margin-top: 0;
}

.current-stage {
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0.25rem;
}

.stage-subtext {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

.pass-links-section {
  padding-top: 0.5rem;
}

.pass-links-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
}

.pass-links-header h3 {
  margin: 0;
  font-size: 1.1em;
  font-weight: var(--font-header-weight);
  color: var(--color-text);
}

.pass-links-header .close-button {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-btn);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pass-links-header .close-button:hover {
  color: var(--color-text);
  background: var(--color-border);
}

.pass-links-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.pass-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-2);
  width: 100%;
}

.pass-link-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-card);
  padding: var(--spacing-2);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 50px;
}

.pass-link-card:hover {
  border-color: var(--color-accent);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.pass-link-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--spacing-1);
}

.pass-link-id {
  font-family: var(--font-main);
  font-size: 1em;
  font-weight: var(--font-header-weight);
  color: var(--color-text);
  letter-spacing: 0.05em;
}

.copy-button {
  background: var(--color-border);
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-btn);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 28px;
  min-height: 28px;
}

.copy-button:hover {
  background: var(--color-accent);
  color: var(--color-text);
  transform: scale(1.05);
}

.copy-button:active {
  transform: scale(0.95);
}

.pass-link-url {
  color: #fff;
  font-size: 0.85rem;
  word-break: break-all;
}

.pass-link-timestamp {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.pagination button {
  border: 1px solid rgba(138, 0, 238, 0.3);
  color: #fff;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.pagination button:hover:not(:disabled) {
  border-color: rgba(138, 0, 238, 0.6);
}

.pagination button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination span {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.85rem;
}

.monitoring-layout.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--color-accent);
}

.loading-spinner svg {
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
}

.loading-spinner span {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--color-text);
  text-shadow: 0 0 10px rgba(138, 0, 238, 0.3);
}

.monitoring-layout.error {
  justify-content: center;
  align-items: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  max-width: 400px;
  text-align: center;
}

.error-icon {
  width: 48px;
  height: 48px;
  color: #ff3b30;
  margin-bottom: 0.5rem;
}

.error-container h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ff3b30;
  margin: 0;
}

.error-container p {
  color: var(--color-text);
  margin: 0;
  line-height: 1.5;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 59, 48, 0.15);
  border: 1px solid rgba(255, 59, 48, 0.3);
  border-radius: 8px;
  color: #ff3b30;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgba(255, 59, 48, 0.2);
  transform: translateY(-2px);
}

/* Logs Section */
.logs-section {
  flex: 1;
  background: rgba(11, 10, 18, 0.5);
  border: 1px solid rgba(138, 0, 238, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.logs-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(138, 0, 238, 0.2);
  background: rgba(138, 0, 238, 0.05);
}

.logs-icon {
  width: 24px;
  height: 24px;
  color: var(--color-accent);
}

.logs-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
}

.logs-container {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: rgba(138, 0, 238, 0.05);
  border: 1px solid rgba(138, 0, 238, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.status-row:hover {
  background: rgba(138, 0, 238, 0.1);
  transform: translateX(4px);
}

.status-row.clickable {
  cursor: pointer;
}

.status-row.clickable:hover {
  background: rgba(138, 0, 238, 0.15);
  box-shadow: 0 0 15px rgba(138, 0, 238, 0.2);
}

.status-icon {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  color: var(--color-accent);
}

.status-time {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  opacity: 0.7;
}

/* Recap Section */
.recap-section {
  width: 30%;
  min-width: 300px;
}

.recap-card {
  background: rgba(11, 10, 18, 0.5);
  border: 1px solid rgba(138, 0, 238, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recap-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(138, 0, 238, 0.2);
  background: rgba(138, 0, 238, 0.05);
}

.recap-icon {
  width: 24px;
  height: 24px;
  color: var(--color-accent);
}

.recap-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text);
}

.recap-content {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recap-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.recap-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  opacity: 0.7;
}

.recap-value {
  font-size: 1rem;
  color: var(--color-text);
  word-break: break-all;
}

.recap-value.status-active {
  color: #34c759;
  font-weight: 500;
}

.link-part {
  color: var(--color-accent);
  text-decoration: underline;
  cursor: pointer;
}

/* Scrollbar Styles */
.logs-container::-webkit-scrollbar {
  width: 8px;
}

.logs-container::-webkit-scrollbar-track {
  background: rgba(138, 0, 238, 0.05);
  border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb {
  background: rgba(138, 0, 238, 0.2);
  border-radius: 4px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background: rgba(138, 0, 238, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .monitoring-layout {
    flex-direction: column;
    padding: 1rem;
  }

  .recap-section {
    width: 100%;
    min-width: unset;
  }

  .recap-card {
    height: auto;
  }
}

@media (max-width: 768px) {
  .monitoring-layout {
    padding: 0.5rem;
  }

  .logs-header,
  .recap-header {
    padding: 0.75rem 1rem;
  }

  .logs-container,
  .recap-content {
    padding: 0.75rem;
  }

  .status-row {
    padding: 0.5rem 0.75rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
} 

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Progress Section */
.progress-section {
  width: 100%;
  max-width: 800px;
  background: rgba(11, 10, 18, 0.4);  /* More transparent background */
  backdrop-filter: blur(12px);        /* Increased blur for better glass effect */
  -webkit-backdrop-filter: blur(12px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),    /* Softer shadow */
    0 0 0 1px rgba(124, 77, 255, 0.1); /* Subtle border glow */
  animation: fadeIn 0.5s ease-out;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(124, 77, 255, 0.1); /* More subtle border */
}

.progress-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(124, 77, 255, 0.05),  /* Very subtle gradient */
    rgba(124, 77, 255, 0.02)
  );
  z-index: 0;
  pointer-events: none;
  border-radius: 16px;
}

.progress-section.expanded {
  height: 100%;
  justify-content: flex-start;
}

.progress-section.expanded {
  height: 100%;
  justify-content: flex-start;
}

/* Potion Container */
.potion-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  position: relative;
}

/* Flask Container */
.potion-flask {
  --flask-height: 24px;
  --cap-radius: calc(var(--flask-height) / 2);
  --bulge-width: 60%;
  --bulge-height: var(--flask-height);
  --flask-width: 600px;
  
  width: var(--flask-width);
  height: var(--flask-height);
  position: relative;
  display: flex;
  align-items: center;
  filter: drop-shadow(0 0 20px rgba(138, 0, 238, 0.3));
}

/* Flask Body */
.potion-body {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(138, 0, 238, 0.4);
  position: relative;
  overflow: hidden;
  clip-path: path('M 0,12 C 0,5.4 5.4,0 12,0 L 88,0 C 94.6,0 100,5.4 100,12 L 100,88 C 100,94.6 94.6,100 88,100 L 12,100 C 5.4,100 0,94.6 0,88 Z');
  box-shadow: 
    inset 0 0 30px rgba(138, 0, 238, 0.2),
    0 0 20px rgba(138, 0, 238, 0.2);
}

/* Flask Liquid */
.potion-liquid {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    rgba(138, 0, 238, 0.8),
    rgba(138, 0, 238, 0.4)
  );
  transition: width 0.5s ease-in-out;
  overflow: hidden;
  z-index: 2;
  clip-path: path('M 0,12 C 0,5.4 5.4,0 12,0 L 88,0 C 94.6,0 100,5.4 100,12 L 100,88 C 100,94.6 94.6,100 88,100 L 12,100 C 5.4,100 0,94.6 0,88 Z');
}

/* Glossy Effect */
.potion-liquid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );
  animation: shimmer 2s infinite;
}

/* Segment Dividers */
.potion-segment {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background: rgba(138, 0, 238, 0.3);
  z-index: 3;
}

.potion-segment::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg,
    rgba(138, 0, 238, 0.4) 0%,
    rgba(138, 0, 238, 0.2) 50%,
    rgba(138, 0, 238, 0.4) 100%
  );
  clip-path: path('M 0,12 C 0,5.4 5.4,0 12,0 L 88,0 C 94.6,0 100,5.4 100,12 L 100,88 C 100,94.6 94.6,100 88,100 L 12,100 C 5.4,100 0,94.6 0,88 Z');
}

/* Stage Indicators */
.potion-stages {
  position: absolute;
  top: -30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  pointer-events: none;
  z-index: 3;
}

.potion-stage {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(138, 0, 238, 0.4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  pointer-events: auto;
  cursor: help;
  box-shadow: 
    0 0 15px rgba(138, 0, 238, 0.2),
    inset 0 0 10px rgba(138, 0, 238, 0.1);
}

.potion-stage::before {
  content: attr(data-tooltip);
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  font-size: 0.9rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  box-shadow: 0 0 20px rgba(138, 0, 238, 0.3);
}

.potion-stage:hover::before {
  opacity: 1;
  visibility: visible;
}

.potion-stage.active {
  background: rgba(138, 0, 238, 0.4);
  box-shadow: 
    0 0 25px rgba(138, 0, 238, 0.5),
    inset 0 0 15px rgba(138, 0, 238, 0.3);
  transform: scale(1.15);
  border-color: rgba(138, 0, 238, 0.6);
}

.potion-stage.completed {
  background: rgba(138, 0, 238, 0.3);
  box-shadow: 
    0 0 15px rgba(138, 0, 238, 0.4),
    inset 0 0 10px rgba(138, 0, 238, 0.2);
  border-color: rgba(138, 0, 238, 0.5);
}

.stage-icon {
  font-size: 1rem;
  filter: drop-shadow(0 0 8px rgba(138, 0, 238, 0.6));
}

/* Status Pills */
.status-pill {
  padding: 0.75rem 1.5rem;
  background: rgba(11, 10, 18, 0.6);
  border: 1px solid rgba(138, 0, 238, 0.3);
  border-radius: 20px;
  color: white;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.2),
    0 0 15px rgba(138, 0, 238, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-pill:hover {
  background: rgba(11, 10, 18, 0.7);
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.3),
    0 0 20px rgba(138, 0, 238, 0.2);
  transform: translateY(-1px);
}

.ticket-counter {
  left: 0;
}

.last-update {
  right: 0;
}

/* Animations */
@keyframes shimmer {
  0% {
    opacity: 0.5;
    transform: translateX(-100%);
  }
  100% {
    opacity: 0.8;
    transform: translateX(100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .potion-flask {
    --flask-width: 100%;
    --flask-height: 20px;
  }

  .potion-stage {
    width: 20px;
    height: 20px;
  }

  .stage-icon {
    font-size: 0.9rem;
  }

  .status-pill {
    font-size: 0.85rem;
  }
}

/* Status Display */
.status-display {
  background: none;
  border: none;
  box-shadow: none;
  text-align: center;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.current-stage {
  font-size: 1rem;
  color: #fff;
  margin-bottom: 0.25rem;
}

.stage-subtext {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Event Details */
.event-details {
  width: 300px;
  background: rgba(11, 10, 18, 0.4);
  border: 1px solid rgba(138, 0, 238, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  padding: 1.5rem;
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.2),
    0 0 15px rgba(138, 0, 238, 0.1);
}

.event-details h3 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  color: #fff;
  text-shadow: 
    0 0 10px rgba(138, 0, 238, 0.5),
    0 0 20px rgba(138, 0, 238, 0.3);
  letter-spacing: 0.5px;
}

.detail-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.detail-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 0.9rem;
  color: #fff;
  word-break: break-all;
  letter-spacing: 0.5px;
}

.detail-value.url {
  color: rgba(138, 0, 238, 0.8);
  text-decoration: underline;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-value.url:hover {
  color: rgba(138, 0, 238, 1);
  text-shadow: 0 0 10px rgba(138, 0, 238, 0.5);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    filter: blur(0px);
  }
  25% {
    transform: translate(100px, 50px) rotate(90deg) scale(1.2);
    filter: blur(2px);
  }
  50% {
    transform: translate(50px, 100px) rotate(180deg) scale(0.8);
    filter: blur(0px);
  }
  75% {
    transform: translate(-50px, 50px) rotate(270deg) scale(1.1);
    filter: blur(1px);
  }
  100% {
    transform: translate(0, 0) rotate(360deg) scale(1);
    filter: blur(0px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 
      0 0 0 1px rgba(138, 0, 238, 0.4),
      0 0 20px rgba(138, 0, 238, 0.5),
      inset 0 0 15px rgba(138, 0, 238, 0.3);
  }
  50% {
    box-shadow: 
      0 0 0 1px rgba(138, 0, 238, 0.4),
      0 0 30px rgba(138, 0, 238, 0.6),
      inset 0 0 20px rgba(138, 0, 238, 0.4);
  }
  100% {
    box-shadow: 
      0 0 0 1px rgba(138, 0, 238, 0.4),
      0 0 20px rgba(138, 0, 238, 0.5),
      inset 0 0 15px rgba(138, 0, 238, 0.3);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .monitoring-layout {
    flex-direction: column;
    padding: 1rem;
  }

  .event-details {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .progress-dot {
    width: 20px;
    height: 20px;
  }

  .dot-icon {
    font-size: 0.9rem;
  }

  .status-pill {
    font-size: 0.85rem;
  }
}

.progress-container {
  position: relative;
  width: 100%;
  padding: 2rem 0;
}

.progress-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(138, 0, 238, 0.8),
    rgba(138, 0, 238, 0.4)
  );
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 0 20px rgba(138, 0, 238, 0.4),
    inset 0 0 10px rgba(138, 0, 238, 0.3);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );
  animation: shimmer 2s infinite;
}

.progress-dots {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  transform: translateY(-50%);
  padding: 0 2rem;
}

.progress-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.5);
}

.progress-dot.active {
  background: rgba(138, 0, 238, 0.2);
  border-color: rgba(138, 0, 238, 0.4);
  color: #7C4DFF;
  box-shadow: 
    0 0 20px rgba(138, 0, 238, 0.3),
    0 0 40px rgba(138, 0, 238, 0.2);
  animation: pulse 2s infinite;
}

.progress-dot.completed {
  background: rgba(138, 0, 238, 0.1);
  border-color: rgba(138, 0, 238, 0.3);
  color: #7C4DFF;
}

.progress-dot svg {
  width: 24px;
  height: 24px;
  transition: all 0.3s ease;
}

.progress-dot:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 0, 238, 0.2);
}

.progress-dot:hover svg {
  transform: scale(1.1);
}

.progress-dot::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  padding: 6px 12px;
  background: rgba(11, 10, 18, 0.95);
  color: #fff;
  font-size: 0.875rem;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 10;
}

.progress-dot:hover::before {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-12px);
}

@keyframes pulse {
  0% {
    box-shadow: 
      0 0 20px rgba(138, 0, 238, 0.3),
      0 0 40px rgba(138, 0, 238, 0.2);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(138, 0, 238, 0.4),
      0 0 60px rgba(138, 0, 238, 0.3);
  }
  100% {
    box-shadow: 
      0 0 20px rgba(138, 0, 238, 0.3),
      0 0 40px rgba(138, 0, 238, 0.2);
  }
}

@keyframes shimmer {
  0% {
    opacity: 0.5;
    transform: translateX(-100%);
  }
  100% {
    opacity: 0.8;
    transform: translateX(100%);
  }
}

@media (max-width: 768px) {
  .progress-container {
    padding: 2rem 0;
  }

  .progress-bar {
    height: 4px;
  }

  .progress-dot {
    width: 20px;
    height: 20px;
  }

  .dot-icon {
    font-size: 0.9rem;
  }
}

.pass-links-section {
  width: 100%;
  margin-top: 0;
  background: rgba(11, 10, 18, 0.6);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(138, 0, 238, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  flex: 1;
}

.progress-section.expanded .pass-links-section {
  margin-top: 0;
  height: calc(100% - 200px);
}

.pass-links-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(
    90deg,
    rgba(138, 0, 238, 0.1),
    rgba(138, 0, 238, 0.05)
  );
  border-bottom: 1px solid rgba(138, 0, 238, 0.2);
  position: relative;
  z-index: 1000;
}

.pass-links-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(138, 0, 238, 0.1),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pass-links-header:hover::before {
  opacity: 1;
}

.pass-links-header h3 {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 
    0 0 10px rgba(138, 0, 238, 0.3),
    0 0 20px rgba(138, 0, 238, 0.2);
  letter-spacing: 0.5px;
  opacity: 0.9;
}

.expand-button {
  background: rgba(138, 0, 238, 0.15);
  border: 1px solid rgba(138, 0, 238, 0.3);
  color: #fff;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.expand-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(138, 0, 238, 0.2),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.expand-button:hover {
  background: rgba(138, 0, 238, 0.25);
  transform: translateY(-2px);
  box-shadow: 
    0 4px 12px rgba(138, 0, 238, 0.2),
    0 0 0 1px rgba(138, 0, 238, 0.4);
}

.expand-button:hover::before {
  transform: translateX(100%);
}

.pass-links-container {
  padding: 1.5rem;
  height: calc(100% - 70px);
  overflow-y: auto;
  background: rgba(11, 10, 18, 0.4);
  min-height: 0; /* Important for flex child scrolling */
}

.pass-links-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  height: 100%;
}

.pass-link-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-card);
  padding: var(--spacing-3);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pass-link-card:hover {
  border-color: var(--color-accent);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.pass-link-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--spacing-2);
}

.pass-link-id {
  font-family: var(--font-main);
  font-size: 1.2em;
  font-weight: var(--font-header-weight);
  color: var(--color-text);
  letter-spacing: 0.05em;
}

.copy-button {
  background: var(--color-border);
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-btn);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 36px;
  min-height: 36px;
}

.copy-button:hover {
  background: var(--color-accent);
  color: var(--color-text);
  transform: scale(1.05);
}

.copy-button:active {
  transform: scale(0.95);
}

.pass-link-url {
  color: #fff;
  font-size: 0.85rem;
  word-break: break-all;
}

.pass-link-timestamp {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.75rem;
}

.pagination button {
  border: 1px solid rgba(138, 0, 238, 0.3);
  color: #fff;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
}

.pagination button:hover:not(:disabled) {
  border-color: rgba(138, 0, 238, 0.6);
}

.pagination button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination span {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.85rem;
}

/* Scrollbar Styles */
.pass-links-container::-webkit-scrollbar {
  width: 8px;
}

.pass-links-container::-webkit-scrollbar-track {
  background: rgba(11, 10, 18, 0.4);
  border-radius: 4px;
}

.pass-links-container::-webkit-scrollbar-thumb {
  background: rgba(138, 0, 238, 0.3);
  border-radius: 4px;
  border: 2px solid rgba(11, 10, 18, 0.4);
}

.pass-links-container::-webkit-scrollbar-thumb:hover {
  background: rgba(138, 0, 238, 0.4);
}

.progress-section.expanded .pass-links-section {
  margin-top: 0;
  height: calc(100% - 200px);
}

.progress-section.expanded .action-buttons {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  background: rgba(11, 10, 18, 0.8);
  padding: 1rem 2rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(138, 0, 238, 0.1);
}

.pass-links-view {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgba(11, 10, 18, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;
}

.pass-links-view .pass-links-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 1.5rem 2rem;
  background: linear-gradient(
    90deg,
    rgba(138, 0, 238, 0.1),
    rgba(138, 0, 238, 0.05)
  );
  border-bottom: 1px solid rgba(138, 0, 238, 0.2);
  flex-shrink: 0;
}

.back-button {
  background: rgba(138, 0, 238, 0.15);
  border: 1px solid rgba(138, 0, 238, 0.3);
  color: #fff;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  z-index: 1000;
  pointer-events: auto;
}

.back-button:hover {
  background: rgba(138, 0, 238, 0.25);
  transform: translateY(-2px);
  box-shadow: 
    0 4px 12px rgba(138, 0, 238, 0.2),
    0 0 0 1px rgba(138, 0, 238, 0.4);
}

.back-button:active {
  transform: translateY(0);
  background: rgba(138, 0, 238, 0.3);
}

.pass-links-view .pass-links-container {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: rgba(11, 10, 18, 0.4);
  min-height: 0; /* Important for flex child scrolling */
}

.pass-links-view .pass-links-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(4, 1fr);
  padding-bottom: 2rem;
  max-width: 1800px;
  margin: 0 auto;
  height: fit-content;
}

.pass-links-view .pass-link-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  width: 100%;
  aspect-ratio: 16/9;
}

.pass-links-view .pass-link-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(138, 0, 238, 0.2);
}

.pass-links-view .pass-link-url {
  font-size: 1rem;
  line-height: 1.6;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.pass-links-view .pass-link-time {
  font-size: 0.9rem;
  opacity: 0.8;
}

.pass-links-view .copy-button {
  background: rgba(138, 0, 238, 0.15);
  border: 1px solid rgba(138, 0, 238, 0.3);
  color: #fff;
  padding: 1rem;
  border-radius: 8px;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.pass-links-view .copy-button:hover {
  background: rgba(138, 0, 238, 0.25);
  transform: translateY(-2px);
  box-shadow: 
    0 4px 12px rgba(138, 0, 238, 0.2),
    0 0 0 1px rgba(138, 0, 238, 0.4);
}

.pass-links-view .pagination {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(11, 10, 18, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  border-top: 1px solid rgba(138, 0, 238, 0.2);
  margin-top: 2rem;
}

.pass-links-view .pagination button {
  background: rgba(138, 0, 238, 0.15);
  border: 1px solid rgba(138, 0, 238, 0.3);
  color: #fff;
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 8px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pass-links-view .pagination button:not(:disabled):hover {
  background: rgba(138, 0, 238, 0.25);
  transform: translateY(-2px);
  box-shadow: 
    0 4px 12px rgba(138, 0, 238, 0.2),
    0 0 0 1px rgba(138, 0, 238, 0.4);
}

.pass-links-view .pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.pass-links-view .pagination span {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-shadow: 0 0 10px rgba(138, 0, 238, 0.3);
}

/* Scrollbar Styles */
.pass-links-view .pass-links-container::-webkit-scrollbar {
  width: 8px;
}

.pass-links-view .pass-links-container::-webkit-scrollbar-track {
  background: rgba(11, 10, 18, 0.4);
  border-radius: 4px;
}

.pass-links-view .pass-links-container::-webkit-scrollbar-thumb {
  background: rgba(138, 0, 238, 0.3);
  border-radius: 4px;
  border: 2px solid rgba(11, 10, 18, 0.4);
}

.pass-links-view .pass-links-container::-webkit-scrollbar-thumb:hover {
  background: rgba(138, 0, 238, 0.4);
}

@media (max-width: 1800px) {
  .pass-links-view .pass-links-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1400px) {
  .pass-links-view .pass-links-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 900px) {
  .pass-links-view .pass-links-grid {
    grid-template-columns: 1fr;
  }
}

.pass-links-view .progress-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: rgba(11, 10, 18, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(138, 0, 238, 0.2);
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

.pass-links-view .progress-stages {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.pass-links-view .progress-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  flex: 1;
  transition: all 0.3s ease;
}

.pass-links-view .progress-stage-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(138, 0, 238, 0.1);
  border: 1px solid rgba(138, 0, 238, 0.2);
  border-radius: 50%;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.pass-links-view .progress-stage.active .progress-stage-icon {
  background: rgba(138, 0, 238, 0.2);
  border-color: rgba(138, 0, 238, 0.4);
  box-shadow: 
    0 0 20px rgba(138, 0, 238, 0.2),
    0 0 0 2px rgba(138, 0, 238, 0.3);
  transform: scale(1.1);
}

.pass-links-view .progress-stage.completed .progress-stage-icon {
  background: rgba(138, 0, 238, 0.3);
  border-color: rgba(138, 0, 238, 0.5);
  box-shadow: 
    0 0 20px rgba(138, 0, 238, 0.3),
    0 0 0 2px rgba(138, 0, 238, 0.4);
}

.pass-links-view .progress-stage-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  transition: all 0.3s ease;
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.pass-links-view .progress-stage.active .progress-stage-label {
  color: #fff;
  text-shadow: 0 0 10px rgba(138, 0, 238, 0.5);
}

.pass-links-view .progress-stage.completed .progress-stage-label {
  color: rgba(138, 0, 238, 1);
  text-shadow: 0 0 10px rgba(138, 0, 238, 0.3);
}

.pass-links-view .progress-connector {
  position: absolute;
  top: 20px;
  left: calc(-50% + 20px);
  right: calc(50% + 20px);
  height: 2px;
  background: rgba(138, 0, 238, 0.2);
  transition: all 0.3s ease;
  z-index: 1;
}

.pass-links-view .progress-stage:first-child .progress-connector {
  display: none;
}

.pass-links-view .progress-connector.active {
  background: rgba(138, 0, 238, 0.4);
  box-shadow: 0 0 10px rgba(138, 0, 238, 0.2);
}

.pass-links-view .progress-stage-tooltip {
  position: absolute;
  bottom: -45px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(11, 10, 18, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(138, 0, 238, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 20;
}

.pass-links-view .progress-stage:hover .progress-stage-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
}

.pass-links-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 2rem;
  background: rgba(138, 0, 238, 0.05);
  border-top: 1px solid rgba(138, 0, 238, 0.1);
  margin: 0;
}

.pass-links-count {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.link-icon {
  color: rgba(138, 0, 238, 0.7);
  font-size: 1rem;
}

.view-links-button {
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid rgba(138, 0, 238, 0.2);
  border-radius: 6px;
  color: rgba(138, 0, 238, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.view-links-button:hover {
  background: rgba(138, 0, 238, 0.1);
  border-color: rgba(138, 0, 238, 0.4);
  color: rgba(138, 0, 238, 1);
  box-shadow: 0 0 15px rgba(138, 0, 238, 0.2);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--color-card);
  border-radius: var(--radius-card);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow);
  border: 1px solid var(--color-border);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2em;
  font-weight: var(--font-header-weight);
  color: var(--color-text);
}

.modal-close-button {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-btn);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  color: var(--color-text);
  background: var(--color-border);
}

.modal-body {
  padding: var(--spacing-3);
  overflow-y: auto;
  flex: 1;
}

.pass-links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: var(--spacing-3);
  width: 100%;
}

.pass-link-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-card);
  padding: var(--spacing-2);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pass-link-card:hover {
  border-color: var(--color-accent);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.pass-link-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: var(--spacing-2);
}

.pass-link-id {
  font-family: var(--font-main);
  font-size: 1.1em;
  font-weight: var(--font-header-weight);
  color: var(--color-text);
  letter-spacing: 0.05em;
}

.copy-button {
  background: var(--color-border);
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-btn);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 32px;
  min-height: 32px;
}

.copy-button:hover {
  background: var(--color-accent);
  color: var(--color-text);
  transform: scale(1.05);
}

.copy-button:active {
  transform: scale(0.95);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
  padding-top: var(--spacing-3);
  border-top: 1px solid var(--color-border);
}

.pagination button {
  background: var(--color-border);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-btn);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9em;
}

.pagination button:hover:not(:disabled) {
  background: var(--color-accent);
  border-color: var(--color-accent);
}

.pagination button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination span {
  color: var(--color-text-secondary);
  font-size: 0.9em;
}

.progress-stage.failed .progress-stage-icon {
  border-color: rgba(255, 95, 87, 0.8);
  color: #FF5F57;
  box-shadow: 0 0 20px rgba(255, 95, 87, 0.3);
  transform: scale(1.1);
  animation: pulse-error 2s infinite;
}

.progress-connector.failed {
  background: rgba(255, 95, 87, 0.4);
  box-shadow: 0 0 10px rgba(255, 95, 87, 0.2);
}

@keyframes pulse-error {
  0% {
    box-shadow: 
      0 0 0 1px rgba(255, 95, 87, 0.4),
      0 0 20px rgba(255, 95, 87, 0.5),
      inset 0 0 15px rgba(255, 95, 87, 0.3);
  }
  50% {
    box-shadow: 
      0 0 0 1px rgba(255, 95, 87, 0.4),
      0 0 30px rgba(255, 95, 87, 0.6),
      inset 0 0 20px rgba(255, 95, 87, 0.4);
  }
  100% {
    box-shadow: 
      0 0 0 1px rgba(255, 95, 87, 0.4),
      0 0 20px rgba(255, 95, 87, 0.5),
      inset 0 0 15px rgba(255, 95, 87, 0.3);
  }
}
