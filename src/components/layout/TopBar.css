.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(124, 77, 255, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 70px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 2;
}

.logo {
  height: 32px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.menu-button {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-left: 1rem;
  position: relative;
  z-index: 2;
}

.menu-button:hover {
  color: #7C4DFF;
}

.menu-button svg {
  width: 24px;
  height: 24px;
}

/* Remove the transform that was hiding the logo */
.menu-open .logo-container {
  transform: none;
}

/* Adjust the menu button position when menu is open */
.menu-open .menu-button {
  position: fixed;
  right: 2rem;
  top: 1.2rem;
}

/* Ensure the logo stays visible during menu transitions */
.logo-container {
  transition: transform 0.3s ease;
  transform: none !important;
} 