/**
 * Message Transformation Hooks for WebSocket Communication
 * 
 * This module provides extensible transformation hooks for message processing.
 * Hooks can be used for compression, encryption, logging, metrics, and other
 * cross-cutting concerns.
 * 
 * Author: Kasper-Q Development Team
 * Created: 2024
 * Version: 1.0
 */

import {
  BaseMessage,
  MessageTransformHook,
  MessageMetadata,
  createMessageMetadata
} from '../shared/messageSchemas';

/**
 * Compression hook using simple string compression
 */
export class CompressionHook implements MessageTransformHook {
  name = 'compression';
  priority = 100; // High priority for compression

  private compressionThreshold = 1024; // Only compress messages larger than 1KB

  async encode(message: BaseMessage): Promise<BaseMessage> {
    const serialized = JSON.stringify(message.payload);
    
    if (serialized.length > this.compressionThreshold) {
      try {
        // Simple compression using built-in compression (if available)
        const compressed = await this.compress(serialized);
        
        return {
          ...message,
          payload: {
            _compressed: true,
            _originalSize: serialized.length,
            data: compressed
          },
          metadata: {
            ...message.metadata,
            compressed: true,
            originalSize: serialized.length
          }
        };
      } catch (error) {
        console.warn('Compression failed, sending uncompressed:', error);
        return message;
      }
    }
    
    return message;
  }

  async decode(message: BaseMessage): Promise<BaseMessage> {
    if (message.payload._compressed) {
      try {
        const decompressed = await this.decompress(message.payload.data);
        const originalPayload = JSON.parse(decompressed);
        
        return {
          ...message,
          payload: originalPayload,
          metadata: {
            ...message.metadata,
            compressed: false
          }
        };
      } catch (error) {
        console.error('Decompression failed:', error);
        throw new Error('Failed to decompress message');
      }
    }
    
    return message;
  }

  private async compress(data: string): Promise<string> {
    // Simple compression using base64 encoding of compressed data
    // In a real implementation, you might use pako.js or similar
    if (typeof CompressionStream !== 'undefined') {
      const stream = new CompressionStream('gzip');
      const writer = stream.writable.getWriter();
      const reader = stream.readable.getReader();
      
      writer.write(new TextEncoder().encode(data));
      writer.close();
      
      const chunks: Uint8Array[] = [];
      let done = false;
      
      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) chunks.push(value);
      }
      
      const compressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
      let offset = 0;
      for (const chunk of chunks) {
        compressed.set(chunk, offset);
        offset += chunk.length;
      }
      
      return btoa(String.fromCharCode(...compressed));
    }
    
    // Fallback: simple run-length encoding
    return this.simpleCompress(data);
  }

  private async decompress(data: string): Promise<string> {
    if (typeof DecompressionStream !== 'undefined') {
      try {
        const compressed = Uint8Array.from(atob(data), c => c.charCodeAt(0));
        const stream = new DecompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();
        
        writer.write(compressed);
        writer.close();
        
        const chunks: Uint8Array[] = [];
        let done = false;
        
        while (!done) {
          const { value, done: readerDone } = await reader.read();
          done = readerDone;
          if (value) chunks.push(value);
        }
        
        const decompressed = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
        let offset = 0;
        for (const chunk of chunks) {
          decompressed.set(chunk, offset);
          offset += chunk.length;
        }
        
        return new TextDecoder().decode(decompressed);
      } catch (error) {
        // Fallback to simple decompression
        return this.simpleDecompress(data);
      }
    }
    
    return this.simpleDecompress(data);
  }

  private simpleCompress(data: string): string {
    // Simple run-length encoding
    let result = '';
    let count = 1;
    let current = data[0];
    
    for (let i = 1; i < data.length; i++) {
      if (data[i] === current && count < 255) {
        count++;
      } else {
        result += String.fromCharCode(count) + current;
        current = data[i];
        count = 1;
      }
    }
    result += String.fromCharCode(count) + current;
    
    return btoa(result);
  }

  private simpleDecompress(data: string): string {
    const compressed = atob(data);
    let result = '';
    
    for (let i = 0; i < compressed.length; i += 2) {
      const count = compressed.charCodeAt(i);
      const char = compressed[i + 1];
      result += char.repeat(count);
    }
    
    return result;
  }
}

/**
 * Encryption hook for secure message transmission
 */
export class EncryptionHook implements MessageTransformHook {
  name = 'encryption';
  priority = 200; // Higher priority than compression

  private encryptionKey: string | null = null;

  constructor(key?: string) {
    this.encryptionKey = key || null;
  }

  setKey(key: string): void {
    this.encryptionKey = key;
  }

  async encode(message: BaseMessage): Promise<BaseMessage> {
    if (!this.encryptionKey) {
      return message; // Skip encryption if no key
    }

    try {
      const serialized = JSON.stringify(message.payload);
      const encrypted = await this.encrypt(serialized);
      
      return {
        ...message,
        payload: {
          _encrypted: true,
          data: encrypted
        },
        metadata: {
          ...message.metadata,
          encrypted: true
        }
      };
    } catch (error) {
      console.warn('Encryption failed, sending unencrypted:', error);
      return message;
    }
  }

  async decode(message: BaseMessage): Promise<BaseMessage> {
    if (message.payload._encrypted) {
      if (!this.encryptionKey) {
        throw new Error('Cannot decrypt message: no encryption key available');
      }

      try {
        const decrypted = await this.decrypt(message.payload.data);
        const originalPayload = JSON.parse(decrypted);
        
        return {
          ...message,
          payload: originalPayload,
          metadata: {
            ...message.metadata,
            encrypted: false
          }
        };
      } catch (error) {
        console.error('Decryption failed:', error);
        throw new Error('Failed to decrypt message');
      }
    }
    
    return message;
  }

  private async encrypt(data: string): Promise<string> {
    // Simple XOR encryption (for demonstration - use proper encryption in production)
    const key = this.encryptionKey!;
    let result = '';
    
    for (let i = 0; i < data.length; i++) {
      const keyChar = key.charCodeAt(i % key.length);
      const dataChar = data.charCodeAt(i);
      result += String.fromCharCode(dataChar ^ keyChar);
    }
    
    return btoa(result);
  }

  private async decrypt(data: string): Promise<string> {
    // XOR decryption (same as encryption for XOR)
    const encrypted = atob(data);
    return this.encrypt(encrypted); // XOR is symmetric
  }
}

/**
 * Logging hook for message tracing and debugging
 */
export class LoggingHook implements MessageTransformHook {
  name = 'logging';
  priority = 10; // Low priority - runs last

  private logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info';

  constructor(logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info') {
    this.logLevel = logLevel;
  }

  async encode(message: BaseMessage): Promise<BaseMessage> {
    if (this.logLevel === 'debug') {
      console.log('[WebSocket] Outgoing message:', {
        type: message.type,
        timestamp: message.metadata.timestamp,
        correlationId: message.metadata.correlationId,
        payloadSize: JSON.stringify(message.payload).length
      });
    }
    return message;
  }

  async decode(message: BaseMessage): Promise<BaseMessage> {
    if (this.logLevel === 'debug') {
      console.log('[WebSocket] Incoming message:', {
        type: message.type,
        timestamp: message.metadata.timestamp,
        correlationId: message.metadata.correlationId,
        payloadSize: JSON.stringify(message.payload).length
      });
    }
    return message;
  }
}

/**
 * Metrics collection hook
 */
export class MetricsHook implements MessageTransformHook {
  name = 'metrics';
  priority = 5; // Very low priority

  private metrics = {
    messagesSent: 0,
    messagesReceived: 0,
    totalBytesSent: 0,
    totalBytesReceived: 0,
    messageTypes: new Map<string, number>(),
    errors: 0
  };

  async encode(message: BaseMessage): Promise<BaseMessage> {
    this.metrics.messagesSent++;
    this.metrics.totalBytesSent += JSON.stringify(message).length;
    
    const count = this.metrics.messageTypes.get(message.type) || 0;
    this.metrics.messageTypes.set(message.type, count + 1);
    
    return message;
  }

  async decode(message: BaseMessage): Promise<BaseMessage> {
    this.metrics.messagesReceived++;
    this.metrics.totalBytesReceived += JSON.stringify(message).length;
    
    const count = this.metrics.messageTypes.get(message.type) || 0;
    this.metrics.messageTypes.set(message.type, count + 1);
    
    return message;
  }

  getMetrics() {
    return {
      ...this.metrics,
      messageTypes: Object.fromEntries(this.metrics.messageTypes)
    };
  }

  resetMetrics(): void {
    this.metrics = {
      messagesSent: 0,
      messagesReceived: 0,
      totalBytesSent: 0,
      totalBytesReceived: 0,
      messageTypes: new Map(),
      errors: 0
    };
  }
}

/**
 * Message validation hook
 */
export class ValidationHook implements MessageTransformHook {
  name = 'validation';
  priority = 300; // Very high priority

  async encode(message: BaseMessage): Promise<BaseMessage> {
    this.validateMessage(message);
    return message;
  }

  async decode(message: BaseMessage): Promise<BaseMessage> {
    this.validateMessage(message);
    return message;
  }

  private validateMessage(message: BaseMessage): void {
    if (!message.type) {
      throw new Error('Message missing type field');
    }
    
    if (!message.metadata) {
      throw new Error('Message missing metadata field');
    }
    
    if (!message.payload) {
      throw new Error('Message missing payload field');
    }
    
    if (!message.metadata.timestamp) {
      throw new Error('Message metadata missing timestamp');
    }
  }
}
