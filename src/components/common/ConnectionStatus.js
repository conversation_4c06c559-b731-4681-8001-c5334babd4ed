import React, { useState, useEffect } from 'react';
import { useAppContext } from '../../context/AppContext';
import BackendService from '../../services/BackendService';
import './ConnectionStatus.css';

const ConnectionStatus = () => {
  const { connectionStatus } = useAppContext();
  const [connectionMetadata, setConnectionMetadata] = useState({});
  const [retryCountdown, setRetryCountdown] = useState(null);
  const [adaptiveTimeoutStatus, setAdaptiveTimeoutStatus] = useState(null);

  // Update connection metadata when status changes
  useEffect(() => {
    const updateMetadata = () => {
      const metadata = BackendService.getConnectionMetadata();
      setConnectionMetadata(metadata);

      // Get adaptive timeout status if available
      if (BackendService.getAdaptiveTimeoutStatus) {
        const adaptiveStatus = BackendService.getAdaptiveTimeoutStatus();
        setAdaptiveTimeoutStatus(adaptiveStatus);
      }
    };

    updateMetadata();
    const interval = setInterval(updateMetadata, 1000);
    return () => clearInterval(interval);
  }, [connectionStatus]);

  // Handle retry countdown
  useEffect(() => {
    if (connectionStatus === 'retrying' && connectionMetadata.nextRetryIn) {
      const startTime = Date.now();
      const duration = connectionMetadata.nextRetryIn;

      const updateCountdown = () => {
        const elapsed = Date.now() - startTime;
        const remaining = Math.max(0, Math.ceil((duration - elapsed) / 1000));
        setRetryCountdown(remaining);

        if (remaining <= 0) {
          setRetryCountdown(null);
        }
      };

      updateCountdown();
      const interval = setInterval(updateCountdown, 1000);
      return () => clearInterval(interval);
    } else {
      setRetryCountdown(null);
    }
  }, [connectionStatus, connectionMetadata.nextRetryIn]);

  const getStatusInfo = () => {
    let baseTooltip = '';
    let color = '';

    switch (connectionStatus) {
      case 'connected':
        color = '#00ff88';
        baseTooltip = 'Connected';
        break;
      case 'connecting':
        color = '#ffc107';
        baseTooltip = 'Connecting';
        break;
      case 'reconnecting':
        color = '#ff9800';
        baseTooltip = 'Reconnecting';
        break;
      case 'retrying':
        color = '#2196f3';
        baseTooltip = 'Retrying';
        break;
      case 'error':
        color = '#ff4444';
        baseTooltip = 'Error';
        break;
      default:
        color = '#888888';
        baseTooltip = 'Disconnected';
    }

    // Enhance tooltip with adaptive timeout information
    let tooltip = baseTooltip;
    if (adaptiveTimeoutStatus && typeof adaptiveTimeoutStatus === 'object' && connectionStatus === 'connected') {
      try {
        const {
          queueTasksActive = false,
          currentPingTimeout = 15000,
          consecutiveTimeouts = 0,
          isAdaptiveModeActive = false
        } = adaptiveTimeoutStatus;

        if (isAdaptiveModeActive) {
          tooltip += '\n';
          if (queueTasksActive) {
            tooltip += '🔄 QueueIT tasks active - adaptive ping mode';
          } else if (consecutiveTimeouts > 0) {
            tooltip += `⚠️ ${consecutiveTimeouts} ping timeout(s) - adaptive mode`;
          }

          // Safely handle currentPingTimeout
          const timeoutSeconds = typeof currentPingTimeout === 'number' ?
            Math.round(currentPingTimeout / 1000) : 15;
          tooltip += `\n⏱️ Ping timeout: ${timeoutSeconds}s`;
        }
      } catch (error) {
        console.warn('Error processing adaptive timeout status:', error);
        // Continue with base tooltip if there's an error
      }
    }

    return { color, tooltip };
  };

  const handleRetryClick = (e) => {
    e.stopPropagation();
    BackendService.reconnect().catch(error => {
      console.error('Manual reconnect failed:', error);
    });
  };

  const statusInfo = getStatusInfo();

  return (
    <div 
      className="connection-status"
      style={{ '--status-color': statusInfo.color }}
      onClick={handleRetryClick}
      title={statusInfo.tooltip}
    >
      <div className="status-indicator" />
    </div>
  );
};

export default ConnectionStatus;
