import React, { useState, useEffect } from 'react';
import './TopBar.css';
import ModeSwitcher from './ModeSwitcher';
import ConnectionStatus from './ConnectionStatus';
import { FaArrowLeft } from 'react-icons/fa';
import ghostLogo from '../../assets/ghost.svg';
import BackendService from '../../services/BackendService';

const TopBar = ({ mode, onChangeMode, onNavigate, currentPage, isMonitoring }) => {
  const [clickCount, setClickCount] = useState(0);
  const [showTooltip, setShowTooltip] = useState(false);

  // Enhanced navigation handlers that refresh data when menus are opened
  const handleDocumentationClick = async () => {
    try {
      // First navigate to the documentation page
      onNavigate('documentation');

      // Then refresh the documentation data from backend
      console.log('📚 TopBar: Refreshing documentation data...');
      const response = await BackendService.refreshDocumentation();

      if (response.type === 'documentation_refreshed') {
        console.log('📚 TopBar: Documentation data refreshed successfully');
        // Dispatch custom event to notify Documentation component of fresh data
        window.dispatchEvent(new CustomEvent('documentationRefreshed', {
          detail: response.data
        }));
      } else if (response.type === 'error') {
        console.warn('📚 TopBar: Failed to refresh documentation:', response.error);
      }
    } catch (error) {
      console.error('📚 TopBar: Error refreshing documentation:', error);
      // Navigation still works even if refresh fails
    }
  };

  const handleSettingsClick = async () => {
    try {
      // First navigate to the settings page
      onNavigate('settings');

      // Then refresh the settings data from backend
      console.log('⚙️ TopBar: Refreshing settings data...');
      const response = await BackendService.refreshSettings();

      if (response.type === 'settings_refreshed') {
        console.log('⚙️ TopBar: Settings data refreshed successfully');
        // Dispatch custom event to notify Settings component of fresh data
        window.dispatchEvent(new CustomEvent('settingsRefreshed', {
          detail: response.data
        }));
      } else if (response.type === 'error') {
        console.warn('⚙️ TopBar: Failed to refresh settings:', response.error);
      }
    } catch (error) {
      console.error('⚙️ TopBar: Error refreshing settings:', error);
      // Navigation still works even if refresh fails
    }
  };
  const [tooltipTimeout, setTooltipTimeout] = useState(null);

  // Reset click count after 3 seconds of inactivity
  useEffect(() => {
    if (clickCount > 0) {
      const timeout = setTimeout(() => {
        setClickCount(0);
        setShowTooltip(false);
      }, 3000);
      return () => clearTimeout(timeout);
    }
  }, [clickCount]);

  // Handle ghost logo click
  const handleGhostClick = () => {
    const newCount = clickCount + 1;
    setClickCount(newCount);
    setShowTooltip(true);

    if (newCount >= 7) {
      // Toggle between dev and base mode
      onChangeMode(mode === 'dev' ? 'base' : 'dev');
      setClickCount(0);
      setShowTooltip(false);
    }
  };

  // Get tooltip message based on current mode
  const getTooltipMessage = () => {
    const remainingClicks = 7 - clickCount;
    if (mode === 'dev') {
      return `Click ${remainingClicks} more times to exit developer mode`;
    }
    return `Click ${remainingClicks} more times to enter developer mode`;
  };

  // Electron window control handlers
  const handleClose = () => {
    if (window.electronAPI?.windowControls?.close) {
      window.electronAPI.windowControls.close();
    }
  };

  const handleMinimize = () => {
    if (window.electronAPI?.windowControls?.minimize) {
      window.electronAPI.windowControls.minimize();
    }
  };

  const handleMaximize = () => {
    if (window.electronAPI?.windowControls?.maximize) {
      window.electronAPI.windowControls.maximize();
    }
  };

  const isDocumentationPage = currentPage === 'documentation';
  const isSettingsPage = currentPage === 'settings';
  const isMainPage = currentPage === 'main' && !isMonitoring;

  // open dev mode onClick={handleGhostClick}
  return (
    <header className="kasper-topbar" role="banner" aria-label="Kasper Navigation Bar">
      <div className="kasper-topbar__controls" aria-label="Window Controls">
        <button
          className="mac-dot mac-dot--red"
          aria-label="Close window"
          tabIndex={0}
          onClick={handleClose}
          type="button"
        />
        <button
          className="mac-dot mac-dot--yellow"
          aria-label="Minimize window"
          tabIndex={0}
          onClick={handleMinimize}
          type="button"
        />
        <button
          className="mac-dot mac-dot--green"
          aria-label="Maximize window"
          tabIndex={0}
          onClick={handleMaximize}
          type="button"
        />
      </div>

      <div className="kasper-topbar__center">
        <div className="ghost-logo-container">
          <img src={ghostLogo} alt="Ghost Logo" className="kasper-logo" />
          {showTooltip && (
            <div className="ghost-tooltip">
              {getTooltipMessage()}
            </div>
          )}
        </div>
      </div>

      <div className="kasper-topbar__right" aria-label="Topbar Actions">
        <ConnectionStatus />
        {!isMonitoring && (isDocumentationPage || isSettingsPage ? (
          <button
            className="topbar-action-button"
            onClick={() => onNavigate('main')}
            title="Back"
            aria-label="Go Back"
            type="button"
          >
            <FaArrowLeft size={14} />
          </button>
        ) : (
          <>
            <button
              className="topbar-action-button"
              onClick={handleDocumentationClick}
              title="Documentation"
              aria-label="Open Documentation"
              type="button"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 16V16.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M12 8V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <button
              className="topbar-action-button"
              onClick={handleSettingsClick}
              title="Settings"
              aria-label="Open Settings"
              type="button"
            >
              <svg width="14" height="14" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.024 9.25c.47 0 .827-.433.637-.863a4 4 0 0 0-4.094-2.364c-.468.05-.665.576-.43.984l1.08 1.868a.75.75 0 0 0 .649.375h2.158ZM7.84 7.758c-.236-.408-.79-.5-1.068-.12A3.982 3.982 0 0 0 6 10c0 .884.287 1.7.772 2.363.278.38.832.287 1.068-.12l1.078-1.868a.75.75 0 0 0 0-.75L7.839 7.758ZM9.138 12.993c-.235.408-.039.934.43.984a4 4 0 0 0 4.094-2.364c.19-.43-.168-.863-.638-.863h-2.158a.75.75 0 0 0-.65.375l-1.078 1.868Z" />
                <path fillRule="evenodd" d="m14.13 4.347.644-1.117a.75.75 0 0 0-1.299-.75l-.644 1.116a6.954 6.954 0 0 0-2.081-.556V1.75a.75.75 0 0 0-1.5 0v1.29a6.954 6.954 0 0 0-2.081.556L6.525 2.48a.75.75 0 1 0-1.3.75l.645 1.117A7.04 7.04 0 0 0 4.347 5.87L3.23 5.225a.75.75 0 1 0-.75 1.3l1.116.644A6.954 6.954 0 0 0 3.04 9.25H1.75a.75.75 0 0 0 0 1.5h1.29c.078.733.27 1.433.556 2.081l-1.116.645a.75.75 0 1 0 .75 1.298l1.117-.644a7.04 7.04 0 0 0 1.523 1.523l-.645 1.117a.75.75 0 1 0 1.3.75l.644-1.116a6.954 6.954 0 0 0 2.081.556v1.29a.75.75 0 0 0 1.5 0v-1.29a6.954 6.954 0 0 0 2.081-.556l.645 1.116a.75.75 0 0 0 1.299-.75l-.645-1.117a7.042 7.042 0 0 0 1.523-1.523l1.117.644a.75.75 0 0 0 .75-1.298l-1.116-.645a6.954 6.954 0 0 0 .556-2.081h1.29a.75.75 0 0 0 0-1.5h-1.29a6.954 6.954 0 0 0-.556-2.081l1.116-.644a.75.75 0 0 0-.75-1.3l-1.117.645a7.04 7.04 0 0 0-1.524-1.523ZM10 4.5a5.475 5.475 0 0 0-2.781.754A5.527 5.527 0 0 0 5.22 7.277 5.475 5.475 0 0 0 4.5 10a5.475 5.475 0 0 0 .752 2.777 5.527 5.527 0 0 0 2.028 2.004c.802.458 1.73.719 2.72.719a5.474 5.474 0 0 0 2.78-.753 5.527 5.527 0 0 0 2.001-2.027c.458-.802.719-1.73.719-2.72a5.475 5.475 0 0 0-.753-2.78 5.528 5.528 0 0 0-2.028-2.002A5.475 5.475 0 0 0 10 4.5Z" clipRule="evenodd" />
              </svg>
            </button>
          </>
        ))}
      </div>
    </header>
  );
};

export default TopBar; 