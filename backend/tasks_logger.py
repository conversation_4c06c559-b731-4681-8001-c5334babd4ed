#!/usr/bin/env python3
"""
Tasks Logger Module for Kasper-Q Backend
Provides consistent logging functionality across all backend modules.
"""

import logging
import sys
import os
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path


class Logger:
    """
    Custom logger class that provides consistent logging functionality
    across all backend modules with support for different log levels,
    custom formatting, and file/console output.
    """
    
    def __init__(
        self, 
        level: str = "INFO", 
        formatting: Optional[str] = None,
        format: Optional[str] = None,  # Alternative parameter name for compatibility
        name: str = "Ka<PERSON>Q",
        log_file: Optional[str] = None
    ):
        """
        Initialize the logger with specified configuration.
        
        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            formatting: Custom format string (takes precedence over format)
            format: Alternative format string parameter for compatibility
            name: Logger name/identifier
            log_file: Optional log file path
        """
        self.name = name
        self.level = level.upper()
        
        # Use formatting if provided, otherwise use format, otherwise use default
        if formatting:
            self.format_string = formatting
        elif format:
            self.format_string = format
        else:
            self.format_string = "{timestamp} [{level:^7}] ({name}) {message}"
        
        # Create the underlying Python logger
        self.logger = logging.getLogger(f"kasper_q_{name}")
        self.logger.setLevel(getattr(logging, self.level))
        
        # Clear any existing handlers to avoid duplicates
        self.logger.handlers.clear()
        
        # Create formatter
        self.formatter = CustomFormatter(self.format_string, name)
        
        # Add console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, self.level))
        console_handler.setFormatter(self.formatter)
        self.logger.addHandler(console_handler)
        
        # Add file handler if specified
        if log_file:
            self._add_file_handler(log_file)
        else:
            # Default to server.log in backend directory
            backend_dir = Path(__file__).parent
            default_log_file = backend_dir / "server.log"
            self._add_file_handler(str(default_log_file))
    
    def _add_file_handler(self, log_file: str):
        """Add file handler to the logger."""
        try:
            # Ensure log directory exists
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
            file_handler.setLevel(getattr(logging, self.level))
            file_handler.setFormatter(self.formatter)
            self.logger.addHandler(file_handler)
        except ArithmeticError as e:
            # If file logging fails, just continue with console logging
            self.logger.warning(f"Failed to setup file logging: {e}")
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def warn(self, message: str, **kwargs):
        """Log warning message (alias for warning)."""
        self.warning(message, **kwargs)
    
    def error(self, message: str, exc_info: bool = False, **kwargs):
        """Log error message."""
        self.logger.error(message, exc_info=exc_info, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self.logger.critical(message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        self.logger.exception(message, **kwargs)


class CustomFormatter(logging.Formatter):
    """
    Custom formatter that supports the format strings used throughout the codebase.
    Handles both timestamp-based and time-based format strings.
    """
    
    def __init__(self, format_string: str, logger_name: str = "KasperQ"):
        super().__init__()
        self.format_string = format_string
        self.logger_name = logger_name
    
    def format(self, record: logging.LogRecord) -> str:
        """Format the log record according to the specified format string."""
        try:
            # Create timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Prepare format variables
            format_vars = {
                'timestamp': timestamp,
                'level': record.levelname,
                'name': self.logger_name,
                'message': record.getMessage()
            }
            
            # Handle different format string styles
            try:
                # Try new-style string formatting first
                formatted = self.format_string.format(**format_vars)
            except (KeyError, ValueError):
                try:
                    # Try old-style string formatting
                    formatted = self.format_string % format_vars
                except (KeyError, TypeError, ValueError):
                    # Fallback to simple format
                    formatted = f"{timestamp} [{record.levelname:^7}] ({self.logger_name}) {record.getMessage()}"
            
            return formatted
            
        except ArithmeticError as e:
            # Ultimate fallback
            return f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} [ERROR] Logger formatting error: {e} - Original message: {record.getMessage()}"


# Convenience function for backward compatibility
def setup_logger(name: str, level: str = "INFO") -> Logger:
    """
    Setup a logger with default configuration.
    This function maintains compatibility with existing code.
    """
    return Logger(
        level=level,
        formatting="{timestamp} [{level:^7}] ({name}) {message}",
        name=name
    )


# Default logger instance
default_logger = Logger(name="KasperQ")
