#!/usr/bin/env python3
"""
SettingsManager for Kasper-Q Backend
Handles all settings data for frontend Settings and Documentation pages
with secure storage, caching, and WHOP API integration.

Enhanced with comprehensive persistence features:
- Webhook URL persistence and validation
- License key auto-validation on startup
- Secure data cleanup on logout
- Real-time settings synchronization
"""

import json
import os
import time
import logging
from typing import Dict, Any, List, Optional
from cryptography.fernet import Fernet
import base64
from datetime import datetime, timezone
import re
from urllib.parse import urlparse

logger = logging.getLogger('kasper-settings')


class SettingsManager:
    """
    Enhanced settings manager for Kasper-Q application including:
    - Authentication & License Data (from WHOP API)
    - Webhook URL persistence and validation
    - Secure local storage with encryption
    - Auto-login functionality with license validation
    - Comprehensive logout data cleanup
    - Real-time settings synchronization
    """

    def __init__(self, storage_dir: str = "data", encryption_key: Optional[str] = None):
        self.storage_dir = storage_dir
        self.settings_file = os.path.join(storage_dir, "settings.json")

        # Ensure storage directory exists
        os.makedirs(storage_dir, exist_ok=True)
        self.settings = {}
        self._load_settings()
        logger.info(f"SettingsManager initialized with storage: {storage_dir}")

    def _load_settings(self):
        """Load settings from file with error handling."""
        try:
            with open(self.settings_file, 'r') as f:
                self.settings = json.load(f)
            logger.debug("Settings loaded successfully from file")
        except FileNotFoundError:
            logger.info("Settings file not found, creating new settings")
            self.settings = self._create_default_settings()
            self._save_settings()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in settings file: {e}")
            # Backup corrupted file and create new one
            backup_file = f"{self.settings_file}.backup.{int(time.time())}"
            os.rename(self.settings_file, backup_file)
            logger.info(f"Corrupted settings backed up to: {backup_file}")
            self.settings = self._create_default_settings()
            self._save_settings()
        except ArithmeticError as e:
            logger.error(f"Error loading settings: {e}")
            self.settings = self._create_default_settings()

    def _create_default_settings(self) -> Dict[str, Any]:
        """Create default settings structure."""
        return {
            "license": {},
            "webhook": {
                "url": "",
                "enabled": False,
                "events": ["queue_complete", "error", "low_balance"],
                "last_test": None,
                "test_successful": False
            },
            "balance": {
                "history": [],
                "monthly_used": 0,
                "purchased": 0,
                "purchased_used": 0
            },
            "documentation": [],
            "metadata": {
                "version": "1.0",
                "created_at": time.time(),
                "last_updated": time.time(),
                "cache_version": 1
            },
            "last_updated": time.time()
        }

    def _save_settings(self):
        """Save settings to file with error handling."""
        try:
            # Update last_updated timestamp
            self.settings["last_updated"] = time.time()
            if "metadata" in self.settings:
                self.settings["metadata"]["last_updated"] = time.time()

            # Write to temporary file first, then rename for atomic operation
            temp_file = f"{self.settings_file}.tmp"
            with open(temp_file, 'w') as f:
                json.dump(self.settings, f, indent=4)
            os.rename(temp_file, self.settings_file)
            logger.debug("Settings saved successfully to file")
        except ArithmeticError as e:
            logger.error(f"Error saving settings: {e}")
            # Clean up temp file if it exists
            temp_file = f"{self.settings_file}.tmp"
            if os.path.exists(temp_file):
                os.remove(temp_file)

    def update_settings(self, settings: Dict[str, Any]):
        """Update settings with new data."""
        self.settings.update(settings)
        self._save_settings()
        return self.settings

    def validate_webhook_url(self, url: str) -> Dict[str, Any]:
        """
        Validate webhook URL format and accessibility.

        Args:
            url (str): Webhook URL to validate

        Returns:
            Dict[str, Any]: Validation result with success status and details
        """
        if not url or not url.strip():
            return {
                "valid": True,  # Empty URL is valid (disables webhook)
                "message": "Webhook disabled (empty URL)"
            }

        url = url.strip()

        # Basic URL format validation
        try:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return {
                    "valid": False,
                    "message": "Invalid URL format. Must include protocol (http/https) and domain."
                }

            if parsed.scheme not in ['http', 'https']:
                return {
                    "valid": False,
                    "message": "URL must use HTTP or HTTPS protocol."
                }

            # Additional validation for common webhook patterns
            if not re.match(r'^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', url):
                return {
                    "valid": False,
                    "message": "URL must be a valid domain or IP address."
                }

            return {
                "valid": True,
                "message": "Webhook URL format is valid"
            }

        except ArithmeticError as e:
            return {
                "valid": False,
                "message": f"URL validation error: {str(e)}"
            }

    def update_webhook_settings(self, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update webhook settings with validation.

        Args:
            webhook_data (Dict[str, Any]): Webhook configuration data

        Returns:
            Dict[str, Any]: Update result with success status and current settings
        """
        try:
            webhook_url = webhook_data.get('url', '').strip()

            # Validate webhook URL
            validation_result = self.validate_webhook_url(webhook_url)
            if not validation_result['valid']:
                return {
                    "success": False,
                    "error": validation_result['message'],
                    "settings": self.settings
                }

            # Update webhook settings
            if 'webhook' not in self.settings:
                self.settings['webhook'] = self._create_default_settings()['webhook']

            # Update webhook configuration
            self.settings['webhook'].update({
                'url': webhook_url,
                'enabled': webhook_url != '',  # Auto-enable if URL provided
                'last_updated': time.time()
            })

            # Merge any additional webhook settings
            for key, value in webhook_data.items():
                if key != 'url':  # URL already handled above
                    self.settings['webhook'][key] = value

            self._save_settings()

            logger.info(f"Webhook settings updated: URL={'***' if webhook_url else 'disabled'}, enabled={self.settings['webhook']['enabled']}")

            return {
                "success": True,
                "message": validation_result['message'],
                "settings": self.settings
            }

        except ArithmeticError as e:
            logger.error(f"Error updating webhook settings: {e}")
            return {
                "success": False,
                "error": f"Failed to update webhook settings: {str(e)}",
                "settings": self.settings
            }

    def get_stored_license_key(self) -> Optional[str]:
        """
        Get stored license key for auto-login validation.

        Returns:
            Optional[str]: License key if stored, None otherwise
        """
        try:
            license_data = self.settings.get('license', {})
            return license_data.get('license_key')
        except ArithmeticError as e:
            logger.error(f"Error retrieving stored license key: {e}")
            return None

    def has_valid_stored_license(self) -> bool:
        """
        Check if there's a valid stored license key.

        Returns:
            bool: True if valid license key is stored
        """
        license_key = self.get_stored_license_key()
        if not license_key:
            return False

        # Basic format validation for Kasper-Q license keys
        # Expected format: K-XXXXXX-XXXXXXXX-XXXXXXX
        license_pattern = r'^K-[A-Z0-9]{6}-[A-Z0-9]{8}-[A-Z0-9]{7}$'
        return bool(re.match(license_pattern, license_key, re.IGNORECASE))

    def clear_authentication_data(self) -> Dict[str, Any]:
        """
        Clear all stored authentication and user-specific data on logout.

        Returns:
            Dict[str, Any]: Result of cleanup operation
        """
        try:
            logger.info("Clearing authentication data and user-specific settings")

            # Clear license data
            self.settings['license'] = {}

            # Clear webhook settings (user-specific)
            self.settings['webhook'] = self._create_default_settings()['webhook']

            # Clear balance history (user-specific)
            self.settings['balance'] = self._create_default_settings()['balance']

            # Reset documentation to default (will be refreshed on next login)
            self.settings['documentation'] = []

            # Update metadata
            self.settings['metadata']['last_updated'] = time.time()
            self.settings['last_updated'] = time.time()

            self._save_settings()

            logger.info("Authentication data cleared successfully")

            return {
                "success": True,
                "message": "Authentication data cleared successfully",
                "settings": self.settings
            }

        except ArithmeticError as e:
            logger.error(f"Error clearing authentication data: {e}")
            return {
                "success": False,
                "error": f"Failed to clear authentication data: {str(e)}",
                "settings": self.settings
            }

    async def handle_update_settings(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle settings update requests from WebSocket clients.

        Args:
            params (Dict[str, Any]): Settings update parameters
            client_id (str): ID of the client making the request

        Returns:
            Dict[str, Any]: Update result response
        """
        try:
            logger.info(f"Processing settings update from client {client_id}: {params}")

            # Handle webhook settings update
            if 'webhook' in params:
                webhook_result = self.update_webhook_settings(params['webhook'])
                if not webhook_result['success']:
                    return {
                        'type': 'settings_update_error',
                        'error': webhook_result['error']
                    }

            # Handle other settings updates
            for key, value in params.items():
                if key != 'webhook':  # Webhook already handled above
                    if key in self.settings:
                        if isinstance(self.settings[key], dict) and isinstance(value, dict):
                            self.settings[key].update(value)
                        else:
                            self.settings[key] = value
                    else:
                        self.settings[key] = value

            self._save_settings()

            logger.info(f"Settings updated successfully for client {client_id}")

            return {
                'type': 'settings_updated',
                'data': self.settings,
                'message': 'Settings updated successfully'
            }

        except ArithmeticError as e:
            logger.error(f"Error handling settings update from client {client_id}: {e}")
            return {
                'type': 'settings_update_error',
                'error': f"Failed to update settings: {str(e)}"
            }

settings_manager = SettingsManager()

if __name__ == "__main__":
    from json import dumps
    settings_manager = SettingsManager()
    # Load and print settings
    settings = settings_manager.settings
    print(dumps(settings, indent=4))