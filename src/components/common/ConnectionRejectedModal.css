.connection-rejected-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  -webkit-app-region: no-drag;
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  isolation: isolate;
}

.connection-rejected-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--background-primary-rgb), 0.8);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  display: flex;
  flex-direction: column;
  padding: 0;
  width: 100%;
  height: 100%;
  color: var(--text-primary);
  animation: fadeIn 0.3s ease-out;
  pointer-events: auto;
  z-index: 10000;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.connection-rejected-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(var(--background-secondary-rgb), 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  min-height: 60px;
}

.connection-rejected-icon {
  font-size: 1.5rem;
  margin-right: 12px;
  color: var(--error-color);
  filter: drop-shadow(0 0 8px rgba(var(--error-color-rgb), 0.5));
}

.connection-rejected-header h2 {
  margin: 0;
  color: var(--error-color);
  font-size: 1.25rem;
  font-weight: 600;
  text-shadow: 0 0 20px rgba(var(--error-color-rgb), 0.3);
  letter-spacing: -0.02em;
}

.connection-rejected-header h2 strong {
  color: var(--error-color);
  font-weight: 700;
  text-shadow: 0 0 20px rgba(var(--error-color-rgb), 0.5);
}

.connection-rejected-content {
  flex: 1;
  padding: 16px 20px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  background: rgba(var(--background-primary-rgb), 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.connection-rejected-message {
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
  color: var(--text-primary);
  padding: 16px;
  background: rgba(var(--error-color-rgb), 0.1);
  border: 1px solid rgba(var(--error-color-rgb), 0.2);
  border-radius: 6px;
}

.connection-rejected-message strong {
  color: var(--error-color);
  font-weight: 600;
  text-shadow: 0 0 10px rgba(var(--error-color-rgb), 0.2);
}

.connection-rejected-details {
  background: rgba(var(--background-secondary-rgb), 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 16px;
  margin: 0;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.connection-rejected-details h3 {
  margin: 0 0 8px 0;
  color: var(--accent-color);
  font-size: 1rem;
  font-weight: 600;
}

.connection-rejected-details ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.connection-rejected-details li {
  margin-bottom: 4px;
  color: var(--text-secondary);
  line-height: 1.4;
  font-size: 0.9375rem;
}

.connection-rejected-details strong {
  color: var(--accent-color);
  font-weight: 500;
}

.connection-rejected-instructions {
  background: rgba(var(--background-secondary-rgb), 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 16px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.connection-rejected-instructions h3 {
  margin: 0 0 8px 0;
  color: var(--accent-color);
  font-size: 1rem;
  font-weight: 600;
}

.connection-rejected-instructions ol {
  margin: 0;
  padding-left: 20px;
}

.connection-rejected-instructions li {
  margin-bottom: 4px;
  color: var(--text-secondary);
  line-height: 1.4;
  font-size: 0.9375rem;
}

.connection-rejected-instructions li strong {
  color: var(--accent-color);
  font-weight: 500;
}

.connection-rejected-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(var(--background-secondary-rgb), 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  min-height: 60px;
}

.connection-rejected-retry-btn,
.connection-rejected-close-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 0.9375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.connection-rejected-retry-btn {
  background: var(--primary-color);
  color: var(--text-on-primary);
}

.connection-rejected-retry-btn:hover {
  background: var(--primary-color-hover);
}

.connection-rejected-close-btn {
  background: var(--background-tertiary);
  color: var(--text-primary);
}

.connection-rejected-close-btn:hover {
  background: var(--background-tertiary-hover);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 600px) {
  .connection-rejected-content {
    padding: 12px 16px;
  }
  
  .connection-rejected-actions {
    flex-direction: column;
    padding: 12px 16px;
  }
  
  .connection-rejected-retry-btn,
  .connection-rejected-close-btn {
    width: 100%;
  }
  
  .connection-rejected-header {
    padding: 12px 16px;
  }
  
  .connection-rejected-header h2 {
    font-size: 1.125rem;
  }
  
  .connection-rejected-message {
    font-size: 0.9375rem;
  }
}
