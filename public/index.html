<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Kasper-Q - Advanced AI-powered penetration testing tool"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <title>Kasper-Q</title>
    
    <style>
      /* Prevent flash of unstyled content */
      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #0a0a0a;
        color: #ffffff;
        overflow: hidden;
      }
      
      code {
        font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      /* Loading animation */
      .initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #0D0D0D;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      /* Smooth background gradient */
      .initial-loader::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(124, 77, 255, 0.06) 0%, transparent 70%);
        animation: smoothPulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }
      
      .loader-logo {
        width: 120px;
        height: 120px;
        position: relative;
        z-index: 2;
        animation: smoothLogoPulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        filter: brightness(1.2) contrast(1.1);
      }
      
      .loader-logo img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        filter: drop-shadow(0 0 20px rgba(124, 77, 255, 0.3)) 
                drop-shadow(0 0 40px rgba(124, 77, 255, 0.15));
      }
      
      /* Smooth logo pulse animation */
      @keyframes smoothLogoPulse {
        0%, 100% { 
          transform: scale(1); 
          filter: drop-shadow(0 0 20px rgba(124, 77, 255, 0.3)) 
                  drop-shadow(0 0 40px rgba(124, 77, 255, 0.15));
        }
        50% { 
          transform: scale(1.05);
          filter: drop-shadow(0 0 25px rgba(124, 77, 255, 0.4)) 
                  drop-shadow(0 0 50px rgba(124, 77, 255, 0.2))
                  drop-shadow(0 0 70px rgba(124, 77, 255, 0.08));
        }
      }
      
      /* Smooth background pulse animation */
      @keyframes smoothPulse {
        0%, 100% { 
          opacity: 0.06; 
          transform: scale(1); 
        }
        50% { 
          opacity: 0.12; 
          transform: scale(1.02); 
        }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Initial loading screen -->
    <div id="initial-loader" class="initial-loader">
      <div class="loader-logo">
        <img src="ghost.svg" alt="Kasper-Q Logo">
      </div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide initial loader when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.style.opacity = '0';
            loader.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => loader.remove(), 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
