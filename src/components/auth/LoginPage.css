.login-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #0B0A12;
  position: relative;
  overflow: hidden;
  -webkit-app-region: drag;
}

.login-container::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(124, 77, 255, 0.1) 0%,
    transparent 50%
  );
  transform: translate(-25%, -25%);
  pointer-events: none;
  animation: pulseGlow 4s ease-in-out infinite;
}

.login-container::after {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(124, 77, 255, 0.05) 0%,
    transparent 50%
  );
  transform: translate(-25%, -25%);
  pointer-events: none;
  filter: blur(20px);
  animation: pulseGlow 4s ease-in-out infinite reverse;
}

#login-input-card {
  max-width: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 2rem;
  background: rgba(13, 13, 13, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(124, 77, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  -webkit-app-region: no-drag;
}

#login-input-card input {
  width: 100%;
  padding: 1rem;
  background: rgba(20, 19, 30, 0.7);
  border: 1px solid rgba(124, 77, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
  font-family: var(--font-mono);
  text-transform: uppercase;
  letter-spacing: 1px;
}

#login-input-card input::placeholder {
  color: rgba(124, 77, 255, 0.5);
  text-align: center;
}

#login-input-card input:focus {
  outline: none;
  border-color: #7C4DFF;
  box-shadow: 0 0 0 2px rgba(124, 77, 255, 0.2);
  background: rgba(20, 19, 30, 0.9);
}

#login-input-card input:hover {
  border-color: rgba(124, 77, 255, 0.4);
  background: rgba(20, 19, 30, 0.8);
}

.start-button {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #7C4DFF 0%, #5E35B1 100%);
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-top: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: buttonPulse 2s infinite;
}

.start-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.start-button:hover::before {
  left: 100%;
}

.start-button:hover {
  background: linear-gradient(135deg, #8E5EFF 0%, #6E45C2 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(124, 77, 255, 0.3);
}

.start-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(124, 77, 255, 0.2);
}

.error-message {
  color: #ff4d4d;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 0.25rem;
  text-shadow: 0 0 10px rgba(255, 77, 77, 0.3);
}

.loading-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at center,
    rgba(124, 77, 255, 0.4) 0%,
    transparent 70%
  );
  animation: loadingPulse 1.5s ease-in-out infinite;
}

.loading-text {
  position: relative;
  z-index: 1;
  color: #fff;
  font-weight: 600;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(124, 77, 255, 0.5);
  animation: textPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
}

@keyframes textPulse {
  0% {
    opacity: 0.7;
    text-shadow: 0 0 10px rgba(124, 77, 255, 0.5);
  }
  50% {
    opacity: 1;
    text-shadow: 0 0 20px rgba(124, 77, 255, 0.8);
  }
  100% {
    opacity: 0.7;
    text-shadow: 0 0 10px rgba(124, 77, 255, 0.5);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(124, 77, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(124, 77, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(124, 77, 255, 0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes pulseGlow {
  0% {
    opacity: 0.3;
    transform: translate(-25%, -25%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-25%, -25%) scale(1.1);
  }
  100% {
    opacity: 0.3;
    transform: translate(-25%, -25%) scale(1);
  }
}

@keyframes cardGlow {
  0% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border-color: rgba(124, 77, 255, 0.2);
  }
  50% {
    box-shadow: 0 8px 32px rgba(124, 77, 255, 0.2);
    border-color: rgba(124, 77, 255, 0.4);
  }
  100% {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    border-color: rgba(124, 77, 255, 0.2);
  }
}

.shake {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Animated background elements */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(124, 77, 255, 0.1) 0%, rgba(94, 53, 177, 0.1) 100%);
  filter: blur(60px);
  animation: float 15s infinite ease-in-out;
}

.circle-1 {
  width: 600px;
  height: 600px;
  top: -200px;
  left: -100px;
  animation-delay: 0s;
}

.circle-2 {
  width: 400px;
  height: 400px;
  top: 50%;
  right: -100px;
  animation-delay: -5s;
}

.circle-3 {
  width: 300px;
  height: 300px;
  bottom: -100px;
  left: 50%;
  animation-delay: -10s;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(50px, 50px) rotate(90deg);
  }
  50% {
    transform: translate(0, 100px) rotate(180deg);
  }
  75% {
    transform: translate(-50px, 50px) rotate(270deg);
  }
} 