#!/usr/bin/env python3
"""
Test script to verify the QueueIT task scaling functionality.
This script tests the concurrent task scaling based on ticket quantity.
"""

import asyncio
import json
import websockets
import time

async def test_queue_scaling():
    """Test the QueueIT task scaling with different ticket quantities."""
    
    uri = "ws://localhost:8765"
    
    try:
        print("🔗 Connecting to WebSocket server...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected successfully!")
            
            # Test 1: Small scale test (2 tickets)
            print("\n🎫 Testing QueueIT scaling with 2 tickets...")
            start_queue_message = {
                "command": "start_queue_entry",
                "params": {
                    "url": "https://example.queue-it.net/test",
                    "tickets": 2  # Should create 2-6 concurrent tasks
                },
                "id": str(int(time.time() * 1000))
            }
            
            await websocket.send(json.dumps(start_queue_message))
            response = await websocket.recv()
            start_response = json.loads(response)
            print(f"🚀 Start response: {start_response.get('type', 'unknown')}")
            
            if start_response.get('type') == 'queue_entry_started':
                task_data = start_response.get('data', {})
                print(f"📊 Task started with task_count: {task_data.get('task_count', 'unknown')}")
                print(f"🆔 Task ID: {task_data.get('task_id', 'unknown')}")
                
                # Monitor progress for a few seconds
                print("\n📈 Monitoring task progress...")
                for i in range(10):  # Monitor for 10 seconds
                    await asyncio.sleep(1)
                    
                    # Get queue status
                    status_message = {
                        "command": "get_queue_status",
                        "params": {},
                        "id": str(int(time.time() * 1000))
                    }
                    
                    await websocket.send(json.dumps(status_message))
                    response = await websocket.recv()
                    status_response = json.loads(response)
                    
                    if status_response.get('type') == 'queue_status':
                        data = status_response.get('data', {})
                        print(f"⏱️  [{i+1:2d}s] Status: {data.get('overall_status', 'unknown')} | "
                              f"Tasks: {data.get('task_count', 0)} | "
                              f"Passlinks: {data.get('pass_links_count', 0)}/{data.get('ticket_count', 0)}")
                        
                        # Show individual task statuses
                        tasks = data.get('tasks', [])
                        if tasks:
                            task_statuses = [f"T{t['task_index']}:{t['status']}" for t in tasks]
                            print(f"      Individual tasks: {', '.join(task_statuses)}")
                        
                        # Break if completed
                        if data.get('overall_status') == 'completed':
                            print("✅ Task completed!")
                            break
                    else:
                        print(f"⚠️  [{i+1:2d}s] No active tasks")
                
                # Stop the task
                print("\n🛑 Stopping task...")
                stop_message = {
                    "command": "stop_queue_entry",
                    "params": {
                        "entry_id": task_data.get('task_id')
                    },
                    "id": str(int(time.time() * 1000))
                }
                
                await websocket.send(json.dumps(stop_message))
                response = await websocket.recv()
                stop_response = json.loads(response)
                print(f"🛑 Stop response: {stop_response.get('type', 'unknown')}")
            
            # Test 2: Larger scale test (5 tickets)
            print("\n\n🎫 Testing QueueIT scaling with 5 tickets...")
            start_queue_message = {
                "command": "start_queue_entry",
                "params": {
                    "url": "https://example.queue-it.net/test",
                    "tickets": 5  # Should create 5-15 concurrent tasks
                },
                "id": str(int(time.time() * 1000))
            }
            
            await websocket.send(json.dumps(start_queue_message))
            response = await websocket.recv()
            start_response = json.loads(response)
            print(f"🚀 Start response: {start_response.get('type', 'unknown')}")
            
            if start_response.get('type') == 'queue_entry_started':
                task_data = start_response.get('data', {})
                print(f"📊 Task started with task_count: {task_data.get('task_count', 'unknown')}")
                print(f"🆔 Task ID: {task_data.get('task_id', 'unknown')}")
                
                # Monitor for a shorter time
                print("\n📈 Monitoring task progress...")
                for i in range(5):  # Monitor for 5 seconds
                    await asyncio.sleep(1)
                    
                    # Get queue status
                    status_message = {
                        "command": "get_queue_status",
                        "params": {},
                        "id": str(int(time.time() * 1000))
                    }
                    
                    await websocket.send(json.dumps(status_message))
                    response = await websocket.recv()
                    status_response = json.loads(response)
                    
                    if status_response.get('type') == 'queue_status':
                        data = status_response.get('data', {})
                        print(f"⏱️  [{i+1:2d}s] Status: {data.get('overall_status', 'unknown')} | "
                              f"Tasks: {data.get('task_count', 0)} | "
                              f"Passlinks: {data.get('pass_links_count', 0)}/{data.get('ticket_count', 0)}")
                        
                        # Break if completed
                        if data.get('overall_status') == 'completed':
                            print("✅ Task completed!")
                            break
                    else:
                        print(f"⚠️  [{i+1:2d}s] No active tasks")
                
                # Stop the task
                print("\n🛑 Stopping task...")
                stop_message = {
                    "command": "stop_queue_entry",
                    "params": {
                        "entry_id": task_data.get('task_id')
                    },
                    "id": str(int(time.time() * 1000))
                }
                
                await websocket.send(json.dumps(stop_message))
                response = await websocket.recv()
                stop_response = json.loads(response)
                print(f"🛑 Stop response: {stop_response.get('type', 'unknown')}")
            
            print("\n✅ All scaling tests completed!")
            
    except websockets.exceptions.ConnectionRefused:
        print("❌ Failed to connect to WebSocket server. Make sure the backend is running on localhost:8765")
    except ArithmeticError as e:
        print(f"❌ Error during testing: {e}")

if __name__ == "__main__":
    print("🧪 Testing Kasper-Q QueueIT Task Scaling Functionality")
    print("=" * 60)
    asyncio.run(test_queue_scaling())
