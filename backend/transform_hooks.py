"""
Message Transformation Hooks for Python Backend

This module provides extensible transformation hooks for message processing
in the Python backend. Hooks can be used for compression, encryption, logging,
metrics, and other cross-cutting concerns.

Author: <PERSON><PERSON>-Q Development Team
Created: 2024
Version: 1.0
"""

import json
import time
import base64
import gzip
import logging
from typing import Dict, Any, Optional
from collections import defaultdict

# Import shared schemas
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from shared.message_schemas import BaseMessage, MessageMetadata
from backend.message_protocol import MessageTransformHook

logger = logging.getLogger("TransformHooks")


class CompressionHook(MessageTransformHook):
    """Compression hook using gzip compression."""
    
    def __init__(self, compression_threshold: int = 1024):
        super().__init__("compression", priority=100)
        self.compression_threshold = compression_threshold
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Compress message payload if it exceeds threshold."""
        serialized = json.dumps(message.payload)
        
        if len(serialized) > self.compression_threshold:
            try:
                # Compress using gzip
                compressed_data = gzip.compress(serialized.encode('utf-8'))
                compressed_b64 = base64.b64encode(compressed_data).decode('ascii')
                
                # Create new message with compressed payload
                compressed_message = BaseMessage(
                    type=message.type,
                    metadata=MessageMetadata(
                        version=message.metadata.version,
                        timestamp=message.metadata.timestamp,
                        correlation_id=message.metadata.correlation_id,
                        retry_count=message.metadata.retry_count,
                        priority=message.metadata.priority
                    ),
                    payload={
                        "_compressed": True,
                        "_original_size": len(serialized),
                        "data": compressed_b64
                    },
                    error=message.error
                )
                
                # Update metadata
                compressed_message.metadata.compressed = True
                compressed_message.metadata.original_size = len(serialized)
                
                logger.debug(f"Compressed message from {len(serialized)} to {len(compressed_b64)} bytes")
                return compressed_message
                
            except ArithmeticError as e:
                logger.warning(f"Compression failed: {e}, sending uncompressed")
                return message
        
        return message
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Decompress message payload if it was compressed."""
        if hasattr(message.payload, '_compressed') and message.payload.get('_compressed'):
            try:
                # Decompress the data
                compressed_data = base64.b64decode(message.payload['data'])
                decompressed = gzip.decompress(compressed_data).decode('utf-8')
                original_payload = json.loads(decompressed)
                
                # Create new message with decompressed payload
                decompressed_message = BaseMessage(
                    type=message.type,
                    metadata=message.metadata,
                    payload=original_payload,
                    error=message.error
                )
                
                # Update metadata
                if hasattr(decompressed_message.metadata, 'compressed'):
                    decompressed_message.metadata.compressed = False
                
                logger.debug(f"Decompressed message to {len(decompressed)} bytes")
                return decompressed_message
                
            except ArithmeticError as e:
                logger.error(f"Decompression failed: {e}")
                raise ValueError("Failed to decompress message")
        
        return message


class EncryptionHook(MessageTransformHook):
    """Encryption hook for secure message transmission."""
    
    def __init__(self, encryption_key: Optional[str] = None):
        super().__init__("encryption", priority=200)
        self.encryption_key = encryption_key
    
    def set_key(self, key: str):
        """Set the encryption key."""
        self.encryption_key = key
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Encrypt message payload if key is available."""
        if not self.encryption_key:
            return message  # Skip encryption if no key
        
        try:
            serialized = json.dumps(message.payload)
            encrypted_data = self._encrypt(serialized)
            
            # Create new message with encrypted payload
            encrypted_message = BaseMessage(
                type=message.type,
                metadata=message.metadata,
                payload={
                    "_encrypted": True,
                    "data": encrypted_data
                },
                error=message.error
            )
            
            # Update metadata
            encrypted_message.metadata.encrypted = True
            
            logger.debug("Message encrypted")
            return encrypted_message
            
        except ArithmeticError as e:
            logger.warning(f"Encryption failed: {e}, sending unencrypted")
            return message
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Decrypt message payload if it was encrypted."""
        if hasattr(message.payload, '_encrypted') and message.payload.get('_encrypted'):
            if not self.encryption_key:
                raise ValueError("Cannot decrypt message: no encryption key available")
            
            try:
                decrypted_data = self._decrypt(message.payload['data'])
                original_payload = json.loads(decrypted_data)
                
                # Create new message with decrypted payload
                decrypted_message = BaseMessage(
                    type=message.type,
                    metadata=message.metadata,
                    payload=original_payload,
                    error=message.error
                )
                
                # Update metadata
                if hasattr(decrypted_message.metadata, 'encrypted'):
                    decrypted_message.metadata.encrypted = False
                
                logger.debug("Message decrypted")
                return decrypted_message
                
            except ArithmeticError as e:
                logger.error(f"Decryption failed: {e}")
                raise ValueError("Failed to decrypt message")
        
        return message
    
    def _encrypt(self, data: str) -> str:
        """Simple XOR encryption (for demonstration - use proper encryption in production)."""
        key = self.encryption_key
        result = bytearray()
        
        for i, byte in enumerate(data.encode('utf-8')):
            key_byte = ord(key[i % len(key)])
            result.append(byte ^ key_byte)
        
        return base64.b64encode(result).decode('ascii')
    
    def _decrypt(self, data: str) -> str:
        """XOR decryption (same as encryption for XOR)."""
        encrypted_bytes = base64.b64decode(data)
        key = self.encryption_key
        result = bytearray()
        
        for i, byte in enumerate(encrypted_bytes):
            key_byte = ord(key[i % len(key)])
            result.append(byte ^ key_byte)
        
        return result.decode('utf-8')


class LoggingHook(MessageTransformHook):
    """Logging hook for message tracing and debugging."""
    
    def __init__(self, log_level: str = "INFO"):
        super().__init__("logging", priority=10)
        self.log_level = getattr(logging, log_level.upper())
        self.logger = logging.getLogger("MessageLogging")
        self.logger.setLevel(self.log_level)
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Log outgoing messages."""
        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(
                f"Outgoing message: type={message.type}, "
                f"timestamp={message.metadata.timestamp}, "
                f"correlation_id={message.metadata.correlation_id}, "
                f"payload_size={len(json.dumps(message.payload))}"
            )
        return message
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Log incoming messages."""
        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(
                f"Incoming message: type={message.type}, "
                f"timestamp={message.metadata.timestamp}, "
                f"correlation_id={message.metadata.correlation_id}, "
                f"payload_size={len(json.dumps(message.payload))}"
            )
        return message


class MetricsHook(MessageTransformHook):
    """Metrics collection hook."""
    
    def __init__(self):
        super().__init__("metrics", priority=5)
        self.metrics = {
            "messages_sent": 0,
            "messages_received": 0,
            "total_bytes_sent": 0,
            "total_bytes_received": 0,
            "message_types": defaultdict(int),
            "errors": 0
        }
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Collect metrics for outgoing messages."""
        self.metrics["messages_sent"] += 1
        message_size = len(json.dumps(message.to_dict()))
        self.metrics["total_bytes_sent"] += message_size
        self.metrics["message_types"][message.type.value] += 1
        return message
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Collect metrics for incoming messages."""
        self.metrics["messages_received"] += 1
        message_size = len(json.dumps(message.to_dict()))
        self.metrics["total_bytes_received"] += message_size
        self.metrics["message_types"][message.type.value] += 1
        return message
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics."""
        return {
            **self.metrics,
            "message_types": dict(self.metrics["message_types"])
        }
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.metrics = {
            "messages_sent": 0,
            "messages_received": 0,
            "total_bytes_sent": 0,
            "total_bytes_received": 0,
            "message_types": defaultdict(int),
            "errors": 0
        }


class ValidationHook(MessageTransformHook):
    """Message validation hook."""
    
    def __init__(self):
        super().__init__("validation", priority=300)
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Validate outgoing messages."""
        self._validate_message(message)
        return message
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Validate incoming messages."""
        self._validate_message(message)
        return message
    
    def _validate_message(self, message: BaseMessage):
        """Validate message structure."""
        if not message.type:
            raise ValueError("Message missing type field")
        
        if not message.metadata:
            raise ValueError("Message missing metadata field")
        
        if message.payload is None:
            raise ValueError("Message missing payload field")
        
        if not message.metadata.timestamp:
            raise ValueError("Message metadata missing timestamp")
        
        # Validate timestamp is reasonable (not too old or in future)
        now = time.time()
        if abs(message.metadata.timestamp - now) > 300:  # 5 minutes tolerance
            logger.warning(f"Message timestamp seems incorrect: {message.metadata.timestamp}")


class RateLimitingHook(MessageTransformHook):
    """Rate limiting hook to prevent message flooding."""
    
    def __init__(self, max_messages: int = 100, window_seconds: int = 60):
        super().__init__("rate_limiting", priority=250)
        self.max_messages = max_messages
        self.window_seconds = window_seconds
        self.client_windows: Dict[str, list] = defaultdict(list)
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Check rate limits for incoming messages."""
        # Extract client ID from metadata if available
        client_id = getattr(message.metadata, 'client_id', 'unknown')
        
        now = time.time()
        window = self.client_windows[client_id]
        
        # Remove old entries outside the window
        window[:] = [timestamp for timestamp in window if timestamp > now - self.window_seconds]
        
        # Check if under limit
        if len(window) >= self.max_messages:
            raise ValueError(f"Rate limit exceeded for client {client_id}")
        
        # Add current timestamp
        window.append(now)
        
        return message
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Rate limiting doesn't apply to outgoing messages."""
        return message
    
    def get_client_stats(self) -> Dict[str, int]:
        """Get current message counts per client."""
        now = time.time()
        stats = {}
        
        for client_id, window in self.client_windows.items():
            # Count messages in current window
            recent_messages = [t for t in window if t > now - self.window_seconds]
            stats[client_id] = len(recent_messages)
        
        return stats
