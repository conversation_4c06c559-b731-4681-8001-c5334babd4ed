.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(20, 20, 24, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: var(--color-text);
  cursor: pointer;
  font-size: 16px;
  height: 40px;
  width: 40px;
  z-index: 1000;
  transition: all 0.2s ease;
}

.debug-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.connection-debug {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: rgba(20, 20, 24, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(12px);
  color: var(--color-text);
  font-size: 12px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px 12px 0 0;
}

.debug-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.debug-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  transition: color 0.2s ease;
}

.debug-close:hover {
  color: var(--color-text);
}

.debug-section {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.debug-section:last-child {
  border-bottom: none;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: var(--color-accent);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-grid,
.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.status-item,
.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
}

.label {
  color: var(--color-text-secondary);
  font-size: 11px;
}

.value {
  color: var(--color-text);
  font-weight: 500;
  font-size: 11px;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 4px;
  color: rgba(239, 68, 68, 0.9);
  font-size: 11px;
  padding: 8px;
  word-break: break-word;
}

.control-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-buttons button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: var(--color-text);
  cursor: pointer;
  font-size: 10px;
  padding: 6px 8px;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 80px;
}

.control-buttons button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.control-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-buttons button.active {
  background: rgba(34, 197, 94, 0.2);
  border-color: rgba(34, 197, 94, 0.3);
  color: rgba(34, 197, 94, 1);
}

.connection-log {
  max-height: 120px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 8px;
}

.log-entry {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
  font-size: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--color-text-secondary);
  font-family: var(--font-mono);
  min-width: 60px;
}

.log-status {
  font-weight: 600;
  min-width: 80px;
}

.log-detail {
  color: var(--color-text-secondary);
  font-style: italic;
}

/* Scrollbar styling */
.connection-debug::-webkit-scrollbar,
.connection-log::-webkit-scrollbar {
  width: 4px;
}

.connection-debug::-webkit-scrollbar-track,
.connection-log::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.connection-debug::-webkit-scrollbar-thumb,
.connection-log::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.connection-debug::-webkit-scrollbar-thumb:hover,
.connection-log::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .connection-debug {
    width: calc(100vw - 40px);
    max-width: 400px;
  }
  
  .status-grid,
  .config-grid {
    grid-template-columns: 1fr;
  }
  
  .control-buttons {
    flex-direction: column;
  }
  
  .control-buttons button {
    min-width: auto;
  }
}
