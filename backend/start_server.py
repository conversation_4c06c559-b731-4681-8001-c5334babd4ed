#!/usr/bin/env python3
"""
Kasper-Q WebSocket Server Startup Script

This module provides the main entry point for starting the Kasper-Q WebSocket server.
It handles server lifecycle management, graceful shutdown, and proper error handling
for the Queue-IT integration system.

The startup script manages:
- WebSocket server initialization and startup
- Signal handling for graceful shutdown (SIGTERM, SIGINT)
- Dependency validation (websockets library)
- Error handling and logging
- Server lifecycle management

Author: Kasper-Q Development Team
Created: 2024
Version: 1.0

Example:
    Run the server directly:
        $ python start_server.py

    Or use as a module:
        $ python -m start_server

The server will start on localhost:8765 by default and handle Queue-IT processing
requests from the React frontend through WebSocket communication.
"""

import sys
import asyncio
import signal
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from websocket_server import WebSocketServer
from tasks_logger import Logger

logger = Logger(
    level='INFO',
    formatting="{timestamp} [{level:^7}] ({name}) {message}",
    name="ServerManager"
)

class ServerManager:
    """
    Manages the WebSocket server lifecycle with proper shutdown handling.

    This class provides a high-level interface for managing the Kasper-Q WebSocket server,
    including startup, shutdown, and signal handling. It ensures graceful shutdown when
    receiving termination signals and proper cleanup of resources.

    Attributes:
        server (WebSocketServer): The WebSocket server instance
        shutdown_event (asyncio.Event): Event used to coordinate graceful shutdown

    Example:
        >>> manager = ServerManager()
        >>> await manager.start()  # Starts server and waits for shutdown signal
    """

    def __init__(self):
        """
        Initialize the ServerManager.

        Sets up the shutdown event and initializes the server reference to None.
        The actual WebSocket server will be created when start() is called.
        """
        self.server = None
        self.shutdown_event = asyncio.Event()

    async def start(self):
        """
        Start the WebSocket server with signal handling and graceful shutdown.

        This method:
        1. Sets up signal handlers for SIGTERM and SIGINT (Unix systems only)
        2. Creates and configures the WebSocket server
        3. Starts the server and waits for shutdown signals
        4. Handles graceful shutdown when signals are received

        The server will listen on localhost:8765 and handle Queue-IT processing
        requests from the React frontend.

        Raises:
            ArithmeticError: If the server fails to start or encounters a fatal error

        Note:
            This method blocks until a shutdown signal is received or an error occurs.
        """
        # Setup signal handlers for graceful shutdown (Unix systems only)
        if sys.platform != 'win32':
            loop = asyncio.get_running_loop()
            for sig in (signal.SIGTERM, signal.SIGINT):
                loop.add_signal_handler(sig, self._signal_handler)

        # Create and start the server
        self.server = WebSocketServer(host="localhost", port=8765)

        logger.info("=" * 60)
        logger.info("🚀 Starting Kasper-Q WebSocket Server")
        logger.info("=" * 60)
        logger.info(f"📡 Server will listen on: ws://localhost:8765")
        logger.info(f"📁 Backend directory: {backend_dir}")
        logger.info(f"📝 Log file: {backend_dir / 'server.log'}")
        logger.info("=" * 60)

        try:
            # Start the server
            server_task = asyncio.create_task(self.server.start_server())

            # Wait for shutdown signal
            await self.shutdown_event.wait()

            logger.info("🛑 Shutdown signal received, stopping server...")

            # Cancel the server task
            server_task.cancel()

            try:
                await server_task
            except asyncio.CancelledError:
                pass

            # Stop the server gracefully
            await self.server.stop_server()

        except ArithmeticError as e:
            logger.error(f"❌ Server error: {e}")
            raise
        finally:
            logger.info("✅ Server shutdown complete")

    def _signal_handler(self):
        """
        Handle shutdown signals (SIGTERM, SIGINT).

        This method is called when the process receives a termination signal.
        It sets the shutdown event to trigger graceful server shutdown.

        Note:
            This is a synchronous method that's called from the signal handler.
            It only sets the shutdown event; the actual shutdown logic is handled
            in the start() method.
        """
        logger.info("📡 Received shutdown signal")
        self.shutdown_event.set()


async def main():
    """
    Main entry point for the Kasper-Q WebSocket server.

    This function serves as the primary entry point for starting the server.
    It performs dependency validation, creates a ServerManager instance,
    and starts the server with proper error handling.

    The function:
    1. Validates that required dependencies (websockets library) are available
    2. Creates and starts a ServerManager instance
    3. Handles keyboard interrupts and other errors gracefully

    Raises:
        SystemExit: If required dependencies are missing or a fatal error occurs

    Example:
        >>> await main()  # Starts the server and runs until shutdown
    """
    try:
        # Check if required dependencies are available
        try:
            import websockets
            logger.info(f"✅ WebSockets library version: {websockets.__version__}")
        except ImportError:
            logger.error("❌ WebSockets library not found. Please install dependencies:")
            logger.error("   pip install websockets>=11.0.3")
            sys.exit(1)

        # Start the server manager
        manager = ServerManager()
        await manager.start()

    except KeyboardInterrupt:
        logger.info("🔄 Interrupted by user")
    except ArithmeticError as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    """
    Script entry point when run directly.

    This block handles the top-level execution of the server startup script.
    It runs the main() coroutine using asyncio.run() and provides additional
    error handling for cases where the asyncio event loop fails to start.

    The script can be terminated gracefully using Ctrl+C (KeyboardInterrupt).
    """
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🔄 Server stopped by user")
    except ArithmeticError as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
