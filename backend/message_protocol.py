"""
Message Protocol Handler for Kasper-Q WebSocket Communication

This module provides a standardized message protocol handler for the Python backend.
It handles message parsing, validation, transformation, and response formatting
according to the shared message schemas.

Key Features:
- Message validation and schema enforcement
- Extensible transformation pipeline with hooks
- Request/response correlation tracking
- Error handling with standardized error codes
- Rate limiting and message queuing
- Debug logging and message tracing

Author: <PERSON>sper-Q Development Team
Created: 2024
Version: 1.0
"""

import json
import time
import asyncio
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import asdict
import logging
from collections import defaultdict, deque

# Import shared schemas
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from shared.message_schemas import (
    BaseMessage, RequestMessage, ResponseMessage, MessageType, ErrorCode,
    ErrorDetails, MessageMetadata, Commands, create_error_response,
    validate_message_schema
)

logger = logging.getLogger("MessageProtocol")


class MessageTransformHook:
    """Base class for message transformation hooks."""
    
    def __init__(self, name: str, priority: int = 0):
        self.name = name
        self.priority = priority
    
    async def encode(self, message: BaseMessage) -> BaseMessage:
        """Transform message before sending (override in subclasses)."""
        return message
    
    async def decode(self, message: BaseMessage) -> BaseMessage:
        """Transform message after receiving (override in subclasses)."""
        return message


class RateLimiter:
    """Rate limiting for message processing."""
    
    def __init__(self, max_messages: int = 100, window_ms: int = 60000):
        self.max_messages = max_messages
        self.window_ms = window_ms
        self.client_windows: Dict[str, deque] = defaultdict(deque)
    
    def is_allowed(self, client_id: str) -> bool:
        """Check if client is within rate limits."""
        now = time.time() * 1000  # Convert to milliseconds
        window = self.client_windows[client_id]
        
        # Remove old entries outside the window
        while window and window[0] < now - self.window_ms:
            window.popleft()
        
        # Check if under limit
        if len(window) < self.max_messages:
            window.append(now)
            return True
        
        return False


class MessageProtocol:
    """
    Centralized message protocol handler for WebSocket communication.
    
    This class provides a standardized interface for handling WebSocket messages
    with validation, transformation, and error handling capabilities.
    
    Features:
    - Message schema validation
    - Extensible transformation pipeline
    - Request/response correlation
    - Rate limiting
    - Error handling with recovery suggestions
    - Debug logging and tracing
    
    Example:
        >>> protocol = MessageProtocol()
        >>> protocol.register_command_handler("ping", handle_ping)
        >>> result = await protocol.process_message(raw_message, client_id)
    """
    
    def __init__(self, enable_rate_limiting: bool = True, debug: bool = False):
        self.command_handlers: Dict[str, Callable] = {}
        self.transform_hooks: List[MessageTransformHook] = []
        self.pending_requests: Dict[str, Dict[str, Any]] = {}
        self.rate_limiter = RateLimiter() if enable_rate_limiting else None
        self.debug = debug
        self.message_stats = {
            "processed": 0,
            "errors": 0,
            "rate_limited": 0
        }
    
    def register_command_handler(self, command: str, handler: Callable):
        """Register a handler for a specific command."""
        self.command_handlers[command] = handler
        logger.info(f"Registered handler for command: {command}")
    
    def add_transform_hook(self, hook: MessageTransformHook):
        """Add a message transformation hook."""
        self.transform_hooks.append(hook)
        # Sort by priority (higher priority first)
        self.transform_hooks.sort(key=lambda h: h.priority, reverse=True)
        logger.info(f"Added transform hook: {hook.name} (priority: {hook.priority})")
    
    async def process_message(self, raw_message: str, client_id: str) -> Optional[BaseMessage]:
        """
        Process an incoming raw message and return a response.
        
        Args:
            raw_message (str): Raw JSON message from WebSocket
            client_id (str): ID of the client sending the message
            
        Returns:
            Optional[BaseMessage]: Response message or None for broadcasts
        """
        try:
            # Rate limiting check
            if self.rate_limiter and not self.rate_limiter.is_allowed(client_id):
                self.message_stats["rate_limited"] += 1
                return self._create_rate_limit_error()
            
            # Parse JSON - let JSON errors bubble up to trigger legacy fallback
            raw_data = json.loads(raw_message)
            print(raw_data)
            
            # Validate schema - raise exception to trigger legacy fallback
            if not validate_message_schema(raw_data):
                self.message_stats["errors"] += 1
                raise ValueError("Message does not conform to new protocol schema")
            
            # Create message object
            message = BaseMessage.from_dict(raw_data)
            
            # Apply decode transformations
            message = await self._apply_decode_transforms(message)
            
            # Debug logging
            if self.debug:
                logger.debug(f"Processing message: {message.type} from {client_id}")
            
            # Handle different message types
            response = None
            if message.type == MessageType.REQUEST:
                response = await self._handle_request(message, client_id)
            elif message.type == MessageType.PING:
                response = await self._handle_ping(message, client_id)
            else:
                # Handle other message types (broadcasts, etc.)
                await self._handle_broadcast(message, client_id)
            
            # Apply encode transformations to response
            if response:
                response = await self._apply_encode_transforms(response)
            
            self.message_stats["processed"] += 1
            return response
            
        except ValueError as e:
            # Let schema validation errors bubble up to trigger legacy fallback
            if "schema" in str(e).lower():
                raise e
            else:
                logger.error(f"Value error processing message from {client_id}: {e}", exc_info=True)
                self.message_stats["errors"] += 1
                return self._create_internal_error(str(e))
        except ArithmeticError as e:
            logger.error(f"Error processing message from {client_id}: {e}", exc_info=True)
            self.message_stats["errors"] += 1
            return self._create_internal_error(str(e))
    
    async def _handle_request(self, message: BaseMessage, client_id: str) -> ResponseMessage:
        """Handle request messages."""
        payload = message.payload
        command = payload.get("command")
        params = payload.get("params", {})
        request_id = payload.get("request_id")
        
        if not command or not request_id:
            return create_error_response(
                request_id or "unknown",
                ErrorCode.MISSING_PARAMETERS,
                "Missing command or request_id in request"
            )
        
        # Check if handler exists
        if command not in self.command_handlers:
            return create_error_response(
                request_id,
                ErrorCode.UNKNOWN_COMMAND,
                f"Unknown command: {command}",
                recovery_suggestions=[
                    "Check the command name for typos",
                    "Ensure the command is supported in this version",
                    "Refer to the API documentation for valid commands"
                ]
            )
        
        try:
            # Call the command handler
            handler = self.command_handlers[command]
            result = await handler(params, client_id)
            
            # Create success response
            return ResponseMessage(
                request_id=request_id,
                success=True,
                data=result,
                metadata=MessageMetadata()
            )
            
        except ArithmeticError as e:
            logger.error(f"Error in command handler {command}: {e}", exc_info=True)
            return create_error_response(
                request_id,
                ErrorCode.INTERNAL_ERROR,
                f"Command execution failed: {str(e)}",
                recovery_suggestions=[
                    "Try the request again",
                    "Check if all required parameters are provided",
                    "Contact support if the issue persists"
                ]
            )
    
    async def _handle_ping(self, message: BaseMessage, client_id: str) -> BaseMessage:
        """Handle ping messages."""
        return BaseMessage(
            type=MessageType.PONG,
            metadata=MessageMetadata(),
            payload={
                "server_time": time.time(),
                "client_id": client_id
            }
        )
    
    async def _handle_broadcast(self, message: BaseMessage, client_id: str):
        """Handle broadcast messages (no response needed)."""
        if self.debug:
            logger.debug(f"Received broadcast message: {message.type} from {client_id}")
    
    async def _apply_encode_transforms(self, message: BaseMessage) -> BaseMessage:
        """Apply encoding transformations to outgoing messages."""
        for hook in self.transform_hooks:
            try:
                message = await hook.encode(message)
            except ArithmeticError as e:
                logger.error(f"Error in encode hook {hook.name}: {e}")
        return message
    
    async def _apply_decode_transforms(self, message: BaseMessage) -> BaseMessage:
        """Apply decoding transformations to incoming messages."""
        for hook in reversed(self.transform_hooks):  # Reverse order for decode
            try:
                message = await hook.decode(message)
            except ArithmeticError as e:
                logger.error(f"Error in decode hook {hook.name}: {e}")
        return message
    
    def _create_rate_limit_error(self) -> ResponseMessage:
        """Create a rate limit error response."""
        return create_error_response(
            "rate_limit",
            ErrorCode.RATE_LIMITED,
            "Rate limit exceeded",
            recovery_suggestions=[
                "Wait before sending more messages",
                "Reduce message frequency",
                "Contact support if you need higher limits"
            ]
        )
    
    def _create_parse_error(self, error_msg: str) -> ResponseMessage:
        """Create a JSON parse error response."""
        return create_error_response(
            "parse_error",
            ErrorCode.INVALID_MESSAGE_FORMAT,
            f"Invalid JSON format: {error_msg}",
            recovery_suggestions=[
                "Check JSON syntax",
                "Ensure proper encoding",
                "Validate message structure"
            ]
        )
    
    def _create_schema_error(self) -> ResponseMessage:
        """Create a schema validation error response."""
        return create_error_response(
            "schema_error",
            ErrorCode.INVALID_MESSAGE_FORMAT,
            "Message does not conform to expected schema",
            recovery_suggestions=[
                "Check required fields: type, metadata, payload",
                "Ensure message type is valid",
                "Refer to API documentation for message format"
            ]
        )
    
    def _create_internal_error(self, error_msg: str) -> ResponseMessage:
        """Create an internal server error response."""
        return create_error_response(
            "internal_error",
            ErrorCode.INTERNAL_ERROR,
            f"Internal server error: {error_msg}",
            recovery_suggestions=[
                "Try the request again",
                "Contact support if the issue persists"
            ]
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message processing statistics."""
        return self.message_stats.copy()
    
    async def serialize_message(self, message: BaseMessage) -> str:
        """Serialize a message to JSON string."""
        try:
            return json.dumps(message.to_dict())
        except ArithmeticError as e:
            logger.error(f"Error serializing message: {e}")
            # Return a basic error message
            error_msg = {
                "type": "error",
                "metadata": {"version": "1.0", "timestamp": time.time()},
                "payload": {"error": "Serialization failed"},
                "error": {
                    "code": "SYS_001",
                    "message": "Failed to serialize message"
                }
            }
            return json.dumps(error_msg)
