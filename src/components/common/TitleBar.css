/* Title Bar Styles */
.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 38px;
  background: var(--color-card);
  border-bottom: 1px solid var(--color-border);
  padding: 0 var(--spacing-3);
  -webkit-app-region: drag;
  user-select: none;
}

.title-bar-left {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 700;
  font-size: 0.9rem;
  color: var(--accent-color);
  text-shadow: var(--shadow-purple);
}

.app-logo i {
  font-size: 1.1rem;
  animation: ghostFloat 3s ease-in-out infinite;
}

@keyframes ghostFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

.title-bar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.current-mode {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--surface-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal) ease;
}

.current-mode i {
  font-size: 0.9rem;
  color: var(--accent-color);
}

.mode-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: 0.3px;
}

.app-title {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.title-bar-right {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 0 0 auto;
  -webkit-app-region: no-drag;
}

.title-bar-button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  font-size: 0.8rem;
}

.title-bar-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.title-bar-button.close:hover {
  background-color: #ff4444;
  color: white;
}

.title-bar-button.minimize:hover {
  background-color: var(--warning-color);
  color: var(--primary-bg);
}

.title-bar-button.maximize:hover {
  background-color: var(--info-color);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .title-bar {
    padding: 0 var(--spacing-sm);
  }

  .app-title {
    display: none;
  }

  .title-bar-button {
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
  }
}

/* Minimal, luxury macOS-style Title Bar - Centered Mode Switcher */

.title-bar.macos-luxury {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 38px;
  background: #0D0D0D;
  border-bottom: 1px solid #232323;
  box-shadow: 0 1px 8px 0 rgba(30,20,50,0.04);
  padding: 0 0.5rem;
  -webkit-app-region: drag;
  user-select: none;
  position: relative;
  z-index: 1000;
}

.window-controls.macos {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
  -webkit-app-region: no-drag;
  flex: 0 0 auto;
}

.mode-switcher.center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1 1 0;
  background: rgba(44, 32, 64, 0.18);
  border-radius: 999px;
  padding: 2px 4px;
  box-shadow: 0 1px 8px 0 rgba(30,20,50,0.04);
  min-width: 220px;
  min-height: 32px;
  gap: 0;
  position: relative;
  margin: 0 24px;
  max-width: 420px;
}

.mode-pill {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.mode-pill.active {
  color: var(--color-text);
}

.mode-pill.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.mode-pill:not(.disabled):hover {
  color: var(--color-text);
}

/* Sliding indicator for selected mode (optional, can be removed for flat style) */
.mode-switcher.center {
  overflow: hidden;
}
.mode-switcher.center::before {
  display: none;
}

.title-bar-spacer {
  width: 60px;
  height: 1px;
  flex: 0 0 auto;
}

@media (max-width: 700px) {
  .title-bar.macos-luxury {
    height: 34px;
    padding: 0 0.2rem;
  }
  .mode-switcher.center {
    min-width: 140px;
    min-height: 28px;
    padding: 1px 2px;
    margin: 0 8px;
  }
  .mode-pill {
    font-size: 0.92rem;
    padding: 4px 10px;
  }
  .title-bar-spacer {
    width: 32px;
  }
}

.title-bar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
  -webkit-app-region: no-drag;
  flex: 0 0 auto;
}

.action-button {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.action-button:hover {
  color: var(--color-text);
  background: rgba(255, 255, 255, 0.1);
}

.action-button:active {
  transform: scale(0.95);
}

.action-button svg {
  width: 16px;
  height: 16px;
}

@media (max-width: 700px) {
  .title-bar.macos-luxury {
    height: 34px;
    padding: 0 0.2rem;
  }
  
  .mode-switcher.center {
    min-width: 140px;
    min-height: 28px;
    padding: 1px 2px;
    margin: 0 8px;
  }
  
  .mode-pill {
    font-size: 0.92rem;
    padding: 4px 10px;
  }
  
  .action-button {
    width: 28px;
    height: 28px;
  }
  
  .action-button svg {
    width: 14px;
    height: 14px;
  }
}
