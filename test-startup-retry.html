<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Startup Retry Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .log {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.connecting { background: #ffa500; }
        .status.connected { background: #008000; }
        .status.error { background: #ff0000; }
        .status.retrying { background: #ff8c00; }
    </style>
</head>
<body>
    <h1>WebSocket Startup Retry Test</h1>
    
    <div class="status" id="status">Disconnected</div>
    
    <button onclick="testStartupWithoutBackend()">Test Startup Without Backend</button>
    <button onclick="testStartupWithBackend()">Test Startup With Backend</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div class="log" id="log"></div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(status, className) {
            statusElement.textContent = status;
            statusElement.className = `status ${className}`;
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        class TestBackendService {
            constructor() {
                this.socket = null;
                this.isConnected = false;
                this.isConnecting = false;
                this.shouldReconnect = true;
                
                // Graceful startup configuration
                this.startupGracePeriod = 10000; // 10 seconds
                this.startupTime = Date.now();
                this.isInStartupGracePeriod = true;
                this.startupGraceTimeout = null;
                
                // Initial connection retry configuration
                this.initialConnectionFailures = 0;
                this.maxInitialConnectionFailures = 3;
                this.initialRetryDelay = 2000; // 2 seconds
                this.initialRetryTimeout = null;
                
                this._initializeStartupGracePeriod();
            }
            
            _initializeStartupGracePeriod() {
                this.startupGraceTimeout = setTimeout(() => {
                    this.isInStartupGracePeriod = false;
                    this._resetInitialConnectionFailures();
                    log('Startup grace period ended - connection errors will now be shown to user');
                }, this.startupGracePeriod);
            }
            
            isInGracePeriod() {
                return this.isInStartupGracePeriod && (Date.now() - this.startupTime) < this.startupGracePeriod;
            }
            
            _resetInitialConnectionFailures() {
                this.initialConnectionFailures = 0;
                if (this.initialRetryTimeout) {
                    clearTimeout(this.initialRetryTimeout);
                    this.initialRetryTimeout = null;
                }
            }
            
            _handleInitialConnectionFailure(error, rejectConnection) {
                this.initialConnectionFailures++;
                log(`Initial connection failure ${this.initialConnectionFailures}/${this.maxInitialConnectionFailures}: ${error.message}`);
                
                if (this.initialConnectionFailures >= this.maxInitialConnectionFailures) {
                    log('Maximum initial connection failures reached, showing error to user');
                    updateStatus('Connection Error', 'error');
                    rejectConnection(error);
                    return;
                }
                
                log(`Scheduling initial connection retry in ${this.initialRetryDelay}ms (attempt ${this.initialConnectionFailures + 1}/${this.maxInitialConnectionFailures})`);
                updateStatus(`Retrying... (${this.initialConnectionFailures}/${this.maxInitialConnectionFailures})`, 'retrying');
                
                this.initialRetryTimeout = setTimeout(async () => {
                    this.initialRetryTimeout = null;
                    
                    if (this.shouldReconnect && !this.isConnected && !this.isConnecting) {
                        log(`Executing initial connection retry attempt ${this.initialConnectionFailures + 1}`);
                        try {
                            this.isConnecting = false;
                            await this.connect();
                        } catch (retryError) {
                            log('Initial connection retry failed: ' + retryError.message);
                        }
                    }
                }, this.initialRetryDelay);
            }
            
            connect() {
                return new Promise((resolve, reject) => {
                    if (this.isConnecting || this.isConnected) {
                        if (this.isConnected) {
                            resolve();
                            return;
                        }
                        reject(new Error('Connection attempt already in progress'));
                        return;
                    }
                    
                    let connectionTimeout;
                    let connectionResolved = false;
                    
                    const cleanup = () => {
                        if (connectionTimeout) {
                            clearTimeout(connectionTimeout);
                            connectionTimeout = null;
                        }
                    };
                    
                    const resolveConnection = () => {
                        if (!connectionResolved) {
                            connectionResolved = true;
                            cleanup();
                            resolve();
                        }
                    };
                    
                    const rejectConnection = (error) => {
                        if (!connectionResolved) {
                            connectionResolved = true;
                            cleanup();
                            this.isConnecting = false;
                            reject(error);
                        }
                    };
                    
                    try {
                        this.isConnecting = true;
                        updateStatus('Connecting...', 'connecting');
                        log('Attempting to connect to WebSocket...');
                        
                        connectionTimeout = setTimeout(() => {
                            if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
                                log('Connection timeout, closing socket');
                                this.socket.close();
                                const error = new Error('Connection timeout');
                                this._handleInitialConnectionFailure(error, rejectConnection);
                            }
                        }, 5000); // 5 second timeout for testing
                        
                        this.socket = new WebSocket('ws://localhost:8765');
                        
                        this.socket.onopen = () => {
                            log('Connected to WebSocket backend');
                            this.isConnected = true;
                            this.isConnecting = false;
                            this._resetInitialConnectionFailures();
                            updateStatus('Connected', 'connected');
                            resolveConnection();
                        };
                        
                        this.socket.onclose = (event) => {
                            log(`WebSocket closed: ${event.code} ${event.reason}`);
                            
                            const wasConnected = this.isConnected;
                            this.isConnected = false;
                            
                            if (wasConnected) {
                                this.isConnecting = false;
                            }
                            
                            if (!connectionResolved) {
                                // Connection failed during initial attempt
                                const connectionError = new Error(`Connection failed: ${event.code} ${event.reason || 'Unknown reason'}`);
                                connectionError.code = event.code;
                                connectionError.reason = event.reason;
                                
                                this._handleInitialConnectionFailure(connectionError, rejectConnection);
                            }
                        };
                        
                        this.socket.onerror = (error) => {
                            log('WebSocket error: ' + error.message);
                            
                            if (!connectionResolved) {
                                const connectionError = new Error('Failed to connect to backend');
                                this._handleInitialConnectionFailure(connectionError, rejectConnection);
                            }
                        };
                        
                    } catch (error) {
                        log('Error creating WebSocket connection: ' + error.message);
                        rejectConnection(error);
                    }
                });
            }
        }
        
        let testService = null;
        
        async function testStartupWithoutBackend() {
            log('=== Testing startup without backend ===');
            testService = new TestBackendService();
            
            try {
                await testService.connect();
                log('Connection succeeded (unexpected)');
            } catch (error) {
                log('Connection failed: ' + error.message);
            }
        }
        
        async function testStartupWithBackend() {
            log('=== Testing startup with backend ===');
            testService = new TestBackendService();
            
            try {
                await testService.connect();
                log('Connection succeeded');
            } catch (error) {
                log('Connection failed: ' + error.message);
            }
        }
    </script>
</body>
</html>
