/**
 * Enhanced Backend Service using the new WebSocket Manager
 * 
 * This service provides a high-level interface for communicating with the Python backend
 * using the standardized WebSocket communication layer. It maintains backward compatibility
 * while providing enhanced features like type safety, transformation hooks, and improved
 * error handling.
 * 
 * Author: <PERSON><PERSON>-Q Development Team
 * Created: 2024
 * Version: 1.0
 */

import {
  WebSocketManager,
  ConnectionState,
  ConnectionConfig
} from './WebSocketManager';
import {
  MessageType,
  Commands,
  CommandName,
  BroadcastMessage,
  isQueueProgressUpdate,
  isConnectionEstablished,
  isConnectionRejected,
  isPongMessage
} from '../shared/messageSchemas';
import {
  CompressionHook,
  EncryptionHook,
  LoggingHook,
  MetricsHook,
  ValidationHook
} from './transformHooks';

// Type definitions for backend responses
export interface QueueEntryParams {
  url: string;
  tickets: number;
  task_id?: string;
}

export interface QueueStatus {
  task_id: string;
  overall_status: string;
  task_count: number;
  ticket_count: number;
  pass_links_count: number;
  pass_links: string[];
  url: string;
  is_running: boolean;
  start_time: number;
  completion_time?: number;
  duration: number;
  error_message?: string;
  tasks: Array<{
    task_index: number;
    status: string;
    error: string;
    duration: number;
  }>;
}

export interface ConnectionStatus {
  connected: boolean;
  server_time: number;
  uptime: number;
  connected_clients: number;
  is_primary_client: boolean;
  primary_client_authenticated: boolean;
  primary_client_connect_time?: number;
}

export interface LicenseValidationResult {
  valid: boolean;
  license_key?: string;
  error?: string;
  last_updated?: number;
}

// Event handler types
export type QueueProgressHandler = (update: any) => void;
export type ConnectionStateHandler = (state: ConnectionState, metadata?: any) => void;
export type LicenseUpdateHandler = (licenseData: any) => void;

/**
 * Enhanced Backend Service with standardized WebSocket communication
 */
export class EnhancedBackendService {
  private wsManager: WebSocketManager;
  private isInitialized = false;
  
  // Event handlers
  private queueProgressHandlers = new Set<QueueProgressHandler>();
  private connectionStateHandlers = new Set<ConnectionStateHandler>();
  private licenseUpdateHandlers = new Set<LicenseUpdateHandler>();

  constructor(config?: Partial<ConnectionConfig>) {
    this.wsManager = new WebSocketManager(config);
    this.setupEventHandlers();
    this.setupTransformHooks();
  }

  /**
   * Initialize the service and connect to the backend
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    await this.wsManager.connect();
    this.isInitialized = true;
  }

  /**
   * Disconnect from the backend
   */
  disconnect(): void {
    this.wsManager.disconnect();
    this.isInitialized = false;
  }

  /**
   * Get current connection state
   */
  getConnectionState(): ConnectionState {
    return this.wsManager.getState();
  }

  /**
   * Check if connected to backend
   */
  isConnected(): boolean {
    return this.wsManager.isConnected();
  }

  // Command methods with type safety

  /**
   * Ping the server for health check
   */
  async ping(): Promise<any> {
    return this.wsManager.sendCommand(Commands.PING);
  }

  /**
   * Get connection status from server
   */
  async getConnectionStatus(): Promise<ConnectionStatus> {
    return this.wsManager.sendCommand(Commands.GET_CONNECTION_STATUS);
  }

  /**
   * Validate license with WHOP API
   */
  async validateLicense(licenseKey: string): Promise<LicenseValidationResult> {
    return this.wsManager.sendCommand(Commands.VALIDATE_LICENSE, { license_key: licenseKey });
  }

  /**
   * Logout and clear all stored authentication data
   */
  async logout(): Promise<any> {
    return this.wsManager.sendCommand(Commands.LOGOUT, {});
  }

  /**
   * Get application settings
   */
  async getSettings(): Promise<any> {
    return this.wsManager.sendCommand(Commands.GET_SETTINGS);
  }

  /**
   * Update application settings
   */
  async updateSettings(settings: Record<string, any>): Promise<any> {
    return this.wsManager.sendCommand(Commands.UPDATE_SETTINGS, settings);
  }

  /**
   * Get documentation data
   */
  async getDocumentation(): Promise<any> {
    return this.wsManager.sendCommand(Commands.GET_DOCUMENTATION);
  }

  /**
   * Start a queue entry task
   */
  async startQueueEntry(params: QueueEntryParams): Promise<any> {
    return this.wsManager.sendCommand(Commands.START_QUEUE_ENTRY, params);
  }

  /**
   * Stop a queue entry task
   */
  async stopQueueEntry(entryId?: string): Promise<any> {
    return this.wsManager.sendCommand(Commands.STOP_QUEUE_ENTRY, { entry_id: entryId });
  }

  /**
   * Get queue status
   */
  async getQueueStatus(): Promise<QueueStatus> {
    return this.wsManager.sendCommand(Commands.GET_QUEUE_STATUS);
  }

  // Event handler registration

  /**
   * Register handler for queue progress updates
   */
  onQueueProgress(handler: QueueProgressHandler): () => void {
    this.queueProgressHandlers.add(handler);
    return () => this.queueProgressHandlers.delete(handler);
  }

  /**
   * Register handler for connection state changes
   */
  onConnectionStateChange(handler: ConnectionStateHandler): () => void {
    this.connectionStateHandlers.add(handler);
    return () => this.connectionStateHandlers.delete(handler);
  }

  /**
   * Register handler for license updates
   */
  onLicenseUpdate(handler: LicenseUpdateHandler): () => void {
    this.licenseUpdateHandlers.add(handler);
    return () => this.licenseUpdateHandlers.delete(handler);
  }

  /**
   * Get service statistics
   */
  getStats() {
    return this.wsManager.getStats();
  }

  /**
   * Enable/disable debug logging
   */
  setDebug(enabled: boolean): void {
    this.wsManager.setDebug(enabled);
  }

  /**
   * Configure rate limiting
   */
  setRateLimit(maxMessages: number, windowMs: number): void {
    this.wsManager.setRateLimit({
      maxMessages,
      windowMs,
      enabled: true
    });
  }

  /**
   * Add custom transformation hook
   */
  addTransformHook(hook: any): void {
    this.wsManager.addTransformHook(hook);
  }

  // Private methods

  private setupEventHandlers(): void {
    // Handle connection state changes
    this.wsManager.onStateChange((state, metadata) => {
      this.connectionStateHandlers.forEach(handler => {
        try {
          handler(state, metadata);
        } catch (error) {
          console.error('Error in connection state handler:', error);
        }
      });
    });

    // Handle queue progress updates
    this.wsManager.registerHandler(MessageType.QUEUE_PROGRESS_UPDATE, (message: BroadcastMessage) => {
      if (isQueueProgressUpdate(message)) {
        this.queueProgressHandlers.forEach(handler => {
          try {
            handler(message.payload);
          } catch (error) {
            console.error('Error in queue progress handler:', error);
          }
        });
      }
    });

    // Handle license updates
    this.wsManager.registerHandler(MessageType.LICENSE_UPDATED, (message: BroadcastMessage) => {
      this.licenseUpdateHandlers.forEach(handler => {
        try {
          handler(message.payload);
        } catch (error) {
          console.error('Error in license update handler:', error);
        }
      });
    });

    // Handle connection established
    this.wsManager.registerHandler(MessageType.CONNECTION_ESTABLISHED, (message: BroadcastMessage) => {
      if (isConnectionEstablished(message)) {
        console.log('Connection established:', message.payload);
      }
    });

    // Handle connection rejected
    this.wsManager.registerHandler(MessageType.CONNECTION_REJECTED, (message: BroadcastMessage) => {
      if (isConnectionRejected(message)) {
        console.warn('Connection rejected:', message.payload);
      }
    });

    // Handle pong messages
    this.wsManager.registerHandler(MessageType.PONG, (message: BroadcastMessage) => {
      if (isPongMessage(message)) {
        console.debug('Received pong:', message.payload);
      }
    });
  }

  private setupTransformHooks(): void {
    // Add validation hook (highest priority)
    this.wsManager.addTransformHook(new ValidationHook());

    // Add compression hook for large messages
    this.wsManager.addTransformHook(new CompressionHook());

    // Add metrics collection
    this.wsManager.addTransformHook(new MetricsHook());

    // Add logging hook (lowest priority)
    this.wsManager.addTransformHook(new LoggingHook('info'));
  }
}

// Create and export a singleton instance
export const enhancedBackendService = new EnhancedBackendService();

// Export for backward compatibility
export default enhancedBackendService;
