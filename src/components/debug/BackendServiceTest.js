import React, { useEffect, useState } from 'react';
import BackendService from '../../services/BackendService';

const BackendServiceTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Test BackendService methods availability
    const runTests = () => {
      const results = [];
      
      // Test 1: Check if BackendService is imported correctly
      try {
        results.push({
          test: 'BackendService Import',
          passed: BackendService !== undefined && BackendService !== null,
          details: `Type: ${typeof BackendService}, Constructor: ${BackendService.constructor?.name || 'Unknown'}`
        });
      } catch (error) {
        results.push({
          test: 'BackendService Import',
          passed: false,
          details: `Error: ${error.message}`
        });
      }

      // Test 2: Check registerConnectionStateCallback method
      try {
        const hasMethod = typeof BackendService.registerConnectionStateCallback === 'function';
        results.push({
          test: 'registerConnectionStateCallback',
          passed: hasMethod,
          details: `Type: ${typeof BackendService.registerConnectionStateCallback}`
        });
      } catch (error) {
        results.push({
          test: 'registerConnectionStateCallback',
          passed: false,
          details: `Error: ${error.message}`
        });
      }

      // Test 3: Check other required methods
      const requiredMethods = [
        'addConnectionStateCallback',
        'removeConnectionStateCallback',
        'getConnectionState',
        'getConnectionMetadata',
        'connect',
        'disconnect',
        'reconnect',
        'initialize',
        'registerHandler',
        'unregisterHandler',
        'setAutoReconnect'
      ];

      requiredMethods.forEach(methodName => {
        try {
          const hasMethod = typeof BackendService[methodName] === 'function';
          results.push({
            test: methodName,
            passed: hasMethod,
            details: `Type: ${typeof BackendService[methodName]}`
          });
        } catch (error) {
          results.push({
            test: methodName,
            passed: false,
            details: `Error: ${error.message}`
          });
        }
      });

      // Test 4: Try calling getConnectionState
      try {
        const state = BackendService.getConnectionState();
        results.push({
          test: 'getConnectionState() call',
          passed: true,
          details: `Current state: ${state}`
        });
      } catch (error) {
        results.push({
          test: 'getConnectionState() call',
          passed: false,
          details: `Error: ${error.message}`
        });
      }

      // Test 5: Try calling getConnectionMetadata
      try {
        const metadata = BackendService.getConnectionMetadata();
        results.push({
          test: 'getConnectionMetadata() call',
          passed: true,
          details: `Metadata keys: ${Object.keys(metadata).join(', ')}`
        });
      } catch (error) {
        results.push({
          test: 'getConnectionMetadata() call',
          passed: false,
          details: `Error: ${error.message}`
        });
      }

      // Test 6: Try registering a callback
      try {
        const testCallback = () => {};
        BackendService.registerConnectionStateCallback(testCallback);
        BackendService.removeConnectionStateCallback(testCallback);
        results.push({
          test: 'Callback registration',
          passed: true,
          details: 'Successfully registered and removed callback'
        });
      } catch (error) {
        results.push({
          test: 'Callback registration',
          passed: false,
          details: `Error: ${error.message}`
        });
      }

      setTestResults(results);
    };

    runTests();
  }, []);

  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;

  if (!isVisible) {
    return (
      <button 
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          background: totalTests > 0 && passedTests === totalTests ? '#22c55e' : '#ef4444',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '12px',
          zIndex: 1000
        }}
      >
        🧪 Service Test ({passedTests}/{totalTests})
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '400px',
      maxHeight: '80vh',
      background: 'rgba(20, 20, 24, 0.95)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      borderRadius: '8px',
      color: 'white',
      fontSize: '12px',
      overflow: 'auto',
      zIndex: 1000,
      padding: '16px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3 style={{ margin: 0, fontSize: '14px' }}>BackendService Test Results</h3>
        <button 
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ✕
        </button>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <strong>Summary: {passedTests}/{totalTests} tests passed</strong>
        {passedTests === totalTests ? (
          <div style={{ color: '#22c55e', marginTop: '4px' }}>
            ✅ All tests passed! BackendService is working correctly.
          </div>
        ) : (
          <div style={{ color: '#ef4444', marginTop: '4px' }}>
            ❌ Some tests failed. Check the details below.
          </div>
        )}
      </div>

      <div>
        {testResults.map((result, index) => (
          <div 
            key={index}
            style={{
              padding: '8px',
              marginBottom: '4px',
              background: result.passed ? 'rgba(34, 197, 94, 0.1)' : 'rgba(239, 68, 68, 0.1)',
              border: `1px solid ${result.passed ? 'rgba(34, 197, 94, 0.3)' : 'rgba(239, 68, 68, 0.3)'}`,
              borderRadius: '4px'
            }}
          >
            <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
              {result.passed ? '✅' : '❌'} {result.test}
            </div>
            <div style={{ fontSize: '10px', opacity: 0.8 }}>
              {result.details}
            </div>
          </div>
        ))}
      </div>

      {passedTests !== totalTests && (
        <div style={{ marginTop: '16px', padding: '8px', background: 'rgba(59, 130, 246, 0.1)', borderRadius: '4px' }}>
          <strong>Troubleshooting Tips:</strong>
          <ul style={{ margin: '4px 0', paddingLeft: '16px', fontSize: '10px' }}>
            <li>Restart the React development server</li>
            <li>Clear browser cache and reload</li>
            <li>Check browser console for additional errors</li>
            <li>Verify no circular import dependencies</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default BackendServiceTest;
