import React, { useState } from 'react';
import './AdvancedMode.css';

const AdvancedMode = () => {
  const [selectedTarget, setSelectedTarget] = useState('');
  const [scanType, setScanType] = useState('comprehensive');
  const [isScanning, setIsScanning] = useState(false);
  const [results, setResults] = useState([]);

  const scanTypes = [
    { id: 'comprehensive', name: 'Comprehensive Scan', description: 'Full vulnerability assessment' },
    { id: 'network', name: 'Network Scan', description: 'Network topology and services' },
    { id: 'web', name: 'Web Application', description: 'Web-specific vulnerabilities' },
    { id: 'wireless', name: 'Wireless Security', description: 'WiFi and wireless protocols' },
    { id: 'social', name: 'Social Engineering', description: 'Human factor analysis' }
  ];

  const handleStartScan = () => {
    if (!selectedTarget.trim()) return;
    
    setIsScanning(true);
    // Simulate scan process
    setTimeout(() => {
      setResults([
        { id: 1, type: 'High', title: 'SQL Injection Vulnerability', target: 'login.php' },
        { id: 2, type: 'Medium', title: 'Cross-Site Scripting (XSS)', target: 'search.php' },
        { id: 3, type: 'Low', title: 'Information Disclosure', target: 'robots.txt' }
      ]);
      setIsScanning(false);
    }, 3000);
  };

  return (
    <div className="advanced-mode">
      <div className="mode-header">
        <h2>
          <i className="fas fa-cogs"></i>
          Advanced Mode
        </h2>
        <p>Comprehensive penetration testing with advanced configuration options</p>
      </div>

      <div className="advanced-content">
        <div className="left-panel">
          <div className="config-section">
            <h3>Target Configuration</h3>
            <div className="input-group">
              <label>Target URL/IP</label>
              <input
                type="text"
                className="input"
                placeholder="https://example.com or ***********"
                value={selectedTarget}
                onChange={(e) => setSelectedTarget(e.target.value)}
              />
            </div>

            <div className="input-group">
              <label>Scan Type</label>
              <div className="scan-types">
                {scanTypes.map(type => (
                  <div
                    key={type.id}
                    className={`scan-type-card ${scanType === type.id ? 'selected' : ''}`}
                    onClick={() => setScanType(type.id)}
                  >
                    <h4>{type.name}</h4>
                    <p>{type.description}</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="action-buttons">
              <button
                className="btn btn-primary"
                onClick={handleStartScan}
                disabled={isScanning || !selectedTarget.trim()}
              >
                {isScanning ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Scanning...
                  </>
                ) : (
                  <>
                    <i className="fas fa-play"></i>
                    Start Scan
                  </>
                )}
              </button>
              <button className="btn btn-secondary">
                <i className="fas fa-save"></i>
                Save Config
              </button>
            </div>
          </div>
        </div>

        <div className="right-panel">
          <div className="results-section">
            <h3>Scan Results</h3>
            {isScanning ? (
              <div className="scanning-indicator">
                <div className="scan-progress">
                  <div className="progress-bar">
                    <div className="progress-fill"></div>
                  </div>
                  <p>Analyzing target security...</p>
                </div>
              </div>
            ) : results.length > 0 ? (
              <div className="results-list">
                {results.map(result => (
                  <div key={result.id} className={`result-item ${result.type.toLowerCase()}`}>
                    <div className="result-header">
                      <span className={`severity ${result.type.toLowerCase()}`}>
                        {result.type}
                      </span>
                      <h4>{result.title}</h4>
                    </div>
                    <p>Target: {result.target}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-results">
                <i className="fas fa-search"></i>
                <p>No scan results yet. Configure and start a scan to see results.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedMode;
