#!/usr/bin/env python3
"""
Kasper-Q WebSocket Server

This module implements the core WebSocket server for the Kasper-Q Queue-IT integration system.
It provides real-time communication between the React frontend and Python backend, handling
client connections, message routing, license validation, and queue task management.

Key Features:
- Single-client connection enforcement to prevent multiple instances
- Real-time Queue-IT processing status updates via WebSocket
- WHOP API license validation with automatic ping/pong health checks
- Exponential backoff retry logic for connection resilience
- Comprehensive error handling and connection state management
- Integration with Queue-IT processing modules and settings management

The server implements the following Queue-IT processing stages:
- Initializing: Setting up tasks and connections
- In Queue: Waiting in the queue system
- Processing: Actively processing queue bypass
- Generating: Generating pass links
- Complete: Successfully obtained pass links
- Failed: Error occurred during processing

Author: Kasper-Q Development Team
Created: 2024
Version: 1.0

Example:
    Start the server directly:
        >>> server = WebSocketServer(host="localhost", port=8765)
        >>> await server.start_server()

    Or use with the startup script:
        $ python start_server.py
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, Optional, Set
import websockets
from websockets.legacy.server import WebSocketServerProtocol
from websockets.exceptions import ConnectionClosed, WebSocketException
from helpers import license_validator, settings_manager
from queue_task_manager import QueueTaskManager
from queue_it.helpers import setup_logger
from message_protocol import MessageProtocol
from transform_hooks import (
    CompressionHook, EncryptionHook, LoggingHook,
    MetricsHook, ValidationHook, RateLimitingHook
)

logger = setup_logger("WebSocketServer")


class WebSocketServer:
    """
    Main WebSocket server class for Kasper-Q backend communication.

    This class implements a WebSocket server that handles real-time communication between
    the React frontend and Python backend for Queue-IT processing. It enforces single-client
    connections, manages license validation, and coordinates queue task execution.

    Key Features:
    - Single-client connection enforcement to prevent multiple app instances
    - Real-time message handling and broadcasting
    - License validation with WHOP API integration
    - Queue task management and progress updates
    - Automatic retry logic with exponential backoff
    - Comprehensive error handling and connection state management

    Attributes:
        host (str): Server host address (default: localhost)
        port (int): Server port number (default: 8765)
        clients (Set[WebSocketServerProtocol]): Set of active client connections
        client_websockets (Dict[str, WebSocketServerProtocol]): Mapping of client IDs to websockets
        message_handlers (Dict[str, callable]): Registered message handlers
        server: WebSocket server instance
        running (bool): Server running state

        # Single-client enforcement attributes
        primary_client_id (Optional[str]): ID of the primary (allowed) client
        primary_client_websocket (Optional[WebSocketServerProtocol]): Primary client's websocket
        primary_client_authenticated (bool): Authentication status of primary client
        primary_client_connect_time (Optional[float]): Timestamp when primary client connected

        # Task management
        active_tasks (Dict[str, QueueTaskManager]): Active queue processing tasks
        license_validator (LicenseValidator): WHOP API license validation handler
        settings_manager (SettingsManager): Application settings management
        queue_task_manager (QueueTaskManager): Queue processing task coordinator

    Example:
        >>> server = WebSocketServer(host="localhost", port=8765)
        >>> await server.start_server()
        >>> # Server runs until stopped
        >>> await server.stop_server()
    """

    def __init__(self, host: str = "localhost", port: int = 8765):
        """
        Initialize the WebSocket server with single-client enforcement.

        Args:
            host (str): The host address to bind the server to. Defaults to "localhost".
            port (int): The port number to bind the server to. Defaults to 8765.

        Note:
            The server is initialized but not started. Call start_server() to begin
            accepting connections.
        """
        self.host = host
        self.port = port
        self.clients: Set[WebSocketServerProtocol] = set()
        self.client_websockets: Dict[str, WebSocketServerProtocol] = {}  # Map client_id to websocket
        self.message_handlers: Dict[str, callable] = {}
        self.server = None
        self.running = False

        # Single-client connection enforcement
        self.primary_client_id: Optional[str] = None
        self.primary_client_websocket: Optional[WebSocketServerProtocol] = None
        self.primary_client_authenticated: bool = False
        self.primary_client_connect_time: Optional[float] = None

        # Queue task management
        self.active_tasks: Dict[str, QueueTaskManager] = {}

        # Initialize managers
        self.queue_task_manager = QueueTaskManager()
        self.queue_task_manager.set_websocket_server(self)

        # Initialize message protocol with transformation hooks
        self.message_protocol = MessageProtocol(enable_rate_limiting=True, debug=False)
        self._setup_transform_hooks()

        # Register default message handlers
        self._register_handlers()

        logger.info(f"WebSocket server initialized on {host}:{port}")
        logger.info(f"🔒 SINGLE-CLIENT: Single-client connection enforcement enabled")
        logger.info(f"📨 MESSAGE-PROTOCOL: Standardized message protocol enabled")

    def _setup_transform_hooks(self):
        """Setup message transformation hooks for the protocol."""
        # Add validation hook (highest priority)
        self.message_protocol.add_transform_hook(ValidationHook())

        # Add rate limiting hook
        self.message_protocol.add_transform_hook(RateLimitingHook(max_messages=100, window_seconds=60))

        # Add encryption hook (if needed)
        # encryption_hook = EncryptionHook()
        # self.message_protocol.add_transform_hook(encryption_hook)

        # Add compression hook
        self.message_protocol.add_transform_hook(CompressionHook(compression_threshold=1024))

        # Add metrics collection hook
        self.metrics_hook = MetricsHook()
        self.message_protocol.add_transform_hook(self.metrics_hook)

        # Add logging hook (lowest priority)
        self.message_protocol.add_transform_hook(LoggingHook(log_level="INFO"))

        logger.info("Message transformation hooks configured")

    def _is_primary_client_stale(self) -> bool:
        """
        Check if the primary client connection appears to be stale.

        This method validates that the primary client connection is still valid by checking:
        1. Primary client ID and websocket exist
        2. Websocket is still in the active clients set
        3. Client ID mapping still exists

        Returns:
            bool: True if the primary client connection is stale, False otherwise

        Note:
            A stale connection indicates that the primary client has disconnected
            but the cleanup process hasn't completed yet.
        """
        if not self.primary_client_id or not self.primary_client_websocket:
            return True

        # Check if the websocket is still in our active clients
        if self.primary_client_websocket not in self.clients:
            logger.warning(f"🔍 STALE: Primary client {self.primary_client_id} websocket not in active clients")
            return True

        # Check if the client_id mapping still exists
        if self.primary_client_id not in self.client_websockets:
            logger.warning(f"🔍 STALE: Primary client {self.primary_client_id} not in client mapping")
            return True

        return False

    def _clear_stale_primary_client(self):
        """
        Clear primary client status if it appears to be stale.

        This method performs cleanup of stale primary client connections by:
        1. Checking if the primary client is stale
        2. Clearing all primary client references
        3. Resetting authentication status

        This allows new clients to connect when the previous primary client
        has disconnected without proper cleanup.
        """
        if self._is_primary_client_stale():
            logger.info(f"🧹 STALE: Clearing stale primary client {self.primary_client_id}")
            self.primary_client_id = None
            self.primary_client_websocket = None
            self.primary_client_authenticated = False
            self.primary_client_connect_time = None

    async def _process_request_safe(self, path, request_headers):
        """Safe request processor to handle WebSocket handshake issues with enhanced error handling."""
        try:
            # Handle different websockets library versions
            # In newer versions, request_headers might be a different object type
            def safe_get_header(headers, key, default='Unknown'):
                try:
                    if hasattr(headers, 'get'):
                        return headers.get(key, default)
                    elif hasattr(headers, key.lower()):
                        return getattr(headers, key.lower(), default)
                    elif hasattr(headers, '__getitem__'):
                        return headers.get(key, default)
                    else:
                        # Try to access as attribute
                        return getattr(headers, key.replace('-', '_').lower(), default)
                except:
                    return default

            # Log incoming connection attempt with more details
            origin = safe_get_header(request_headers, 'Origin')
            user_agent = safe_get_header(request_headers, 'User-Agent')
            host = safe_get_header(request_headers, 'Host')
            connection = safe_get_header(request_headers, 'Connection')
            upgrade = safe_get_header(request_headers, 'Upgrade')

            logger.info(f"🔗 HANDSHAKE: WebSocket connection attempt - Origin: {origin}, Host: {host}")
            logger.debug(f"🔗 HANDSHAKE: User-Agent: {user_agent}")
            logger.debug(f"🔗 HANDSHAKE: Connection: {connection}, Upgrade: {upgrade}")

            # Basic validation (be more lenient for compatibility)
            if upgrade != 'Unknown' and upgrade.lower() != 'websocket':
                logger.warning(f"🚫 HANDSHAKE: Invalid upgrade header: {upgrade}")
                return self._create_http_response(400, "Bad Request - Invalid Upgrade Header")

            logger.info("✅ HANDSHAKE: WebSocket headers validated successfully")
            # Return None to accept the connection
            return None

        except ArithmeticError as e:
            logger.error(f"💥 HANDSHAKE: Error processing WebSocket request: {e}", exc_info=True)
            return None  # Accept connection on error to avoid blocking

    def _create_http_response(self, status_code: int, reason: str):
        """Create a proper HTTP response for WebSocket handshake rejection."""
        try:
            # Try different import paths for different websockets versions
            try:
                from websockets.legacy.http import Response
            except ImportError:
                try:
                    from websockets.http import Response
                except ImportError:
                    # Fallback - just return None to accept connection
                    logger.warning("Could not import Response class, accepting connection")
                    return None

            body = f"{reason}\r\n".encode('utf-8')
            return Response(status_code, reason, body)
        except ArithmeticError as e:
            logger.error(f"Error creating HTTP response: {e}")
            # Fallback to accepting connection
            return None

    async def _reject_client_connection(self, websocket: WebSocketServerProtocol, client_id: str):
        """Safely reject a client connection with proper error handling."""
        try:
            # Check if connection is still open
            if self._is_websocket_closed(websocket):
                logger.debug(f"🚫 REJECT: Client {client_id} connection already closed")
                return

            rejection_message = {
                'type': 'connection_rejected',
                'reason': 'single_instance_policy',
                'message': 'Only one instance of Kasper-Q can be active at a time. Please close any other running instances and try again.',
                'primary_client_id': self.primary_client_id,
                'primary_client_connected_since': self.primary_client_connect_time,
                'timestamp': time.time()
            }

            # Send rejection message with timeout
            try:
                await asyncio.wait_for(
                    websocket.send(json.dumps(rejection_message)),
                    timeout=2.0
                )
                logger.info(f"📤 REJECT: Sent rejection message to client {client_id}")
            except asyncio.TimeoutError:
                logger.warning(f"⏰ REJECT: Timeout sending rejection message to client {client_id}")
            except ConnectionClosed:
                logger.debug(f"🔌 REJECT: Client {client_id} disconnected before rejection message sent")
            except ArithmeticError as e:
                logger.warning(f"⚠️ REJECT: Error sending rejection message to client {client_id}: {e}")

            # Close the connection with a brief delay
            try:
                await asyncio.sleep(0.1)
                if not self._is_websocket_closed(websocket):
                    await websocket.close(code=4001, reason="Single instance policy - another client is already connected")
                    logger.info(f"🔒 REJECT: Closed connection for client {client_id}")
            except ArithmeticError as e:
                logger.warning(f"⚠️ REJECT: Error closing connection for client {client_id}: {e}")

        except ArithmeticError as e:
            logger.error(f"💥 REJECT: Unexpected error rejecting client {client_id}: {e}", exc_info=True)

    def _register_handlers(self):
        """Register default message handlers for the WebSocket server."""
        # Register handlers with the new message protocol
        self.message_protocol.register_command_handler('ping', self._handle_ping)
        self.message_protocol.register_command_handler('get_documentation', self._handle_get_documentation)
        self.message_protocol.register_command_handler('get_settings', self._handle_get_settings)
        self.message_protocol.register_command_handler('refresh_documentation', self._handle_refresh_documentation)
        self.message_protocol.register_command_handler('refresh_settings', self._handle_refresh_settings)
        self.message_protocol.register_command_handler('update_settings', self._handle_update_settings)
        self.message_protocol.register_command_handler('get_connection_status', self._handle_get_connection_status)
        self.message_protocol.register_command_handler('start_queue_entry', self._handle_start_queue_entry)
        self.message_protocol.register_command_handler('stop_queue_entry', self._handle_stop_queue_entry)
        self.message_protocol.register_command_handler('get_queue_status', self._handle_get_queue_status)
        self.message_protocol.register_command_handler('validate_license', self._handle_validate_license)
        self.message_protocol.register_command_handler('logout', self._handle_logout)
        self.message_protocol.register_command_handler('test_webhook', self._handle_test_webhook)

        # Keep legacy handlers for backward compatibility
        self.message_handlers.update({
            'ping': self._handle_ping,
            'get_documentation': self._handle_get_documentation,
            'get_settings': self._handle_get_settings,
            'refresh_documentation': self._handle_refresh_documentation,
            'refresh_settings': self._handle_refresh_settings,
            'update_settings': self._handle_update_settings,
            'get_connection_status': self._handle_get_connection_status,
            'start_queue_entry': self._handle_start_queue_entry,
            'stop_queue_entry': self._handle_stop_queue_entry,
            'get_queue_status': self._handle_get_queue_status,
            'validate_license': self._handle_validate_license,
            'logout': self._handle_logout,
            'test_webhook': self._handle_test_webhook,
        })
    
    async def start_server(self):
        """
        Start the WebSocket server and begin accepting connections.

        This method initializes and starts the WebSocket server with the following configuration:
        - Ping/pong health checks every 30 seconds with 10-second timeout
        - 5-second close timeout for graceful disconnection
        - 1MB maximum message size limit
        - 32 message queue limit per connection
        - Compression disabled for better performance

        The server enforces single-client connections and handles all WebSocket
        communication for Queue-IT processing.

        Raises:
            ArithmeticError: If the server fails to start or bind to the specified port

        Note:
            This method blocks until the server is stopped or encounters an error.
        """
        try:
            # Create a wrapper function that matches the expected signature with enhanced error handling
            async def handler(websocket, path=None):
                try:
                    await self._handle_client(websocket)
                except ConnectionClosed as e:
                    logger.debug(f"Client connection closed during handling: {e}")
                except WebSocketException as e:
                    logger.warning(f"WebSocket exception during client handling: {e}")
                except ArithmeticError as e:
                    logger.error(f"Unexpected error in client handler: {e}", exc_info=True)

            self.server = await websockets.serve(
                handler,
                self.host,
                self.port,
                ping_interval=2,      # Send ping every 2 seconds for responsive health checks
                ping_timeout=10,      # Wait 10 seconds for pong response
                close_timeout=5,      # Wait 5 seconds for graceful close
                max_size=10**6,       # 1MB max message size
                max_queue=32,         # Max queued messages per connection
                compression=None      # Disable compression for better performance
                # Removed process_request to avoid handshake compatibility issues
            )
            self.running = True
            logger.info(f"WebSocket server started on ws://{self.host}:{self.port}")
            logger.info(f"Server configuration: ping_interval=2s, ping_timeout=10s, close_timeout=5s")

            # Keep the server running until stopped
            await self.server.wait_closed()

        except ArithmeticError as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            raise

    async def stop_server(self):
        """
        Stop the WebSocket server and close all connections gracefully.

        This method performs a graceful shutdown by:
        1. Setting the running flag to False
        2. Closing all active client connections
        3. Closing the server socket
        4. Waiting for all connections to close

        The method uses asyncio.gather with return_exceptions=True to ensure
        all connections are closed even if some fail during the process.
        """
        if self.server:
            self.running = False

            # Close all client connections gracefully
            if self.clients:
                await asyncio.gather(
                    *[client.close() for client in self.clients.copy()],
                    return_exceptions=True
                )

            # Close the server socket
            self.server.close()
            await self.server.wait_closed()
            logger.info("WebSocket server stopped")
    
    def _is_websocket_closed(self, websocket):
        """Check if websocket is closed, handling different websockets library versions."""
        try:
            # Try different ways to check if websocket is closed
            if hasattr(websocket, 'closed'):
                return websocket.closed
            elif hasattr(websocket, 'state'):
                # In newer versions, check state
                from websockets.protocol import State
                return websocket.state == State.CLOSED
            elif hasattr(websocket, 'close_code'):
                # If close_code is set, connection is closed
                return websocket.close_code is not None
            else:
                # Fallback - assume connection is open
                return False
        except ArithmeticError as e:
            logger.debug(f"Error checking websocket state: {e}")
            # Assume connection is open if we can't determine state
            return False

    async def _handle_client(self, websocket: WebSocketServerProtocol):
        """Handle a new client connection with single-client enforcement and enhanced error handling."""
        client_id = str(uuid.uuid4())[:8]
        remote_address = getattr(websocket, 'remote_address', 'Unknown')

        logger.info(f"🔌 CLIENT: {client_id} attempting to connect from {remote_address}")

        # Validate WebSocket connection state
        if self._is_websocket_closed(websocket):
            logger.warning(f"🚫 CLIENT: {client_id} connection already closed during handshake")
            return

        # Clear any stale primary client before checking
        self._clear_stale_primary_client()

        # Check if we already have a primary client
        if self.primary_client_id is not None:
            logger.warning(f"🚫 REJECTED: Client {client_id} connection attempt - Primary client {self.primary_client_id} already active")
            await self._reject_client_connection(websocket, client_id)
            return

        # Validate connection is still open before proceeding
        if self._is_websocket_closed(websocket):
            logger.warning(f"🚫 CLIENT: {client_id} connection closed before acceptance")
            return

        try:
            # Accept this client as the primary client
            self.primary_client_id = client_id
            self.primary_client_websocket = websocket
            self.primary_client_authenticated = False
            self.primary_client_connect_time = time.time()

            # Add client to tracking
            self.clients.add(websocket)
            self.client_websockets[client_id] = websocket

            logger.info(f"✅ PRIMARY: Client {client_id} accepted as primary client")

            # Attempt auto-login with stored license key
            await self._attempt_auto_login(client_id)

            # Send welcome message with retry logic
            welcome_sent = False
            for attempt in range(3):
                try:
                    await self._send_message(websocket, {
                        'type': 'connection_established',
                        'client_id': client_id,
                        'server_time': time.time(),
                        'message': 'Connected to Kasper-Q backend'
                    })
                    welcome_sent = True
                    break
                except ArithmeticError as e:
                    logger.warning(f"Failed to send welcome message to {client_id}, attempt {attempt + 1}: {e}")
                    if attempt < 2:
                        await asyncio.sleep(0.1)

            if not welcome_sent:
                logger.error(f"Failed to send welcome message to {client_id} after 3 attempts")
                return

            # Handle incoming messages with enhanced error handling
            try:
                async for message in websocket:
                    try:
                        # Validate connection is still open
                        if self._is_websocket_closed(websocket):
                            logger.warning(f"🔌 CLIENT: {client_id} connection closed during message processing")
                            break

                        await self._process_message(websocket, message, client_id)
                    except ConnectionClosed as e:
                        logger.info(f"🔌 CLIENT: {client_id} disconnected during message processing: {e}")
                        break
                    except WebSocketException as e:
                        logger.warning(f"⚠️ CLIENT: WebSocket error processing message from {client_id}: {e}")
                        # Continue processing other messages for recoverable errors
                        continue
                    except ArithmeticError as e:
                        logger.error(f"💥 CLIENT: Error processing message from {client_id}: {e}", exc_info=True)
                        # Send error response to client if possible
                        try:
                            if not self._is_websocket_closed(websocket):
                                await self._send_message(websocket, {
                                    'type': 'error',
                                    'error': 'Message processing failed',
                                    'timestamp': time.time()
                                })
                        except:
                            pass  # Ignore errors when sending error response
                        continue

            except ConnectionClosed as e:
                logger.info(f"🔌 CLIENT: {client_id} connection closed during message loop: {e}")
            except WebSocketException as e:
                logger.warning(f"⚠️ CLIENT: WebSocket exception in message loop for {client_id}: {e}")
            except ArithmeticError as e:
                logger.error(f"💥 CLIENT: Unexpected error in message loop for {client_id}: {e}", exc_info=True)

        except ConnectionClosed as e:
            logger.info(f"🔌 CLIENT: {client_id} disconnected: {e}")
        except WebSocketException as e:
            logger.warning(f"⚠️ CLIENT: WebSocket error for client {client_id}: {e}")
        except ArithmeticError as e:
            logger.error(f"💥 CLIENT: Unexpected error for client {client_id}: {e}", exc_info=True)
        finally:
            # Clean up client tracking
            self.clients.discard(websocket)
            self.client_websockets.pop(client_id, None)

            # Handle primary client disconnection
            if client_id == self.primary_client_id:
                logger.info(f"🔓 PRIMARY: Primary client {client_id} disconnected - clearing primary client status")
                self.primary_client_id = None
                self.primary_client_websocket = None
                self.primary_client_authenticated = False
                self.primary_client_connect_time = None
                logger.info(f"🔓 PRIMARY: Primary client slot is now available for new connections")

            logger.info(f"🧹 Client {client_id} cleanup completed")

            # Clean up any active tasks for this client
            try:
                if (self.queue_task_manager.client_id == client_id and
                    self.queue_task_manager.is_running):
                    logger.info(f"Stopping tasks for disconnected client {client_id}")
                    self.queue_task_manager.stop_all_tasks()

            except ArithmeticError as e:
                logger.error(f"Error during task cleanup for client {client_id}: {e}")
    
    async def _process_message(self, websocket: WebSocketServerProtocol, message: str, client_id: str):
        """Process an incoming message from a client using the new message protocol."""
        try:
            # Try new message protocol first
            response_message = await self.message_protocol.process_message(message, client_id)

            if response_message:
                # Serialize and send the response
                response_json = await self.message_protocol.serialize_message(response_message)
                await self._send_raw_message(websocket, response_json)

        except (ValueError, json.JSONDecodeError, KeyError) as protocol_error:
            logger.debug(f"New protocol failed, trying legacy: {protocol_error}")
            # Fallback to legacy message processing
            await self._process_message_legacy(websocket, message, client_id)

    async def _process_message_legacy(self, websocket: WebSocketServerProtocol, message: str, client_id: str):
        """Legacy message processing for backward compatibility."""
        try:
            data = json.loads(message)
            command = data.get('command')
            params = data.get('params', {})
            request_id = data.get('id')

            logger.debug(f"Received legacy command '{command}' from client {client_id}")

            if command in self.message_handlers:
                response = await self.message_handlers[command](params, client_id)

                # Send response back to client
                if request_id:
                    response['id'] = request_id

                await self._send_message(websocket, response)
            else:
                # Unknown command
                error_response = {
                    'type': 'error',
                    'error': f"Unknown command: {command}",
                    'id': request_id
                }
                await self._send_message(websocket, error_response)

        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON received from client {client_id}")
            await self._send_message(websocket, {
                'type': 'error',
                'error': 'Invalid JSON format'
            })
        except ArithmeticError as e:
            logger.error(f"Error processing legacy message from client {client_id}: {e}")
            await self._send_message(websocket, {
                'type': 'error',
                'error': str(e)
            })
    
    async def _send_message(self, websocket: WebSocketServerProtocol, data: Dict[str, Any]):
        """Send a message to a specific client with enhanced error handling."""
        try:
            # Check if websocket is still open
            if self._is_websocket_closed(websocket):
                logger.warning("⚠️ SEND: Cannot send message - WebSocket connection is closed")
                return False

            # Add timestamp if not present
            if 'timestamp' not in data:
                data['timestamp'] = time.time()

            # Serialize message with error handling
            try:
                message = json.dumps(data)
            except (TypeError, ValueError) as e:
                logger.error(f"💥 SEND: JSON serialization error: {e}")
                # Try to send a simplified error message instead
                try:
                    error_msg = json.dumps({
                        'type': 'error',
                        'error': 'Failed to serialize message',
                        'timestamp': time.time()
                    })
                    await websocket.send(error_msg)
                except:
                    pass
                return False

            # Send with timeout to prevent blocking
            try:
                await asyncio.wait_for(websocket.send(message), timeout=5.0)
                return True
            except asyncio.TimeoutError:
                logger.warning("⏰ SEND: Timeout sending message")
                return False
            except ConnectionClosed as e:
                logger.info(f"🔌 SEND: Connection closed while sending message: {e}")
                return False

        except WebSocketException as e:
            logger.warning(f"⚠️ SEND: WebSocket error: {e}")
            return False
        except ArithmeticError as e:
            logger.error(f"💥 SEND: Failed to send message: {e}", exc_info=True)
            return False

    async def _send_raw_message(self, websocket: WebSocketServerProtocol, message: str):
        """Send a raw JSON message string to a client."""
        try:
            # Check if websocket is still open
            if self._is_websocket_closed(websocket):
                logger.warning("⚠️ SEND: Cannot send raw message - WebSocket connection is closed")
                return False

            # Send with timeout to prevent blocking
            try:
                await asyncio.wait_for(websocket.send(message), timeout=5.0)
                return True
            except asyncio.TimeoutError:
                logger.warning("⏰ SEND: Timeout sending raw message")
                return False
            except ConnectionClosed as e:
                logger.info(f"🔌 SEND: Connection closed while sending raw message: {e}")
                return False

        except WebSocketException as e:
            logger.warning(f"⚠️ SEND: WebSocket error sending raw message: {e}")
            return False
        except ArithmeticError as e:
            logger.error(f"💥 SEND: Failed to send raw message: {e}", exc_info=True)
            return False
    
    async def broadcast_message(self, data: Dict[str, Any]):
        """Broadcast a message to all connected clients."""
        if self.clients:
            message = json.dumps(data)
            await asyncio.gather(
                *[client.send(message) for client in self.clients.copy()],
                return_exceptions=True
            )

    async def broadcast_to_client(self, client_id: str, data: Dict[str, Any]):
        """Send a message to a specific client by client_id."""
        websocket = self.client_websockets.get(client_id)
        if websocket:
            try:
                await self._send_message(websocket, data)
            except ArithmeticError as e:
                logger.error(f"Failed to send message to client {client_id}: {e}")
        else:
            logger.warning(f"Client {client_id} not found for message broadcast")

    async def _attempt_auto_login(self, client_id: str):
        """
        Attempt automatic login using stored license key.

        This method checks for a stored license key in settings.json and validates it
        with the WHOP API. If valid, it automatically authenticates the user.

        Args:
            client_id (str): ID of the client to authenticate
        """
        try:
            logger.info(f"🔐 AUTO-LOGIN: Attempting auto-login for client {client_id}")

            # Check if there's a stored license key
            stored_license_key = settings_manager.get_stored_license_key()
            if not stored_license_key:
                logger.info(f"🔐 AUTO-LOGIN: No stored license key found for client {client_id}")
                return

            # Validate license format first
            if not settings_manager.has_valid_stored_license():
                logger.warning(f"🔐 AUTO-LOGIN: Invalid license format in storage for client {client_id}, clearing")
                settings_manager.clear_authentication_data()
                return

            logger.info(f"🔐 AUTO-LOGIN: Found stored license key for client {client_id}: {stored_license_key[:8]}...")

            # Validate with WHOP API
            validation_result = license_validator.validate_license(stored_license_key)
            validation_result["last_updated"] = time.time()

            if 'error' in validation_result:
                logger.warning(f"🔐 AUTO-LOGIN: Stored license validation failed for client {client_id}: {validation_result['error']}")

                # Clear invalid license data
                settings_manager.clear_authentication_data()

                # Notify client of authentication failure
                await self.broadcast_message({
                    'type': 'authentication_failure',
                    'reason': 'stored_license_invalid',
                    'message': 'Stored license is no longer valid. Please log in again.',
                    'clear_storage': True
                })
                return

            # License is valid - update settings and authenticate
            settings_manager.update_settings(validation_result)
            self.primary_client_authenticated = True

            logger.info(f"🔐 AUTO-LOGIN: Successfully authenticated client {client_id} with stored license")

            # Notify client of successful auto-login
            await self.broadcast_message({
                'type': 'auto_login_success',
                'data': validation_result,
                'message': 'Automatically logged in with stored license'
            })

            # Broadcast license update to all clients
            await self.broadcast_message({
                'type': 'license_updated',
                'data': settings_manager.settings
            })

        except Exception as e:
            logger.error(f"🔐 AUTO-LOGIN: Error during auto-login for client {client_id}: {e}")
            # Don't fail the connection, just log the error

    # Message Handlers
    async def _handle_ping(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle ping messages and validate license with WHOP API.

        This method implements the health check mechanism with automatic license validation.
        It validates the license every 30 seconds to ensure continued access while responding
        to ping requests every 2 seconds for responsive connection monitoring.

        Args:
            params (Dict[str, Any]): Ping message parameters (unused)
            client_id (str): ID of the client sending the ping (unused)

        Returns:
            Dict[str, Any]: Pong response with server and license status, or error details

        Note:
            License validation is performed automatically during ping/pong cycles to ensure
            real-time validation without requiring separate API calls from the frontend.
            With 2-second ping intervals, this provides responsive connection monitoring
            while maintaining reasonable license validation frequency.
        """
        try:
            # Check license status from settings
            settings = settings_manager.settings
            license_data = settings.get('license', {})
            license_key = license_data.get('license_key')

            if not license_key:
                logger.warning(f"No license key found for client {client_id}")
                return {
                    'type': 'pong',
                    'server_status': 'running',
                    'license_status': 'invalid',
                }

            # Validate with WHOP API if more than 30 seconds since last validation
            # This balances responsiveness with API rate limiting
            if time.time() - license_data.get('last_updated', 0) > 30:
                logger.debug(f"Performing license validation during ping from client {client_id}")
                result = license_validator.validate_license(license_key)
                result["last_updated"] = time.time()
                if 'error' in result:
                    logger.warning(f"License validation failed during ping: {result['error']}")
                    return {
                        'type': 'pong',
                        'server_status': 'running',
                        'license_status': 'invalid',
                        'license_error': result['error']
                    }
                # Update settings with new license data
                settings_manager.update_settings(result)
                logger.debug(f"License validation successful during ping from client {client_id}")

            return {
                'type': 'pong',
                'server_status': 'running',
                'license_status': 'valid',
                'license_data': license_data,  # Include license data for auto-authentication
                'timestamp': time.time()
            }

        except ArithmeticError as e:
            logger.error(f"Error handling ping: {e}")
            return {
                'type': 'pong',
                'server_status': 'error',
                'error': str(e)
            }

    async def _handle_get_connection_status(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle connection status requests from clients.

        This method provides comprehensive connection status information including
        server state, client information, and primary client details.

        Args:
            params (Dict[str, Any]): Request parameters (unused)
            client_id (str): ID of the requesting client

        Returns:
            Dict[str, Any]: Connection status information including:
                - connected: Always True for active connections
                - server_time: Current server timestamp
                - uptime: Server uptime (TODO: implement actual calculation)
                - connected_clients: Number of active client connections
                - is_primary_client: Whether this client is the primary client
                - primary_client_authenticated: Authentication status of primary client
                - primary_client_connect_time: When primary client connected
        """
        return {
            'type': 'connection_status',
            'connected': True,
            'server_time': time.time(),
            'uptime': time.time(),  # TODO: Calculate actual uptime
            'connected_clients': len(self.clients),
            'is_primary_client': client_id == self.primary_client_id,
            'primary_client_authenticated': self.primary_client_authenticated,
            'primary_client_connect_time': self.primary_client_connect_time
        }
    async def _handle_get_documentation(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """Handle documentation content requests."""
        return {
            "type": "documentation_data",
            "data": settings_manager.settings["documentation"]
        }
    
    async def _handle_get_settings(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """Handle settings data requests."""
        # If mroe than 10 seconds have passed from last load, re-load them
        return {
            "type": "settings_data",
            "data": settings_manager.settings
        }

    async def _handle_refresh_documentation(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle documentation refresh requests when menu is opened.

        This method is called when the user opens the Documentation menu to ensure
        the latest documentation content is fetched from the backend. It forces
        a refresh of the documentation data and returns the updated content.

        Args:
            params (Dict[str, Any]): Request parameters (unused for documentation refresh)
            client_id (str): ID of the client requesting the refresh

        Returns:
            Dict[str, Any]: Response containing:
                - type: 'documentation_refreshed'
                - data: Updated documentation content from settings_manager
                - timestamp: When the refresh occurred
        """
        try:
            logger.info(f"Documentation refresh requested by client {client_id}")

            # Force refresh of settings data to get latest documentation
            # This could include fetching from external APIs, updating from files, etc.
            
            # Get the refreshed documentation data
            documentation_data = settings_manager.settings.get("documentation", [])

            logger.debug(f"Documentation refreshed with {len(documentation_data)} items")

            return {
                "type": "documentation_refreshed",
                "data": documentation_data,
                "timestamp": time.time()
            }

        except ArithmeticError as e:
            logger.error(f"Error refreshing documentation for client {client_id}: {e}")
            return {
                "type": "error",
                "error": f"Failed to refresh documentation: {str(e)}"
            }

    async def _handle_refresh_settings(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle settings refresh requests when menu is opened.

        This method is called when the user opens the Settings menu to ensure
        the latest settings data is fetched from the backend. It forces a refresh
        of all settings data including license information, webhook configuration,
        balance data, and other user preferences.

        Args:
            params (Dict[str, Any]): Request parameters (unused for settings refresh)
            client_id (str): ID of the client requesting the refresh

        Returns:
            Dict[str, Any]: Response containing:
                - type: 'settings_refreshed'
                - data: Updated settings data from settings_manager
                - timestamp: When the refresh occurred
        """
        try:
            logger.info(f"Settings refresh requested by client {client_id}")

            # Force refresh of settings data from storage and external sources
            # This could include re-reading from encrypted files, validating license, etc.
            refreshed_settings = settings_manager.update_settings({})  # Trigger refresh

            logger.debug(f"Settings refreshed for client {client_id}")

            return {
                "type": "settings_refreshed",
                "data": refreshed_settings,
                "timestamp": time.time()
            }

        except ArithmeticError as e:
            logger.error(f"Error refreshing settings for client {client_id}: {e}")
            return {
                "type": "error",
                "error": f"Failed to refresh settings: {str(e)}"
            }
    async def _handle_update_settings(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle settings update requests with actual persistence.

        This method processes settings updates from the frontend, including:
        - Webhook URL updates with validation
        - Other application settings
        - Real-time persistence to settings.json

        Args:
            params (Dict[str, Any]): Settings update parameters
            client_id (str): ID of the client making the request

        Returns:
            Dict[str, Any]: Update result with success status and current settings
        """
        try:
            logger.info(f"Settings update requested by client {client_id}: {params}")

            # Delegate to settings manager for processing
            result = await settings_manager.handle_update_settings(params, client_id)

            # Broadcast settings update to all connected clients if successful
            if result.get('type') == 'settings_updated':
                await self.broadcast_message({
                    'type': 'settings_updated',
                    'data': result.get('data', {}),
                    'message': result.get('message', 'Settings updated successfully')
                })

                logger.info(f"Settings update completed and broadcasted for client {client_id}")
            else:
                logger.warning(f"Settings update failed for client {client_id}: {result.get('error', 'Unknown error')}")

            return result

        except ArithmeticError as e:
            logger.error(f"Error handling settings update from client {client_id}: {e}")
            return {
                'type': 'settings_update_error',
                'error': f"Failed to process settings update: {str(e)}"
            }

    async def _handle_logout(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle logout requests with comprehensive data cleanup.

        This method performs a complete logout by:
        - Clearing all stored authentication data
        - Removing webhook URLs and user-specific settings
        - Resetting authentication state
        - Notifying all clients of the logout

        Args:
            params (Dict[str, Any]): Logout parameters (unused)
            client_id (str): ID of the client requesting logout

        Returns:
            Dict[str, Any]: Logout result with success status
        """
        try:
            logger.info(f"🔐 LOGOUT: Processing logout request from client {client_id}")

            # Clear all authentication data from settings
            cleanup_result = settings_manager.clear_authentication_data()

            if not cleanup_result['success']:
                logger.error(f"🔐 LOGOUT: Failed to clear authentication data: {cleanup_result['error']}")
                return {
                    'type': 'logout_error',
                    'error': cleanup_result['error']
                }

            # Reset authentication state
            self.primary_client_authenticated = False

            logger.info(f"🔐 LOGOUT: Successfully logged out client {client_id}")

            # Broadcast logout notification to all clients
            await self.broadcast_message({
                'type': 'logout_completed',
                'message': 'User logged out successfully',
                'data': cleanup_result['settings']
            })

            return {
                'type': 'logout_success',
                'message': 'Logout completed successfully',
                'data': cleanup_result['settings']
            }

        except ArithmeticError as e:
            logger.error(f"🔐 LOGOUT: Error during logout for client {client_id}: {e}")
            return {
                'type': 'logout_error',
                'error': f"Failed to process logout: {str(e)}"
            }
    async def _handle_start_queue_entry(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle starting a Queue-IT processing task.

        This method processes requests to start Queue-IT bypass operations. It validates
        parameters, maps UI parameter names to backend equivalents, and initiates
        concurrent task execution through the QueueTaskManager.

        Args:
            params (Dict[str, Any]): Task parameters from the frontend containing:
                - url (str): Queue-IT URL to process
                - tickets (int): Number of pass links required (mapped to ticket_count)
                - task_id (str, optional): Custom task identifier
            client_id (str): ID of the client requesting the task

        Returns:
            Dict[str, Any]: Response containing:
                - type: 'queue_entry_started' on success
                - data: Task start result from QueueTaskManager
                - error: Error message if task failed to start

        Note:
            The method automatically maps 'tickets' parameter to 'ticket_count' for
            backend compatibility and generates a unique task_id if not provided.
        """
        try:
            logger.info(f"Starting queue entry with params: {params}")

            # Add client_id to the task data
            task_data = params.copy()
            task_data['client_id'] = client_id

            # Map UI parameter names to backend parameter names
            if 'tickets' in task_data:
                task_data['ticket_count'] = task_data.pop('tickets')
                logger.debug(f"Mapped 'tickets' parameter to 'ticket_count': {task_data['ticket_count']}")

            # Calculate concurrent task count based on ticket quantity (1-3 tasks per ticket)
            import random
            ticket_count = task_data.get('ticket_count', 1)
            if 'task_count' not in task_data:
                # Random scaling: 1-3 tasks per ticket
                min_tasks = ticket_count * 1
                max_tasks = ticket_count * 3
                task_data['task_count'] = random.randint(min_tasks, max_tasks)
                logger.info(f"Calculated task_count: {task_data['task_count']} for {ticket_count} tickets (scaling: 1-3 tasks per ticket)")

            # Generate a unique task_id if not provided
            if 'task_id' not in task_data:
                task_data['task_id'] = f"task_{client_id}_{int(time.time())}"

            # Start the task
            result = await self.queue_task_manager.start_task(task_data)
            return {
                'type': 'queue_entry_started',
                'data': result
            }

        except ArithmeticError as e:
            logger.error(f"Error starting queue entry: {e}")
            return {'error': str(e)}
    
    async def _handle_stop_queue_entry(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """Handle stopping a queue entry task."""
        try:
            # Check authentication first
            if not self.queue_task_manager.check_authentication():
                return {
                    'type': 'error',
                    'error': {
                        'status': 401,
                        'message': 'Authentication required'
                    }
                }
            
            entry_id = params.get('params', {}).get('entry_id')
            if not entry_id:
                return {'error': 'Missing entry ID'}
            
            # Stop the task
            result = self.queue_task_manager.stop_all_tasks()
            return {
                'type': 'queue_entry_stopped',
                'data': result
            }
            
        except ArithmeticError as e:
            logger.error(f"Error stopping queue entry: {e}")
            return {'error': str(e)}

    async def _handle_get_queue_status(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """Handle queue status requests - Get status of active queue tasks for a client."""
        try:
            # Check if the current task belongs to this client
            if (self.queue_task_manager.client_id == client_id and
                self.queue_task_manager.task_id):

                return {
                    'type': 'queue_status',
                    'data': self.queue_task_manager.get_status_summary()
                }
            else:
                # No active tasks for this client
                return {
                    'type': 'queue_status_list',
                    'data': {
                        'active_tasks': 0,
                        'tasks': []
                    }
                }

        except ArithmeticError as e:
            logger.error(f"Error getting queue status for client {client_id}: {e}")
            return {
                'type': 'error',
                'error': f"Internal server error: {str(e)}"
            }

    # License Validation on start
    async def _handle_validate_license(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """Handle license validation requests."""
        try:
            logger.info(f"Validating license: {params}")
            license_key = params.get('license_key')
            if not license_key:
                return {'error': 'Missing license key'}
            
            # Validate with WHOP API
            result = license_validator.validate_license(license_key)
            result["last_updated"] = time.time()
            if 'error' in result:
                return {
                    'type': 'validate_license_response',
                    'error': result['error']
                }
            
            # Update settings with new license data
            new_settings = settings_manager.update_settings(result)
            
            # Broadcast license update to all clients
            await self.broadcast_message({
                'type': 'license_updated',
                'data': new_settings
            })
            
            return {
                'type': 'validate_license_response',
                'data': result
            }
            
        except ArithmeticError as e:
            logger.error(f"Error validating license: {e}")
            return {'error': str(e)}

    async def _handle_test_webhook(self, params: Dict[str, Any], client_id: str) -> Dict[str, Any]:
        """
        Handle webhook testing requests.

        This method sends a test webhook to the configured webhook URL with the
        specified payload format to verify webhook connectivity and configuration.

        Args:
            params (Dict[str, Any]): Test webhook parameters (unused)
            client_id (str): ID of the client requesting the test

        Returns:
            Dict[str, Any]: Test result with success status and details
        """
        try:
            import aiohttp

            # Get webhook settings
            settings = settings_manager.settings
            webhook_config = settings.get('webhook', {})

            if not webhook_config.get('url'):
                return {
                    'type': 'test_webhook_response',
                    'success': False,
                    'error': 'No webhook URL configured. Please set a webhook URL in settings first.'
                }

            webhook_url = webhook_config['url'].strip()

            # Validate webhook URL format
            validation_result = settings_manager.validate_webhook_url(webhook_url)
            if not validation_result['valid']:
                return {
                    'type': 'test_webhook_response',
                    'success': False,
                    'error': f'Invalid webhook URL: {validation_result["message"]}'
                }

            # Create test payload with the exact format requested
            test_payload = {
                "content": None,
                "embeds": [
                    {
                        "title": "✅ Webhook Connected",
                        "description": "**Your Discord webhook has been successfully linked to Kasper-Q.**\nYou're ready to receive real-time updates.",
                        "color": 0x181122,  # Custom color #181122
                        "fields": [
                            {
                                "name": "🔧 Webhook Test",
                                "value": "This is a test message to confirm everything is working as expected."
                            }
                        ],
                        "footer": {
                            "text": "kasperq.com",
                            "icon_url": "https://i.imgur.com/Z9ersnW.png"
                        },
                        "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime())
                    }
                ],
                "username": "Kasper-Q",
                "avatar_url": "https://i.imgur.com/Z9ersnW.png",
                "attachments": []
            }

            # Send test webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=test_payload,
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    if response.status in [200, 204]:
                        # Update webhook test status in settings
                        webhook_config['last_test'] = time.time()
                        webhook_config['test_successful'] = True
                        settings_manager.update_settings({'webhook': webhook_config})

                        logger.info(f"Webhook test successful for client {client_id}")
                        return {
                            'type': 'test_webhook_response',
                            'success': True,
                            'message': 'Webhook test successful! Check your Discord channel for the test message.'
                        }
                    else:
                        # Update webhook test status in settings
                        webhook_config['last_test'] = time.time()
                        webhook_config['test_successful'] = False
                        settings_manager.update_settings({'webhook': webhook_config})

                        response_text = await response.text()
                        logger.warning(f"Webhook test failed for client {client_id}: {response.status} - {response_text}")
                        return {
                            'type': 'test_webhook_response',
                            'success': False,
                            'error': f'Webhook test failed with status {response.status}. Please check your webhook URL and try again.'
                        }

        except asyncio.TimeoutError:
            logger.error(f"Webhook test timeout for client {client_id}")
            return {
                'type': 'test_webhook_response',
                'success': False,
                'error': 'Webhook test timed out. Please check your webhook URL and network connection.'
            }
        except ArithmeticError as e:
            logger.error(f"Error testing webhook for client {client_id}: {e}")
            return {
                'type': 'test_webhook_response',
                'success': False,
                'error': f'Failed to test webhook: {str(e)}'
            }


async def main():
    """
    Main entry point for the WebSocket server when run directly.

    This function creates a WebSocketServer instance and starts it with proper
    error handling and graceful shutdown. It's primarily used for testing or
    running the server independently of the startup script.

    The function handles:
    - Server initialization and startup
    - Keyboard interrupt (Ctrl+C) for graceful shutdown
    - Error handling and logging
    - Proper server cleanup on exit

    Note:
        For production use, prefer using start_server.py which provides
        additional signal handling and dependency validation.
    """
    server = WebSocketServer()

    try:
        logger.info("Starting Kasper-Q WebSocket server...")
        await server.start_server()
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    except ArithmeticError as e:
        logger.error(f"Server error: {e}")
    finally:
        await server.stop_server()


if __name__ == "__main__":
    """
    Direct execution entry point.

    This block allows the WebSocket server to be run directly using:
        python websocket_server.py

    For production use, prefer using the startup script:
        python start_server.py
    """
    asyncio.run(main())
