import React, { useState, useEffect, useCallback } from 'react';
import { FaLink } from 'react-icons/fa';
import { useAppContext } from '../../context/AppContext';
import BackendService from '../../services/BackendService';
import './MonitoringView.css';

// Custom SVG Icons
const StartIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.5 3.5L12.5 8L5.5 12.5V3.5Z" fill="currentColor" stroke="currentColor" strokeWidth="1.2" strokeLinejoin="round"/>
  </svg>
);

const StopIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="4.5" y="4.5" width="7" height="7" rx="1.5" fill="currentColor" stroke="currentColor" strokeWidth="1.2"/>
  </svg>
);

const CancelIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.5 4.5L4.5 11.5M4.5 4.5L11.5 11.5" stroke="currentColor" strokeWidth="1.2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Function to copy link to clipboard
const copyToClipboard = (url) => {
  navigator.clipboard.writeText(url).then(() => {
    // You could add a toast notification here if you want
  });
};

const PassLinksModal = ({ isOpen, onClose, passLinks }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const linksPerPage = 16; // Increased to show 16 links per page

  // Use graceful fallback pattern for pass links
  const safePassLinks = passLinks || [];

  const getVisibleLinks = () => {
    const startIndex = (currentPage - 1) * linksPerPage;
    return safePassLinks.slice(startIndex, startIndex + linksPerPage);
  };

  const totalPages = Math.ceil(safePassLinks.length / linksPerPage);

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Pass Links ({safePassLinks.length})</h3>
          <button 
            className="modal-close-button"
            onClick={onClose}
            title="Close"
          >
            <CancelIcon />
          </button>
        </div>
        <div className="modal-body">
          <div className="pass-links-grid">
            {getVisibleLinks().map((link, index) => (
              <div key={index} className="pass-link-card">
                <div className="pass-link-content">
                  <span className="pass-link-id">#{String(index + 1 + (currentPage - 1) * linksPerPage).padStart(3, '0')}</span>
                  <button 
                    className="copy-button"
                    onClick={() => copyToClipboard(link.url)}
                    title="Copy Link"
                  >
                    <FaLink />
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          {totalPages > 1 && (
            <div className="pagination">
              <button 
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                title="Previous Page"
              >
                ←
              </button>
              <span>Page {currentPage} of {totalPages}</span>
              <button 
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
                title="Next Page"
              >
                →
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const MonitoringView = ({ ticketsRequested, eventUrl, onExit }) => {
  const { backendConnected, connectionStatus } = useAppContext();
  const [passLinks, setPassLinks] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStage, setCurrentStage] = useState('initializing');
  const [showPassLinksModal, setShowPassLinksModal] = useState(false);
  const [taskId, setTaskId] = useState(null);
  const [error, setError] = useState(null);

  // Add mouse tracking effect
  useEffect(() => {
    const handleMouseMove = (e) => {
      const x = (e.clientX / window.innerWidth) * 100;
      const y = (e.clientY / window.innerHeight) * 100;
      document.documentElement.style.setProperty('--mouse-x', `${x}%`);
      document.documentElement.style.setProperty('--mouse-y', `${y}%`);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Dynamic stage definitions based on backend status
  const getStageDefinitions = () => [
    {
      id: 'initializing',
      label: 'Loading Queue',
      icon: '📋',
      message: 'Preparing to join the queue'
    },
    {
      id: 'solving',
      label: 'Solving Challenges',
      icon: '🔒',
      message: 'Solving security challenges'
    },
    {
      id: 'queued',
      label: 'Taking Place',
      icon: '⏳',
      message: 'Securing positions in queue'
    },
    {
      id: 'waiting',
      label: 'Waiting in Queue',
      icon: '⌛',
      message: 'Waiting for our turn'
    },
    {
      id: 'active',
      label: 'Our Turn',
      icon: '🎯',
      message: 'Getting pass links'
    },
    {
      id: 'completed',
      label: 'Completed',
      icon: '✅',
      message: 'All pass links ready'
    },
    {
      id: 'error',
      label: 'Failed',
      icon: '❌',
      message: 'Processing failed'
    }
  ];

  const stages = getStageDefinitions();
  const currentStageIndex = stages.findIndex(stage => stage.id === currentStage);

  // Connection status indicator component
  const ConnectionStatusIndicator = () => {
    const getStatusColor = () => {
      switch (connectionStatus) {
        case 'connected': return '#4CAF50';
        case 'connecting': return '#FF9800';
        case 'error': return '#F44336';
        default: return '#9E9E9E';
      }
    };

    const getStatusText = () => {
      switch (connectionStatus) {
        case 'connected': return 'Connected';
        case 'connecting': return 'Connecting...';
        case 'error': return 'Connection Error';
        default: return 'Disconnected';
      }
    };

    const getStatusIcon = () => {
      switch (connectionStatus) {
        case 'connected': return '🟢';
        case 'connecting': return '🟡';
        case 'error': return '🔴';
        default: return '⚫';
      }
    };

    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '6px 12px',
        background: 'rgba(0, 0, 0, 0.2)',
        borderRadius: '20px',
        fontSize: '12px',
        color: getStatusColor(),
        border: `1px solid ${getStatusColor()}`,
        marginBottom: '10px'
      }}>
        <span>{getStatusIcon()}</span>
        <span>{getStatusText()}</span>
      </div>
    );
  };

  // WebSocket message handler for queue progress updates
  const handleQueueProgressUpdate = useCallback((message) => {
    console.log('🔄 MonitoringView: Received queue progress update:', message);

    if (message.type === 'queue_progress_update') {
      const { overall_status, pass_links, error_message } = message;

      console.log('🔄 MonitoringView: Processing progress update:', {
        overall_status,
        pass_links_count: pass_links?.length || 0,
        error_message
      });

      // Update current stage based on backend status
      setCurrentStage(overall_status || 'initializing');

      // Update pass links with actual backend data
      if (pass_links && Array.isArray(pass_links)) {
        const formattedLinks = pass_links.map((url, index) => ({
          id: `link_${index}`,
          url: url,
          timestamp: new Date().toLocaleTimeString()
        }));
        setPassLinks(formattedLinks);
        console.log('🔄 MonitoringView: Updated pass links:', formattedLinks.length);
      }

      // Update error message if status is error
      if (overall_status === 'error' && error_message) {
        setError(error_message);
        console.log('🔄 MonitoringView: Set error:', error_message);
      } else {
        setError(null);
      }
    } else {
      console.log('🔄 MonitoringView: Ignoring non-queue-progress message:', message.type);
    }
  }, []);

  // Register WebSocket handler on component mount
  useEffect(() => {
    console.log('🔄 MonitoringView: Registering queue progress handler');
    BackendService.registerHandler('queue_progress_update', handleQueueProgressUpdate);

    return () => {
      console.log('🔄 MonitoringView: Unregistering queue progress handler');
      BackendService.unregisterHandler('queue_progress_update', handleQueueProgressUpdate);
    };
  }, []); // Remove handleQueueProgressUpdate dependency to prevent re-registration

  // Real queue entry management functions
  const startQueueEntry = async () => {
    if (!backendConnected) {
      setError('Backend not connected. Please check connection.');
      return;
    }

    try {
      setIsProcessing(true);
      setCurrentStage('initializing');
      setPassLinks([]);
      setError(null);

      // Validate required parameters with sensible defaults for backward compatibility
      const queueUrl = eventUrl || 'https://queue-it.example.com';
      const ticketCount = ticketsRequested || 1;

      if (!eventUrl) {
        console.warn('No event URL provided, using placeholder URL for testing');
      }

      // Validate ticket quantity
      if (ticketCount < 1 || ticketCount > 100) {
        throw new Error('Ticket quantity must be between 1 and 10');
      }

      // Log the parameters being used for debugging
      console.log('Starting queue entry with parameters:', {
        url: queueUrl,
        tickets: ticketCount,
        originalEventUrl: eventUrl,
        originalTicketsRequested: ticketsRequested
      });

      // Start queue entry with backend using real parameters
      const response = await BackendService.startQueueEntry({
        url: queueUrl,
        tickets: ticketCount
      });

      // Handle backend response format: { type: 'queue_entry_started' | 'error', ... }
      if (response.type === 'queue_entry_started') {
        setTaskId(response.task_id);
        console.log('Queue entry started successfully:', {
          taskId: response.task_id,
          taskCount: response.task_count,
          ticketCount: response.ticket_count,
          status: response.status,
          message: response.message
        });
      } else if (response.type === 'error') {
        throw new Error(response.error || 'Failed to start queue entry');
      } else {
        // Handle unexpected response format
        throw new Error(`Unexpected response format: ${JSON.stringify(response)}`);
      }
    } catch (error) {
      console.error('Failed to start queue entry:', error);
      setError(error.message);
      setIsProcessing(false);
      setCurrentStage('error');
    }
  };

  const stopQueueEntry = async () => {
    if (!taskId) return;

    try {
      const response = await BackendService.stopQueueEntry(taskId);

      // Handle backend response format: { type: 'queue_entry_stopped' | 'error', ... }
      if (response.type === 'queue_entry_stopped') {
        setIsProcessing(false);
        setTaskId(null);
        console.log('Queue entry stopped successfully:', {
          taskId: response.task_id,
          status: response.status,
          passLinksObtained: response.pass_links_obtained,
          message: response.message
        });

        // Don't reset stage if completed, otherwise reset to initializing
        if (currentStage !== 'completed') {
          setCurrentStage('initializing');
        }
      } else if (response.type === 'error') {
        throw new Error(response.error || 'Failed to stop queue entry');
      } else {
        // Handle unexpected response format
        console.warn('Unexpected stop response format:', response);
        // Still try to stop processing on unexpected response
        setIsProcessing(false);
        setTaskId(null);
      }
    } catch (error) {
      console.error('Failed to stop queue entry:', error);
      setError(error.message);
    }
  };

  const stopProcessing = () => {
    stopQueueEntry();
  };

  const startProcessing = () => {
    startQueueEntry();
  };

  const handleCancel = () => {
    stopProcessing();
    // Call onExit to return to main page
    if (typeof onExit === 'function') {
      onExit();
    }
  };

  return (
    <div className="monitoring-layout">
      <div className="card-section progress-section">
        

        <div className="progress-container">
          <div className="progress-bar">
            <div className="progress-stages">
              {stages.map((stage, index) => {
                const isActive = index === currentStageIndex;
                const isCompleted = index < currentStageIndex && currentStage !== 'error';
                const isFailed = currentStage === 'error';// && index === currentStageIndex;

                return (
                  <div
                    key={stage.id}
                    className={`progress-stage ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''} ${isFailed ? 'failed' : ''}`}
                  >
                    <div className="progress-stage-icon">
                      {stage.icon}
                    </div>
                    {index > 0 && (
                      <div className={`progress-connector ${isCompleted ? 'active' : ''} ${isFailed ? 'failed' : ''}`} />
                    )}
                    <div className="progress-stage-tooltip">
                      {stage.id === 'error' && error ? error : stage.message}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <div className="status-display">
          <div className="current-stage">
            {stages[currentStageIndex]?.label || 'Unknown Status'}
          </div>
          <div className="stage-subtext">
            {currentStage === 'error' && error ? error : stages[currentStageIndex]?.message || 'Processing...'}
          </div>

          {/* Queue Parameters Display 
          {(eventUrl || ticketsRequested) && (
            <div className="queue-params-display" style={{
              marginTop: '10px',
              padding: '8px 12px',
              background: 'rgba(124, 77, 255, 0.1)',
              borderRadius: '6px',
              fontSize: '12px',
              color: 'rgba(255, 255, 255, 0.8)'
            }}>
              <div>URL: {eventUrl || 'Using default URL'}</div>
              <div>Tickets: {ticketsRequested || 1}</div>
            </div>
          )}
            */}
        </div>

        {(passLinks?.length > 0 || currentStageIndex >= 2) && (
          <div className="pass-links-summary">
            <div className="pass-links-count">
              <FaLink className="link-icon" />
              <span>{passLinks?.length || 0} Pass Links Generated</span>
            </div>
            {passLinks?.length > 0 && (
              <button
                className="view-links-button"
                onClick={() => setShowPassLinksModal(true)}
              >
                View Links
              </button>
            )}
          </div>
        )}

        <div className="traffic-lights">
          {!isProcessing ? (
            <button
              className="action-button start"
              onClick={startProcessing}
              title="Start Processing"
            >
              <StartIcon />
            </button>
          ) : currentStage !== 'error' ? (
            <button
              className="action-button stop"
              onClick={stopProcessing}
              title="Stop Processing"
            >
              <StopIcon />
            </button>
          ) : null}
          <button
            className="action-button cancel"
            onClick={handleCancel}
            title="Return to Main Page"
          >
            <CancelIcon />
          </button>
        </div>
      </div>

      <PassLinksModal
        isOpen={showPassLinksModal}
        onClose={() => setShowPassLinksModal(false)}
        passLinks={passLinks}
      />
    </div>
  );
};

export default MonitoringView; 