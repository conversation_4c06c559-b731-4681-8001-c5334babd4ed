from pydantic import BaseModel
from typing import Dict, Union
from random import randint
from requests import post

class CaptchaSolver:
    name: str
    base_url: str
    headers: Dict[str, str]
    payload: Dict[str, Union[str, int, Dict[str, Union[str, int, Dict[str, str]]]]]

    def __init__(
        self, 
        user_agent: str,
        sec_ch_ua: str,
        chrome_version: str,
        os: str,
        os_full_name: str,
        os_version: str,
        customer_id: str,
        event_id: str,
        challenge_hash: str,
        queue_url: str,
        domain: str,
        license_key: str,
        hwid: str
    ):
        self.user_agent = user_agent
        self.sec_ch_ua = sec_ch_ua
        self.chrome_version = chrome_version
        self.os = os
        self.os_full_name = os_full_name
        self.os_version = os_version
        self.customer_id = customer_id
        self.event_id = event_id
        self.challenge_hash = challenge_hash
        self.queue_url = queue_url
        self.domain = domain

        # user details
        self.license_key = license_key
        self.hwid = hwid
    
    @property
    def _stats(self):
        return {
            "userAgent": self.user_agent,
            "screen": "1470 x 956",
            "browser": "Chrome",
            "browserVersion": f"{self.chrome_version}.0.0.0",
            "isMobile": False,
            "os": self.os_full_name,
            "osVersion": self.os_version,
            "cookiesEnabled": True,
            "tries": 1,
            "duration": randint(50, 150)
        }
    
    @property
    def url(self):
        return self.base_url.format(self.domain)

    @property
    def headers(self):
        return { 
            "Host": self.domain,
            "sec-ch-ua": self.sec_ch_ua,
            "sec-ch-ua-mobile": "?0",
            "user-agent": self.user_agent,
            "sec-ch-ua-platform": self.os,
            "accept": "*/*",
            "origin": f"https://{self.domain}",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": self.queue_url,
            "accept-language": "en-GB,en;q=0.9",
            "x-queueit-challange-customerid": self.customer_id,
            "x-queueit-challange-eventid": self.event_id,
            "x-queueit-challange-hash": self.challenge_hash,
            "x-queueit-challange-reason": "1"
        }

    def get_response(self, **kwargs) -> dict:
        response = post(
            "http://127.0.0.1:3023/api/solve-captcha",
            json={
                "name": self.name,
                "license_key": self.license_key,
                "hwid": self.hwid,
                "details": {
                    **kwargs
                }
            },
            verify=False
        ).json()
        if (error := response.get("error")):
            raise ArithmeticError(error)
        return response["result"]
    
    def solve(self, challenge: dict) -> dict:
        raise NotImplementedError

class BotDetect(CaptchaSolver):
    name: str = "BotDetect"
    base_url: str = "https://{}/challengeapi/queueitcaptcha/challenge/en-us"
    
    def solve(self, challenge_details: dict):
        self.challenge_details = challenge_details
        response = self.get_response(
            image=self.challenge_details['imageBase64']
        )
        self._stats["duration"] = randint(50, 150)
        return {
            "challengeType": "botdetect",
            "sessionId": self.challenge_details['sessionId'],
            "challengeDetails": self.challenge_details['challengeDetails'],
            "solution": response,
            "stats": self._stats,
            "customerId": self.customer_id,
            "eventId": self.event_id,
            "version": 6
        }

class Recaptcha(CaptchaSolver):
    name: str = "Recaptcha"
    base_url: str = "https://{}/challengeapi/recaptcha/challenge"

    def solve(self, challenge_details: dict):
        self.challenge_details = challenge_details
        response = self.get_response(
            sitekey=self.challenge_details['siteKey'],
            pageurl=self.domain
        )
        self._stats["duration"] = randint(9000, 10000)
        return {
            "challengeType": "recaptcha",
            "sessionId": self.challenge_details['sessionId'],
            "challengeDetails": self.challenge_details['challengeDetails'],
            "solution": response,
            "stats": self._stats,
            "customerId": self.customer_id,
            "eventId": self.event_id,
            "version": 6
        }
    
class RecaptchaInvisible(CaptchaSolver):
    name: str = "RecaptchaInvisible"
    base_url: str = "https://{}/challengeapi/recaptchainvisible/challenge"

    def solve(self, challenge_details: dict):
        self.challenge_details = challenge_details
        response = self.get_response(
            sitekey=self.challenge_details['siteKey'],
            pageurl=self.domain,
            enterprise=True
        )
        self._stats["duration"] = randint(1900, 2100)
        return  {
            "challengeType": "recaptcha-invisible",
            "sessionId": self.challenge_details['sessionId'],
            "challengeDetails": self.challenge_details['challengeDetails'],
            "solution": response,
            "stats": self._stats,
            "customerId": self.customer_id,
            "eventId": self.event_id,
            "version": 6
        }

class ProofOfWork(CaptchaSolver):
    name: str = "ProofOfWork"
    base_url: str = "https://{}/challengeapi/pow/challenge"
    
    def solve(self, challenge_details: dict):
        session_id = challenge_details["sessionId"]
        # meta = challenge_details["meta"]
        parameters = challenge_details["parameters"]
        new_challenge_details = challenge_details["challengeDetails"]
        response = self.get_response(
            type=parameters['type'],
            input=parameters['input'],
            runs=parameters['runs'],
            complexity=parameters['complexity']
        )
        self._stats["duration"] = randint(780,830)
        return {
            "challengeType": "proofofwork",
            "sessionId": session_id,
            "challengeDetails": new_challenge_details,
            "solution": response,
            "stats": self._stats,
            "customerId": self.customer_id,
            "eventId": self.event_id,
            "version": 6
        }

SOLVERS = {
    "BotDetect": BotDetect,
    "Recaptcha": Recaptcha,
    "RecaptchaInvisible": RecaptchaInvisible,
    "ProofOfWork": ProofOfWork
}