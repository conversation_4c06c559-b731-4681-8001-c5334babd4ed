"""
Kasper-Q Queue Task Manager

This module manages Queue-IT processing tasks for the Kasper-Q system. It coordinates
concurrent queue bypass operations, monitors task progress, and provides real-time
status updates through WebSocket communication.

Key Features:
- Concurrent queue bypass task execution with configurable task counts
- Real-time progress monitoring and status aggregation
- Queue-IT processing stages: Initializing, In Queue, Processing, Generating, Complete, Failed
- Discord webhook notifications for pass link generation and completion
- Thread-safe task management with proper cleanup and error handling
- Integration with WHOP API license validation
- WebSocket broadcasting for real-time frontend updates

The task manager implements a singleton pattern to ensure consistent state management
across the application and provides comprehensive error handling for robust operation.

Processing Stages:
- Initializing: Setting up tasks and preparing for queue entry
- Solving: Actively working on queue bypass challenges
- Queued: Waiting in the queue system
- Waiting: Waiting for queue progression
- Active: Actively processing in the queue
- Completed: Successfully obtained pass links
- Error: Failed due to errors or authentication issues

Author: Kasper-Q Development Team
Created: 2024
Version: 1.0

Example:
    >>> manager = QueueTaskManager()
    >>> manager.set_settings_manager(settings_manager)
    >>> manager.set_websocket_server(websocket_server)
    >>> result = await manager.start_task({
    ...     'task_id': 'task_123',
    ...     'ticket_count': 5,
    ...     'url': 'https://queue.example.com',
    ...     'client_id': 'client_abc'
    ... })
"""

import asyncio
import time
from threading import Thread
from typing import Dict, Any
from tls_client import Session
import aiohttp
import ctypes
import threading

from queue_it.main import QueueItHandler
from tasks_logger import Logger
from helpers import settings_manager

logger = Logger(
    level='INFO',
    formatting="{timestamp} [{level:^7}] ({name}) {message}",
    name="TasksManager"
)

class QueueTaskManager:
    """
    Manages Queue-IT processing tasks with concurrent execution and real-time monitoring.

    This class implements a singleton pattern to manage Queue-IT bypass tasks across the
    application. It coordinates multiple concurrent tasks, monitors their progress, and
    provides real-time status updates through WebSocket communication.

    Key Features:
    - Singleton pattern for consistent state management
    - Concurrent task execution with configurable task counts
    - Real-time progress monitoring and status aggregation
    - Discord webhook notifications for pass links and completion
    - Thread-safe operations with proper cleanup
    - Integration with license validation and settings management
    - WebSocket broadcasting for frontend updates

    Task Processing Stages:
    - initializing: Setting up tasks and preparing for execution
    - solving: Actively working on queue bypass challenges
    - queued: Waiting in the queue system
    - waiting: Waiting for queue progression
    - active: Actively processing in the queue
    - completed: Successfully obtained required pass links
    - error: Failed due to errors or authentication issues

    Attributes:
        tasks (List[Dict]): List of active task entries
        logger (Logger): Task manager logger instance
        stop_event (threading.Event): Event for coordinating task shutdown
        _settings_manager (SettingsManager): Settings and configuration manager
        _is_authenticated (bool): Current authentication status

        # Task execution attributes
        task_id (str): Current task identifier
        task_count (int): Number of concurrent tasks to run
        ticket_count (int): Number of pass links required
        url (str): Queue-IT URL to process
        pass_links (List[str]): Successfully obtained pass links
        websocket_server: WebSocket server for broadcasting updates
        client_id (str): ID of the client that started the task
        event_loop: Asyncio event loop for scheduling async operations
        is_running (bool): Whether tasks are currently running
        start_time (float): Timestamp when tasks started
        overall_status (str): Aggregated status of all tasks
        last_update_time (float): Last time status was updated
        completion_time (float): Timestamp when tasks completed

    Example:
        >>> manager = QueueTaskManager()
        >>> manager.set_settings_manager(settings_manager)
        >>> manager.set_websocket_server(websocket_server)
        >>> result = await manager.start_task({
        ...     'task_id': 'task_123',
        ...     'ticket_count': 5,
        ...     'url': 'https://queue.example.com'
        ... })
    """
    _instance = None
    _initialized = False

    def __new__(cls):
        """
        Implement singleton pattern to ensure only one instance exists.

        Returns:
            QueueTaskManager: The singleton instance
        """
        if cls._instance is None:
            cls._instance = super(QueueTaskManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """
        Initialize the QueueTaskManager singleton instance.

        This method only initializes the instance once due to the singleton pattern.
        Subsequent calls to __init__ will not reinitialize the instance.
        """
        if not self._initialized:
            self.tasks = []
            self.logger = logger
            self.stop_event = threading.Event()
            self._initialized = True
            self._is_authenticated = False

            # Task management attributes
            self.task_id = None
            self.task_count = 0
            self.ticket_count = 0
            self.url = None
            self.pass_links = []
            self.websocket_server = None
            self.client_id = None
            self.event_loop = None
            self.is_running = False
            self.start_time = None
            self.overall_status = 'initializing'
            self.last_update_time = time.time()
            self.completion_time = None

    def set_websocket_server(self, websocket_server):
        """
        Set the WebSocket server instance for broadcasting real-time updates.

        Args:
            websocket_server: The WebSocket server instance used to broadcast
                task progress updates, status changes, and completion notifications
                to connected clients in real-time.
        """
        self.websocket_server = websocket_server
        self.logger.info("WebSocket server reference set")

    def check_authentication(self):
        """
        Check if we have a valid license key for Queue-IT operations.

        This method validates that:
        1. A settings manager is configured
        2. License data exists in settings
        3. The license is marked as valid

        Returns:
            bool: True if authenticated and license is valid, False otherwise

        Note:
            This method is called before starting any Queue-IT operations to ensure
            proper authorization through the WHOP API license system.
        """
        self.logger.info("Checking authentication")

        settings = settings_manager.settings
        license_data = settings.get('license', {})

        # Check if we have a valid license
        if not license_data.get('valid'):
            self.logger.error("License is not valid")
            return False

        return True

    def ensure_authentication(self):
        """
        Ensure we're authenticated before allowing Queue-IT operations.

        This method performs authentication checking and automatically stops
        all running tasks if authentication fails. This provides security
        by preventing unauthorized Queue-IT processing.

        Returns:
            bool: True if authenticated, False if authentication failed

        Side Effects:
            - Stops all running tasks if authentication fails
            - Clears the task list if authentication fails

        Note:
            This method should be called before any task management operations
            to ensure proper authorization.
        """
        if not self.check_authentication():
            # Stop all tasks if we're not authenticated
            self.stop_all_tasks()
            # Clear all tasks
            self.tasks.clear()
            return False
        return True

    async def start_task(self, task_data):
        """
        Start a new Queue-IT processing task with authentication and validation.

        This method initializes and starts a new queue bypass operation with the
        specified parameters. It performs authentication checking, sets up task
        state, and begins concurrent task execution.

        Args:
            task_data (Dict[str, Any]): Task configuration containing:
                - task_id (str, optional): Unique task identifier
                - task_count (int, optional): Number of concurrent tasks (default: 1)
                - ticket_count (int, optional): Number of pass links required (default: 1)
                - url (str): Queue-IT URL to process
                - client_id (str): ID of the client requesting the task

        Returns:
            Dict[str, Any]: Task start result containing:
                - success (bool): Whether the task started successfully
                - error (str): Error message if task failed to start
                - task_id (str): The assigned task identifier
                - task_count (int): Number of concurrent tasks started
                - status (str): Initial task status

        Raises:
            ArithmeticError: If task initialization or startup fails

        Note:
            This method resets all previous task state and starts fresh. Only one
            task can be active at a time per QueueTaskManager instance.
        """
        self.logger.info(f"Starting task with data: {task_data}")

        # Check authentication first
        if not self.check_authentication():
            return {
                'success': False,
                'error': 'Authentication required'
            }

        # Initialize task manager with the provided data
        self.task_id = task_data.get('task_id', f"task_{int(time.time())}")
        self.task_count = task_data.get('task_count', 1)
        self.ticket_count = task_data.get('ticket_count', 1)
        self.url = task_data.get('url', '')
        self.client_id = task_data.get('client_id')

        # Reset state for new task
        self.tasks.clear()
        self.pass_links.clear()
        self.is_running = False
        self.start_time = None
        self.overall_status = 'initializing'
        self.last_update_time = time.time()
        self.stop_event.clear()
        self.final_update_sent = False  # Track if final completion update has been sent

        # Get current event loop for async operations
        try:
            self.event_loop = asyncio.get_running_loop()
        except RuntimeError:
            self.event_loop = None
            self.logger.warning("No running event loop found")

        # Start the tasks
        return await self.start_tasks()

    def stop_all_tasks(self):
        """Stop all tasks with authentication check."""
        if not self.ensure_authentication():
            return
            
        # Set stop event
        self.stop_event.set()
        
        # Stop each task
        for task in self.tasks:
            try:
                # Stop the QueueItHandler first
                if task.get('handler'):
                    task['handler'].stop()
                
                # Force stop the thread
                if task.get('thread') and task['thread'].is_alive():
                    thread_id = task['thread'].ident
                    if thread_id:
                        # Raise SystemExit in the thread
                        ctypes.pythonapi.PyThreadState_SetAsyncExc(
                            ctypes.c_long(thread_id),
                            ctypes.py_object(SystemExit)
                        )
                
                # Update task status
                task['status'] = 'stopped'
                task['error'] = 'Task stopped due to authentication failure'
                task['last_status_change'] = time.time()
                
            except ArithmeticError as e:
                logger.error(f"Error stopping task {task.get('id')}: {e}")
        
        # Clear stop event
        self.stop_event.clear()
        
        # Remove completed/stopped tasks
        self.tasks = [t for t in self.tasks if t['status'] not in ['completed', 'stopped', 'error']]
        
        return {
            'success': True,
            'message': 'All tasks stopped',
            'task_id': None,
            'status': 'stopped'
        }

    def get_tasks(self):
        """Get all tasks with authentication check."""
        if not self.ensure_authentication():
            return []
        return self.tasks

    def get_task(self, task_id):
        """Get a specific task with authentication check."""
        if not self.ensure_authentication():
            return None
        return next((t for t in self.tasks if t['id'] == task_id), None)

    def get_status_summary(self):
        """Get a summary of the current task status."""
        if not self.ensure_authentication():
            return {
                'error': 'Authentication required',
                'status': 'unauthenticated'
            }

        return {
            'task_id': self.task_id,
            'overall_status': self.overall_status,
            'task_count': self.task_count,
            'ticket_count': self.ticket_count,
            'pass_links_count': len(self.pass_links),
            'pass_links': self.pass_links,
            'url': self.url,
            'is_running': self.is_running,
            'start_time': self.start_time,
            'completion_time': getattr(self, 'completion_time', None),
            'duration': time.time() - self.start_time if self.start_time else 0,
            'error_message': getattr(self, 'error_message', None),
            'tasks': [
                {
                    'task_index': task['task_index'],
                    'status': task['status'],
                    'error': task['error'],
                    'duration': time.time() - task['start_time']
                }
                for task in self.tasks
            ]
        }

    def _schedule_async_task(self, coro):
        """Thread-safe method to schedule async operations from worker threads"""
        if self.event_loop and not self.event_loop.is_closed():
            try:
                # Schedule the coroutine to run in the event loop from another thread
                future = asyncio.run_coroutine_threadsafe(coro, self.event_loop)
                return future
            except ArithmeticError as e:
                self.logger.error(f"Failed to schedule async task: {e}")
        else:
            self.logger.warning("No event loop available to schedule async task")
        return None

    async def start_tasks(self) -> Dict[str, Any]:
        """Launch all concurrent queue bypass tasks"""
        try:
            self.is_running = True
            self.start_time = time.time()
            self.logger.info(f"Starting {self.task_count} queue bypass tasks for {self.task_id}")

            # Create and start all tasks
            for i in range(self.task_count):
                await self._create_task(i)

            # Start monitoring thread
            monitor_thread = Thread(target=self._monitor_tasks, daemon=True)
            monitor_thread.start()
            self.logger.info(f"Started monitoring thread for task {self.task_id}")

            # Send initial status update if websocket server is available (but don't wait for it)
            if self.websocket_server:
                # Schedule the broadcast without waiting for it
                asyncio.create_task(self._broadcast_update())
                self.logger.info(f"Scheduled initial status update for task {self.task_id}")

            return {
                'success': True,
                'task_id': self.task_id,
                'task_count': self.task_count,
                'status': 'started'
            }

        except ArithmeticError as e:
            self.logger.error(f"Failed to start tasks for {self.task_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _create_task(self, task_index: int):
        """Create and start a single queue bypass task"""
        try:
            self.logger.info(f"Creating task {task_index}")

            # Get settings for captcha API keys
            settings = settings_manager.settings
            
            # Create QueueItHandler instance
            handler = QueueItHandler(
                session=Session(client_identifier="chrome_110"),
                url=self.url,
                license_key=settings.get("license", {}).get('license_key', ''),
                hwid=settings.get("license", {}).get('metadata', {}).get('hwid', '')
            )

            # Create task entry
            task_entry = {
                'handler': handler,
                'task_index': task_index,
                'status': 'initializing',
                'error': None,
                'start_time': time.time(),
                'last_status_change': time.time(),
                'thread': None
            }

            # Add task to list before starting thread
            self.tasks.append(task_entry)
            self.logger.info(f"Created task entry for task {task_index}")

            # Start task in separate thread
            thread = Thread(target=self._run_task, args=(task_entry,), daemon=True)
            thread.start()
            task_entry['thread'] = thread

            self.logger.info(f"Started task thread for task {task_index}")

        except ArithmeticError as e:
            self.logger.error(f"Failed to create task {task_index}: {e}", exc_info=True)
    
    async def _send_discord_webhook(self, pass_url: str, task_index: int):
        """Send a Discord webhook notification with pass URL details."""
        try:

            settings = settings_manager.settings
            webhook_config = settings.get('webhook', {})

            if not webhook_config.get('enabled') or not webhook_config.get('url'):
                self.logger.debug("Discord webhook not configured or disabled")
                return

            # Prepare webhook payload with improved format
            payload = {
                "content": None,
                "embeds": [{
                    "title": "🎫 Queue Pass Acquired",
                    "description": f"**Task `#{task_index}` completed.** Pass link successfully retrieved.",
                    "color": 0x181122,  # Custom color #181122
                    "fields": [
                        {
                            "name": "🔗 Pass Link",
                            "value": f"[Click here]({pass_url})"
                        },
                        {
                            "name": "🌍 Event URL",
                            "value": f"[Open Event]({self.url})"
                        }
                    ],
                    "footer": {
                        "text": f"pass url #{len(self.pass_links)}/{self.ticket_count}",
                        "icon_url": "https://i.imgur.com/Z9ersnW.png"
                    },
                    "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime())
                }],
                "username": "Kasper-Q",
                "avatar_url": "https://i.imgur.com/Z9ersnW.png",
                "attachments": []
            }

            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_config['url'],
                    json=payload,
                    timeout=10
                ) as response:
                    if response.status == 204:
                        self.logger.info(f"Discord webhook sent successfully for task {task_index}")
                    else:
                        self.logger.warning(f"Failed to send Discord webhook for task {task_index}: {response.status}")
                        response_text = await response.text()
                        self.logger.debug(f"Webhook response: {response_text}")

        except ArithmeticError as e:
            self.logger.error(f"Error sending Discord webhook: {e}")

    async def _send_completion_webhook(self):
        """Send a Discord webhook notification when all pass links are obtained."""
        try:
            settings = settings_manager.settings
            webhook_config = settings.get('webhook', {})

            if not webhook_config.get('enabled') or not webhook_config.get('url'):
                self.logger.debug("Discord webhook not configured or disabled")
                return

            # Calculate duration
            duration = time.time() - self.start_time if self.start_time else 0
            duration_str = f"{int(duration // 60)}m {int(duration % 60)}s"

            # Prepare webhook payload with improved format
            payload = {
                "content": None,
                "embeds": [{
                    "title": "✅ Queue Entry Completed",
                    "description": f"**All {self.ticket_count} pass links successfully obtained.** Task completed in {duration_str}.",
                    "color": 0x181122,  # Custom color #181122
                    "fields": [
                        {
                            "name": "🔗 Pass Links",
                            "value": "\n".join([f"[Link {i+1}]({link})" for i, link in enumerate(self.pass_links)])
                        },
                        {
                            "name": "🌍 Event URL",
                            "value": f"[Open Event]({self.url})"
                        },
                        {
                            "name": "⏱️ Duration",
                            "value": duration_str
                        }
                    ],
                    "footer": {
                        "text": f"Queue Entry Complete • {self.ticket_count} tickets",
                        "icon_url": "https://i.imgur.com/Z9ersnW.png"
                    },
                    "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime())
                }],
                "username": "Kasper-Q",
                "avatar_url": "https://i.imgur.com/Z9ersnW.png",
                "attachments": []
            }

            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_config['url'],
                    json=payload,
                    timeout=10
                ) as response:
                    if response.status == 204:
                        self.logger.info("Discord webhook sent successfully for completion")
                    else:
                        self.logger.warning(f"Failed to send completion Discord webhook: {response.status}")
                        response_text = await response.text()
                        self.logger.debug(f"Webhook response: {response_text}")

        except ArithmeticError as e:
            self.logger.error(f"Error sending completion Discord webhook: {e}")

    def _run_task(self, task_entry: Dict[str, Any]):
        """Run individual queue bypass task with progress monitoring"""
        handler = task_entry['handler']
        task_index = task_entry['task_index']
        
        try:
            self.logger.info(f"Starting queue bypass task {task_index}")
            
            # Update status to solving before starting the process
            task_entry['status'] = 'solving'
            task_entry['last_status_change'] = time.time()
            
            # Start the queue bypass process in a new thread
            solve_thread = Thread(target=handler.solve, daemon=True)
            solve_thread.start()
            
            # Monitor task progress
            last_status = None
            last_check_time = time.time()
            
            while not self.stop_event.is_set() and self.is_running:
                # Check stop event first
                if self.stop_event.is_set():
                    self.logger.info(f"Stop event detected for task {task_index}")
                    task_entry['status'] = 'stopped'
                    task_entry['error'] = 'Task stopped by user'
                    # Force stop the solve thread
                    if solve_thread.is_alive():
                        thread_id = solve_thread.ident
                        if thread_id:
                            # Force terminate the thread
                            ctypes.pythonapi.PyThreadState_SetAsyncExc(
                                ctypes.c_long(thread_id),
                                ctypes.py_object(SystemExit)
                            )
                    break

                current_status = handler.status
                current_error = handler.error
                
                # Log status periodically (every 5 seconds) or on change
                current_time = time.time()
                if current_status != last_status or current_time - last_check_time >= 5:
                    self.logger.debug(f"Task {task_index} status: {current_status} (error: {current_error})")
                    last_check_time = current_time
                
                # Update task status if changed
                if current_status != last_status:
                    task_entry['status'] = current_status
                    task_entry['error'] = current_error
                    task_entry['last_status_change'] = time.time()
                    
                    self.logger.info(f"Task {task_index} status changed: {last_status} -> {current_status}")
                    last_status = current_status
                
                # Check if task obtained a new passlink
                if handler.pass_url and handler.pass_url not in self.pass_links:
                    self.pass_links.append(handler.pass_url)
                    task_entry['pass_url'] = handler.pass_url

                    self.logger.info(f"Task {task_index} obtained passlink: {handler.pass_url} ({len(self.pass_links)}/{self.ticket_count})")

                    # Send Discord webhook for individual pass link
                    self._schedule_async_task(self._send_discord_webhook(handler.pass_url, task_index))

                    # Check if we have enough pass-links for the entire operation
                    if len(self.pass_links) >= self.ticket_count:
                        self.logger.info(f"Target passlinks reached ({len(self.pass_links)}/{self.ticket_count}) - completing all tasks")
                        # Mark this individual task as completed since we got a passlink
                        task_entry['status'] = 'completed'
                        # Schedule completion of the entire operation
                        self._schedule_async_task(self._complete_all_tasks())
                        break
                    else:
                        # We need more passlinks - create a new handler instance for this task
                        self.logger.info(f"Task {task_index} needs to continue for more passlinks ({len(self.pass_links)}/{self.ticket_count}) - creating new handler")

                        # Get settings for new handler
                        settings = settings_manager.settings

                        # Create new QueueItHandler instance
                        new_handler = QueueItHandler(
                            session=Session(client_identifier="chrome_110"),
                            url=self.url,
                            license_key=settings.get("license", {}).get('license_key', ''),
                            hwid=settings.get("license", {}).get('metadata', {}).get('hwid', '')
                        )

                        # Replace the handler in the task entry
                        task_entry['handler'] = new_handler
                        task_entry['status'] = 'solving'
                        task_entry['last_status_change'] = time.time()

                        # Start new solve thread
                        solve_thread = Thread(target=new_handler.solve, daemon=True)
                        solve_thread.start()

                        self.logger.info(f"Task {task_index} restarted with new handler for additional passlinks")
                
                # Check for error states
                if current_status == 'error' and current_error:
                    self.logger.warning(f"Task {task_index} failed: {current_error}")
                    break
                
                # Check if solve thread is still alive
                if not solve_thread.is_alive():
                    if current_status not in ['completed', 'error']:
                        self.logger.warning(f"Task {task_index} solve thread died unexpectedly")
                        task_entry['status'] = 'error'
                        task_entry['error'] = "Solve thread died unexpectedly"
                        break
                
                time.sleep(0.5)  # Status check interval
                
        except ArithmeticError as e:
            self.logger.error(f"Task {task_index} encountered exception: {e}")
            task_entry['status'] = 'error'
            task_entry['error'] = str(e)
    
    def _monitor_tasks(self):
        """Monitor overall task progress and broadcast updates"""
        while self.is_running and not self.stop_event.is_set():
            try:
                # Calculate overall progress
                old_status = self.overall_status
                self.overall_status, error_message = self._calculate_overall_status()

                # Store error message if status is error
                if self.overall_status == 'error':
                    self.error_message = error_message
                elif hasattr(self, 'error_message'):
                    delattr(self, 'error_message')

                # Check if task is completed with all required passlinks
                is_completed = self._is_task_fully_completed()

                # Broadcast update if status changed or every 2 seconds (but only if not completed)
                current_time = time.time()
                should_broadcast = (self.overall_status != old_status or
                                  current_time - self.last_update_time > 2.0)

                if should_broadcast:
                    # Schedule broadcast update in the event loop thread-safely
                    self._schedule_async_task(self._broadcast_update())
                    self.last_update_time = current_time

                # If task is fully completed, send final update and stop monitoring
                if is_completed and not self.final_update_sent:
                    self.logger.info(f"Task {self.task_id} fully completed with all passlinks - sending final update and stopping progress monitoring")
                    # Send one final update to ensure frontend has completion status
                    if not should_broadcast:  # Only send if we haven't already sent one above
                        self._schedule_async_task(self._broadcast_update())
                    self.final_update_sent = True
                    break
                elif is_completed and self.final_update_sent:
                    # Already sent final update, just break out of monitoring loop
                    self.logger.debug(f"Task {self.task_id} final update already sent - stopping monitoring")
                    break

                # If overall status is error, we stop all the tasks and stop
                # sending updates
                if self.overall_status == 'error':
                    self.logger.info(f"Overall status is error, stopping all tasks for {self.task_id}")
                    # Call stop_all_tasks directly since it's a synchronous method
                    self.stop_all_tasks()
                    break

                time.sleep(0.5)  # Monitor interval

            except ArithmeticError as e:
                self.logger.error(f"Error in task monitoring for {self.task_id}: {e}", exc_info=True)
                # Continue monitoring despite errors
                time.sleep(1.0)
    
    def _calculate_overall_status(self) -> tuple[str, str | None]:
        """
        Calculate overall status based on individual task states using >50% majority rule.

        This method aggregates the status of all individual Queue-IT tasks to determine
        the overall processing status. It implements the following logic:

        1. If no tasks exist, return 'initializing'
        2. If enough tasks completed (>= ticket_count), return 'completed'
        3. Find status that represents >50% of tasks, prioritized by importance
        4. Fall back to most common status if no majority exists

        Queue-IT Processing Stages (in priority order):
        - completed: Successfully obtained pass links (highest priority)
        - active: Actively processing in the queue
        - waiting: Waiting for queue progression
        - queued: Waiting in the queue system
        - solving: Working on queue bypass challenges
        - initializing: Setting up tasks
        - error: Failed due to errors (lowest priority)

        Returns:
            tuple[str, str | None]: A tuple containing:
                - status (str): The calculated overall status
                - error_message (str | None): Error message if status is 'error', None otherwise

        Note:
            The >50% majority rule ensures that the overall status reflects the state
            of most tasks, providing accurate progress representation to users.
        """
        if not self.tasks:
            return "initializing", None

        # Count tasks in each status
        status_counts = {}
        error_messages = []
        for task in self.tasks:
            print(f"Identifying status for task {task['task_index']}: {task['status']}")
            status = task['status']
            status_counts[status] = status_counts.get(status, 0) + 1
            if status == 'error' and task.get('error'):
                error_messages.append(task['error'])

        total_tasks = len(self.tasks)

        # Check if we have enough completed tasks
        completed_count = status_counts.get('completed', 0)
        if completed_count >= self.ticket_count:
            return 'completed', None

        # Define status priority (higher number = higher priority)
        # This determines which status takes precedence when multiple statuses exist
        status_priority = {
            'error': 0,          # Lowest priority - only shown if majority are errors
            'initializing': 1,   # Initial setup phase
            'solving': 2,        # Working on bypass challenges
            'queued': 3,         # Waiting in queue system
            'waiting': 4,        # Waiting for queue progression
            'active': 5,         # Actively processing
            'completed': 6       # Highest priority - successfully completed
        }

        # Find the status with >50% of tasks, prioritized by importance
        for status in sorted(status_priority.keys(), key=lambda x: status_priority[x], reverse=True):
            count = status_counts.get(status, 0)
            if count / total_tasks > 0.5:
                if status == 'error' and error_messages:
                    # Return most common error message for error status
                    from collections import Counter
                    error_counter = Counter(error_messages)
                    most_common = error_counter.most_common(1)
                    print(f"Most common error message: {most_common[0][0]}")
                    return status, most_common[0][0] if most_common else error_messages[0]
                print(f"Returning status: {status}")
                return status, None

        # Default to the most common status if no majority exists
        most_common_status = max(status_counts.items(), key=lambda x: x[1])[0] if status_counts else 'initializing'
        if most_common_status == 'error' and error_messages:
            # Return most common error message for error status
            from collections import Counter
            error_counter = Counter(error_messages)
            most_common = error_counter.most_common(1)
            print(f"Most common error message: {most_common[0][0]}")
            return most_common_status, most_common[0][0] if most_common else error_messages[0]
        print(f"Returning status: {most_common_status}")
        return most_common_status, None

    def _is_task_fully_completed(self) -> bool:
        """
        Check if the task is fully completed with all required passlinks collected.

        A task is considered fully completed when:
        1. The overall status is 'completed'
        2. All required passlinks have been successfully collected (>= ticket_count)

        Returns:
            bool: True if task is fully completed, False otherwise
        """
        # Check if overall status is completed
        if self.overall_status != 'completed':
            return False

        # Check if we have collected all required passlinks
        if len(self.pass_links) < self.ticket_count:
            return False

        # Additional validation: ensure passlinks are valid (not empty)
        valid_passlinks = [link for link in self.pass_links if link and link.strip()]
        if len(valid_passlinks) < self.ticket_count:
            self.logger.warning(f"Task {self.task_id} has {len(self.pass_links)} passlinks but only {len(valid_passlinks)} are valid")
            return False

        return True

    async def _broadcast_update(self):
        """Broadcast current progress to WebSocket clients"""
        if not self.websocket_server:
            self.logger.debug("No websocket server available for broadcast")
            return

        try:
            # Prepare task summary
            task_summary = []
            for task in self.tasks:
                task_summary.append({
                    'task_index': task['task_index'],
                    'status': task['status'],
                    'error': task['error'],
                    'duration': time.time() - task['start_time']
                })

            # Check if this is a completion update
            is_fully_completed = self._is_task_fully_completed()

            # Prepare update message
            update_data = {
                'type': 'queue_progress_update',
                'task_id': self.task_id,
                'timestamp': time.time(),
                'overall_status': self.overall_status,
                'error_message': self.error_message if hasattr(self, 'error_message') else None,
                'ticket_count': self.ticket_count,
                'pass_links_count': len(self.pass_links),
                'pass_links': self.pass_links,
                'duration': time.time() - self.start_time if self.start_time else 0,
                'is_final_update': is_fully_completed,  # Signal to frontend that no more updates will come
                'completion_time': getattr(self, 'completion_time', None)
            }

            # Log different messages based on completion status
            if is_fully_completed:
                self.logger.info(f"Broadcasting FINAL update for task {self.task_id}: status={self.overall_status}, pass_links={len(self.pass_links)}/{self.ticket_count}")
            else:
                self.logger.debug(f"Broadcasting update for task {self.task_id}: status={self.overall_status}, pass_links={len(self.pass_links)}/{self.ticket_count}")

            # Broadcast to WebSocket server
            await self.websocket_server.broadcast_to_client(self.client_id, update_data)

        except Exception as e:
            self.logger.error(f"Failed to broadcast update for task {self.task_id}: {e}", exc_info=True)
    
    async def _complete_all_tasks(self):
        """Complete the task manager when enough pass-links are obtained"""
        self.overall_status = 'completed'
        self.completion_time = time.time()

        self.logger.info(f"Task manager {self.task_id} completed successfully with {len(self.pass_links)} passlinks")

        # Mark all tasks as completed since we reached the target
        for task in self.tasks:
            if task['status'] not in ['completed', 'error', 'stopped']:
                task['status'] = 'completed'
                task['last_status_change'] = time.time()
                # Stop the individual handler
                if task.get('handler'):
                    task['handler'].stop()

        # Send completion webhook
        await self._send_completion_webhook()

        # Send final completion update to frontend
        await self._broadcast_update()
        self.final_update_sent = True

        # Mark as not running to stop the monitoring thread
        # Note: The monitoring thread will detect completion via _is_task_fully_completed()
        # and stop sending further updates, but we set this to ensure cleanup
        self.is_running = False

        # Stop all remaining tasks (synchronous method)
        self.stop_all_tasks()

# Create global singleton instance
# This global instance ensures consistent task management across the application
queue_task_manager = QueueTaskManager()
