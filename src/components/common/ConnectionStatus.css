.connection-status {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--status-color);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px var(--status-color);
}

.connection-status:hover {
  transform: scale(1.1);
}

.status-indicator {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  animation: pulse 2s infinite;
  box-shadow: none !important;
}

/* Tooltip style matching topbar-action-button */
.connection-status[title] {
  position: relative;
}

.connection-status[title]::after {
  content: attr(title);
  position: absolute;
  bottom: -36px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-accent);
  color: var(--color-text-primary);
  padding: 4px 10px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
  opacity: 0;
  pointer-events: none;
  z-index: var(--z-tooltip);
  transition: opacity var(--transition-fast);
}

.connection-status[title]:hover::after,
.connection-status[title]:focus::after {
  opacity: 1;
}

.connection-status[title]:focus:not(:focus-visible)::after {
  opacity: 0;
}

.status-tooltip {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  background: rgba(20, 20, 24, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: #fff;
  white-space: nowrap;
  z-index: 1000;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.status-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(20, 20, 24, 0.95);
}

.ping-info {
  margin-top: 4px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 4px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

/* Status-specific animations */
.connection-status[style*="retrying"] .status-indicator {
  animation: spin 2s linear infinite;
}

.connection-status[style*="connecting"] .status-indicator,
.connection-status[style*="reconnecting"] .status-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Status-specific styles */
.connection-status.connected {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.1);
}

.connection-status.connecting {
  border-color: rgba(251, 191, 36, 0.3);
  background: rgba(251, 191, 36, 0.1);
}

.connection-status.reconnecting {
  border-color: rgba(249, 115, 22, 0.3);
  background: rgba(249, 115, 22, 0.1);
}

.connection-status.retrying {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.1);
}

.connection-status.error {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
}

.connection-status.disconnected {
  border-color: rgba(107, 114, 128, 0.3);
  background: rgba(107, 114, 128, 0.1);
}

/* Status details */
.status-details {
  color: var(--color-text-secondary);
  font-size: 10px;
  margin-left: 4px;
  opacity: 0.8;
}

/* Retry button */
.retry-button {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: var(--color-text);
  cursor: pointer;
  font-size: 12px;
  padding: 2px 6px;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.retry-button:active {
  transform: scale(0.95);
}

/* Error details */
.error-details {
  color: rgba(239, 68, 68, 0.8);
  font-size: 10px;
  font-style: italic;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Animations */
.connection-status.retrying .status-icon {
  animation: spin 2s linear infinite;
}

.connection-status.connecting .status-icon,
.connection-status.reconnecting .status-icon {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Status-specific styles */
.connection-status.connected {
  border-color: rgba(0, 255, 136, 0.3);
  background: rgba(0, 255, 136, 0.05);
}

.connection-status.connected .status-text {
  color: #00ff88;
}

.connection-status.connecting {
  border-color: rgba(255, 193, 7, 0.3);
  background: rgba(255, 193, 7, 0.05);
}

.connection-status.connecting .status-text {
  color: #ffc107;
}

.connection-status.connecting .status-icon {
  animation: pulse 1s infinite;
}

.connection-status.error {
  border-color: rgba(255, 68, 68, 0.3);
  background: rgba(255, 68, 68, 0.05);
}

.connection-status.error .status-text {
  color: #ff4444;
}

.connection-status.disconnected {
  border-color: rgba(136, 136, 136, 0.3);
  background: rgba(136, 136, 136, 0.05);
}

.connection-status.disconnected .status-text {
  color: #888;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .connection-status {
    font-size: 11px;
    padding: 6px 10px;
    gap: 8px;
  }
  
  .ping-info {
    display: none;
  }
}
