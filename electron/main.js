const { app, BrowserWindow, ipcMain, shell } = require('electron')
const path = require('path');
const isDev = require('electron-is-dev');

// Keep a global reference to prevent garbage collection
let mainWindow;

/** #7C4DFF - #7C4DFF
 * Creates the main application window with proper sizing and configuration
 */ 
function createWindow() {
  // Create browser window with Kasper-Q styling
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 600,
    resizable: false,
    maximizable: false,
    fullscreenable: false,
    backgroundColor: '#0A0A0A', // Dark background matching our theme
    show: false, // Don't show until ready-to-show
    frame: false, // Custom frame for our cyberpunk UI
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Load the React app
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Show window when ready and centered
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.center();
    console.log('Main: Testing direct openExternal…')
    shell.openExternal('https://example.com', { activate: true })
      .then(() => console.log('Main: Direct openExternal succeeded'))
      .catch(err => console.error('Main: Direct openExternal failed', err))
  });

  // Handle window controls (custom frame)
  ipcMain.on('window-minimize', () => {
    mainWindow.minimize();
  });

  ipcMain.on('window-maximize', () => {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });

  ipcMain.on('window-close', () => {
    mainWindow.close();
  });

  // Open DevTools in development mode
  if (isDev) {
    mainWindow.webContents.openDevTools({ mode: 'detach' });
  }
}

// Create window when Electron is ready
app.whenReady().then(createWindow);

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// On macOS, recreate window when dock icon is clicked
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

ipcMain.handle('open-external-url', async (event, url) => {
  console.log('Main: 🎯 open-external-url handler fired for', url)
  try {
    // <-- HERE is where you pass { activate: true }
    await shell.openExternal(url, { activate: true })
    console.log('Main: ✅ shell.openExternal succeeded')
    return true
  } catch (err) {
    console.error('Main: ❌ shell.openExternal failed:', err)
    throw err
  }
})