/* Developer Mode Styles */
.developer-mode {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-lg);
  gap: var(--spacing-lg);
}

.mode-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.mode-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: 1.8rem;
  color: var(--accent-color);
  margin-bottom: var(--spacing-sm);
}

.mode-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.developer-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.tab-navigation {
  display: flex;
  background: var(--tertiary-bg);
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.tab-button.active {
  background: var(--secondary-bg);
  color: var(--accent-color);
  border-bottom-color: var(--accent-color);
}

.tab-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Console Styles */
.console-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: var(--font-mono);
  background: var(--primary-bg);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.console-output {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  background: var(--primary-bg);
  font-size: 0.85rem;
  line-height: 1.4;
}

.console-line {
  display: flex;
  margin-bottom: 4px;
  word-break: break-all;
}

.console-line .timestamp {
  color: var(--text-muted);
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
  font-size: 0.75rem;
}

.console-line .text {
  flex: 1;
}

.console-line.command .text {
  color: var(--accent-color);
  font-weight: 500;
}

.console-line.response .text {
  color: var(--text-primary);
}

.console-line.info .text {
  color: var(--info-color);
}

.console-line.success .text {
  color: var(--success-color);
}

.console-line.error .text {
  color: var(--error-color);
}

.console-input {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-color);
  gap: var(--spacing-sm);
}

.prompt {
  color: var(--accent-color);
  font-weight: 600;
  flex-shrink: 0;
}

.command-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-family: var(--font-mono);
  font-size: 0.9rem;
  outline: none;
}

.command-input::placeholder {
  color: var(--text-muted);
}

/* API Testing Styles */
.api-testing {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.api-testing h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.api-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.api-form .input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.api-form label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

.api-form select,
.api-form input,
.api-form textarea {
  font-family: var(--font-mono);
}

.api-form textarea {
  resize: vertical;
  min-height: 120px;
}

/* Logs Viewer Styles */
.logs-viewer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.logs-viewer h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.log-entries {
  flex: 1;
  overflow-y: auto;
  background: var(--primary-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-family: var(--font-mono);
  font-size: 0.85rem;
}

.log-entry {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--text-muted);
  flex-shrink: 0;
  width: 120px;
}

.log-level {
  flex-shrink: 0;
  width: 60px;
  font-weight: 600;
  text-transform: uppercase;
}

.log-entry.info .log-level {
  color: var(--info-color);
}

.log-entry.warning .log-level {
  color: var(--warning-color);
}

.log-entry.error .log-level {
  color: var(--error-color);
}

.log-message {
  flex: 1;
  color: var(--text-primary);
}

/* Config Editor Styles */
.config-editor {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.config-editor h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.config-section {
  background: var(--tertiary-bg);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.config-section h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
}

.config-section .input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.config-section .input-group:last-child {
  margin-bottom: 0;
}

.config-section label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .developer-mode {
    padding: var(--spacing-md);
  }
  
  .tab-navigation {
    flex-wrap: wrap;
  }
  
  .tab-button {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.8rem;
  }
  
  .tab-content {
    padding: var(--spacing-md);
  }
  
  .console-output {
    font-size: 0.8rem;
  }
  
  .log-entry {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .log-time,
  .log-level {
    width: auto;
  }
}
