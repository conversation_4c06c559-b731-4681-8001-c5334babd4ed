/**
 * LicenseStorage.js
 * Secure license key persistence system for Kasper-Q
 */

class LicenseStorage {
  constructor() {
    this.storageKey = 'kasper-q-license-key';
    this.licenseInfoKey = 'kasper-q-license-info';
    
    // Simple obfuscation key (not cryptographically secure, but prevents casual tampering)
    this.obfuscationKey = 'KasperQ2024SecureStorage';
  }

  /**
   * Simple XOR-based obfuscation for license key
   * Note: This is not cryptographically secure, just prevents casual viewing
   */
  _obfuscate(text) {
    if (!text) return '';
    
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i);
      const keyChar = this.obfuscationKey.charCodeAt(i % this.obfuscationKey.length);
      result += String.fromCharCode(charCode ^ keyChar);
    }
    
    // Base64 encode to make it look less suspicious
    return btoa(result);
  }

  /**
   * Deobfuscate the stored license key
   */
  _deobfuscate(obfuscatedText) {
    if (!obfuscatedText) return '';
    
    try {
      // Base64 decode first
      const decoded = atob(obfuscatedText);
      
      let result = '';
      for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i);
        const keyChar = this.obfuscationKey.charCodeAt(i % this.obfuscationKey.length);
        result += String.fromCharCode(charCode ^ keyChar);
      }
      
      return result;
    } catch (error) {
      console.error('Failed to deobfuscate license key:', error);
      return '';
    }
  }

  /**
   * Store license key and associated information securely
   */
  storeLicense(licenseKey, licenseInfo = null) {
    try {
      if (!licenseKey) {
        console.error('Cannot store empty license key');
        return false;
      }

      // Obfuscate and store the license key
      const obfuscatedKey = this._obfuscate(licenseKey);
      localStorage.setItem(this.storageKey, obfuscatedKey);

      // Store license information if provided
      if (licenseInfo) {
        const licenseData = {
          ...licenseInfo,
          storedAt: Date.now(),
          version: '1.0'
        };
        localStorage.setItem(this.licenseInfoKey, JSON.stringify(licenseData));
      }

      console.log('🔐 License key stored successfully');
      return true;
    } catch (error) {
      console.error('Failed to store license key:', error);
      return false;
    }
  }

  /**
   * Retrieve stored license key
   */
  getLicenseKey() {
    try {
      const obfuscatedKey = localStorage.getItem(this.storageKey);
      if (!obfuscatedKey) {
        return null;
      }

      const licenseKey = this._deobfuscate(obfuscatedKey);
      return licenseKey || null;
    } catch (error) {
      console.error('Failed to retrieve license key:', error);
      return null;
    }
  }

  /**
   * Retrieve stored license information
   */
  getLicenseInfo() {
    try {
      const licenseInfoStr = localStorage.getItem(this.licenseInfoKey);
      if (!licenseInfoStr) {
        return null;
      }

      return JSON.parse(licenseInfoStr);
    } catch (error) {
      console.error('Failed to retrieve license info:', error);
      return null;
    }
  }

  /**
   * Check if a license key is stored
   */
  hasStoredLicense() {
    return !!this.getLicenseKey();
  }

  /**
   * Remove stored license key and information
   */
  clearLicense() {
    try {
      localStorage.removeItem(this.storageKey);
      localStorage.removeItem(this.licenseInfoKey);
      console.log('🔐 License key cleared from storage');
      return true;
    } catch (error) {
      console.error('Failed to clear license key:', error);
      return false;
    }
  }

  /**
   * Check if stored license is expired based on stored information
   */
  isStoredLicenseExpired() {
    const licenseInfo = this.getLicenseInfo();
    if (!licenseInfo || !licenseInfo.renewalPeriodEnd) {
      return false; //true; // Consider expired if no info available
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return false// licenseInfo.renewalPeriodEnd < currentTime;
  }

  /**
   * Get license expiration status and time remaining
   */
  getLicenseExpirationInfo() {
    const licenseInfo = this.getLicenseInfo();
    if (!licenseInfo || !licenseInfo.renewalPeriodEnd) {
      return {
        isExpired: true,
        timeRemaining: 0,
        expirationDate: null
      };
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const timeRemaining = licenseInfo.renewalPeriodEnd - currentTime;
    const isExpired = timeRemaining <= 0;

    return {
      isExpired,
      timeRemaining: Math.max(0, timeRemaining),
      expirationDate: new Date(licenseInfo.renewalPeriodEnd * 1000),
      licenseInfo
    };
  }

  /**
   * Update stored license information (useful for refreshing data from server)
   */
  updateLicenseInfo(newLicenseInfo) {
    const existingInfo = this.getLicenseInfo();
    if (!existingInfo) {
      console.warn('No existing license info to update');
      return false;
    }

    const updatedInfo = {
      ...existingInfo,
      ...newLicenseInfo,
      updatedAt: Date.now()
    };

    try {
      localStorage.setItem(this.licenseInfoKey, JSON.stringify(updatedInfo));
      console.log('🔐 License info updated successfully');
      return true;
    } catch (error) {
      console.error('Failed to update license info:', error);
      return false;
    }
  }

  /**
   * Validate stored license format (basic validation)
   */
  isValidLicenseFormat(licenseKey) {
    if (!licenseKey) return false;
    
    // Basic format validation for Kasper-Q license keys
    // Expected format: K-XXXXXX-XXXXXXXX-XXXXXXX
    const licensePattern = /^K-[A-Z0-9]{6}-[A-Z0-9]{8}-[A-Z0-9]{7}$/i;
    return licensePattern.test(licenseKey);
  }

  /**
   * Get storage statistics for debugging
   */
  getStorageInfo() {
    return {
      hasLicenseKey: this.hasStoredLicense(),
      hasLicenseInfo: !!this.getLicenseInfo(),
      isExpired: this.isStoredLicenseExpired(),
      expirationInfo: this.getLicenseExpirationInfo(),
      storageKeys: {
        licenseKey: this.storageKey,
        licenseInfo: this.licenseInfoKey
      }
    };
  }
}

// Create and export singleton instance
const licenseStorage = new LicenseStorage();
export default licenseStorage;
