# WebSocket Retry System Implementation

## Overview

This document describes the robust WebSocket connection retry logic implemented for the React UI to handle various connection scenarios with automatic recovery and user feedback.

## Features Implemented

### 1. Exponential Backoff Strategy
- **Base delay**: 1 second
- **Maximum delay**: 30 seconds  
- **Backoff factor**: 2x (1s → 2s → 4s → 8s → 16s → 30s)
- **Jitter**: ±10% randomization to prevent thundering herd
- **Infinite retries**: Continuously attempts reconnection

### 2. Connection States
- `disconnected` - No connection, not attempting
- `connecting` - Initial connection attempt in progress
- `connected` - Successfully connected and operational
- `reconnecting` - Lost connection, attempting to reconnect
- `retrying` - Waiting for next retry attempt (with countdown)
- `error` - Connection failed with error details

### 3. Visual Status Indicators
- **Connection status component** with real-time updates
- **Retry countdown timer** showing seconds until next attempt
- **Connection duration** display for active connections
- **Error details** with truncated error messages
- **Manual retry button** for user-initiated reconnection
- **Animated status icons** (spinning, pulsing) for visual feedback

### 4. Health Monitoring
- **Ping/pong heartbeat** every 30 seconds
- **Heartbeat timeout** detection (10 seconds)
- **Missed heartbeat tracking** (max 3 before reconnection)
- **Connection quality assessment** based on response times

### 5. State Preservation
- **Message queuing** during disconnection (max 100 messages)
- **Automatic message replay** on reconnection
- **Context state maintenance** throughout retry cycles
- **Graceful degradation** with cached/fallback data

## Implementation Details

### BackendService Enhancements

#### Retry Configuration
```javascript
retryConfig: {
  maxAttempts: Infinity,    // Continuously retry
  baseDelay: 1000,         // Start with 1 second
  maxDelay: 30000,         // Maximum 30 seconds
  backoffFactor: 2,        // Exponential backoff
  jitterFactor: 0.1        // Add randomness
}
```

#### Key Methods
- `_calculateRetryDelay()` - Exponential backoff with jitter
- `_startRetryProcess()` - Initiates retry sequence
- `setAutoReconnect(enabled)` - Enable/disable auto-retry
- `resetRetryAttempts()` - Reset retry counter on success
- `getConnectionMetadata()` - Detailed connection information

#### Enhanced Heartbeat System
- Timeout detection for ping responses
- Missed heartbeat counting
- Automatic reconnection on heartbeat failure
- Server status tracking from ping responses

### Connection Status Component

#### Real-time Updates
- Connection metadata polling every second
- Retry countdown timer with visual feedback
- Error message display with truncation
- Manual retry button for user control

#### Visual States
- **Connected** (🟢): Green with connection duration
- **Connecting** (🟡): Yellow with pulsing animation
- **Reconnecting** (🟠): Orange with attempt counter
- **Retrying** (🔄): Blue with spinning icon and countdown
- **Error** (🔴): Red with error details
- **Disconnected** (⚫): Gray with retry button

### AppContext Integration

#### State Management
- Maps BackendService states to UI states
- Preserves existing functionality
- Handles state transitions smoothly
- Maintains backward compatibility

#### Auto-initialization
- Automatic connection on app startup
- Retry system activation on failures
- Clean shutdown on app unmount
- Resource cleanup and timeout clearing

## Usage Scenarios

### 1. UI Starts Before Server
```
disconnected → connecting → error → retrying (1s) → 
connecting → error → retrying (2s) → connecting → 
error → retrying (4s) → ... → connected
```

### 2. Server Restart During Operation
```
connected → reconnecting → retrying (1s) → 
connecting → connected
```

### 3. Network Interruption
```
connected → (heartbeat timeout) → reconnecting → 
retrying (1s) → connecting → connected
```

### 4. Persistent Connection Issues
```
connected → error → retrying (1s) → error → 
retrying (2s) → error → retrying (4s) → ... 
(continues with exponential backoff up to 30s)
```

## Configuration Options

### Retry Behavior
- **Auto-reconnect**: Can be enabled/disabled
- **Retry delays**: Configurable base/max delays
- **Backoff factor**: Adjustable exponential multiplier
- **Jitter**: Configurable randomization percentage

### Health Monitoring
- **Heartbeat interval**: Default 30 seconds
- **Heartbeat timeout**: Default 10 seconds
- **Max missed heartbeats**: Default 3

### UI Feedback
- **Status update frequency**: 1 second
- **Error message length**: Truncated to 30 characters
- **Countdown precision**: 1 second intervals

## Debug Tools

### ConnectionDebug Component
- Real-time connection status monitoring
- Retry attempt tracking and history
- Manual connection controls
- Configuration display
- Connection log with timestamps
- Error details and metadata

### Debug Features
- **Status grid**: Current connection state details
- **Control buttons**: Manual reconnect/disconnect
- **Auto-reconnect toggle**: Enable/disable retry system
- **Connection log**: Last 10 state changes with timestamps
- **Configuration display**: Current retry settings

## Integration Guide

### Adding to Existing Components
```javascript
import ConnectionDebug from './components/debug/ConnectionDebug';

// Add to your main app component
<ConnectionDebug />
```

### Using Connection Status
```javascript
import { useAppContext } from './context/AppContext';

const { connectionStatus, backendConnected } = useAppContext();

// Handle different states
switch (connectionStatus) {
  case 'connected':
    // Show normal UI
    break;
  case 'retrying':
    // Show retry message
    break;
  case 'error':
    // Show error state
    break;
}
```

### Manual Connection Control
```javascript
import BackendService from './services/BackendService';

// Manual reconnect
BackendService.reconnect();

// Disable auto-reconnect
BackendService.setAutoReconnect(false);

// Get connection details
const metadata = BackendService.getConnectionMetadata();
```

## Benefits

### User Experience
- **Seamless reconnection** without user intervention
- **Clear status feedback** with visual indicators
- **Manual control** when needed
- **Graceful degradation** during outages

### Developer Experience
- **Comprehensive logging** for debugging
- **Debug tools** for development
- **Configurable behavior** for different environments
- **Backward compatibility** with existing code

### System Reliability
- **Automatic recovery** from network issues
- **Resource cleanup** to prevent memory leaks
- **Connection health monitoring** for early detection
- **Exponential backoff** to reduce server load

## Testing

The retry system can be tested by:
1. Starting the UI before the WebSocket server
2. Stopping/restarting the WebSocket server during operation
3. Simulating network interruptions
4. Using the debug component to monitor behavior
5. Testing manual reconnection controls

The system provides comprehensive logging and visual feedback to verify correct operation in all scenarios.
