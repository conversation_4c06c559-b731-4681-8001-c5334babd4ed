/**
 * Shared Message Schemas for Kasper-Q WebSocket Communication
 * 
 * This module defines the standardized message format and schemas used for communication
 * between the React frontend and Python backend. It provides type safety, validation,
 * and versioning support for all WebSocket messages.
 * 
 * Author: Ka<PERSON>-Q Development Team
 * Created: 2024
 * Version: 1.0
 */

// Message types enumeration
export enum MessageType {
  // Connection management
  CONNECTION_ESTABLISHED = "connection_established",
  CONNECTION_REJECTED = "connection_rejected",
  AUTHENTICATION_FAILURE = "authentication_failure",
  
  // Request/Response pairs
  REQUEST = "request",
  RESPONSE = "response",
  ERROR = "error",
  
  // Health checks
  PING = "ping",
  PONG = "pong",
  
  // Queue operations
  QUEUE_ENTRY_STARTED = "queue_entry_started",
  QUEUE_ENTRY_STOPPED = "queue_entry_stopped",
  QUEUE_PROGRESS_UPDATE = "queue_progress_update",
  QUEUE_STATUS = "queue_status",
  
  // License management
  VALIDATE_LICENSE_RESPONSE = "validate_license_response",
  LICENSE_UPDATED = "license_updated",
  
  // Settings management
  SETTINGS_DATA = "settings_data",
  DOCUMENTATION_DATA = "documentation_data",
  
  // Connection status
  CONNECTION_STATUS = "connection_status"
}

// Standardized error codes
export enum ErrorCode {
  // Authentication errors
  AUTHENTICATION_REQUIRED = "AUTH_001",
  INVALID_LICENSE = "AUTH_002",
  LICENSE_EXPIRED = "AUTH_003",
  
  // Connection errors
  CONNECTION_REJECTED = "CONN_001",
  SINGLE_CLIENT_VIOLATION = "CONN_002",
  CONNECTION_TIMEOUT = "CONN_003",
  
  // Message errors
  INVALID_MESSAGE_FORMAT = "MSG_001",
  UNKNOWN_COMMAND = "MSG_002",
  MISSING_PARAMETERS = "MSG_003",
  INVALID_PARAMETERS = "MSG_004",
  
  // Queue operation errors
  QUEUE_START_FAILED = "QUEUE_001",
  QUEUE_STOP_FAILED = "QUEUE_002",
  QUEUE_NOT_FOUND = "QUEUE_003",
  
  // System errors
  INTERNAL_ERROR = "SYS_001",
  SERVICE_UNAVAILABLE = "SYS_002",
  RATE_LIMITED = "SYS_003"
}

// Message metadata interface
export interface MessageMetadata {
  version: string;
  timestamp: number;
  correlationId?: string;
  retryCount: number;
  priority: number; // 0 = normal, 1 = high, 2 = critical
}

// Error details interface
export interface ErrorDetails {
  code: ErrorCode;
  message: string;
  details?: Record<string, any>;
  recoverySuggestions?: string[];
}

// Base message interface
export interface BaseMessage {
  type: MessageType;
  metadata: MessageMetadata;
  payload: Record<string, any>;
  error?: ErrorDetails;
}

// Request message interface
export interface RequestMessage extends BaseMessage {
  type: MessageType.REQUEST;
  payload: {
    command: string;
    params: Record<string, any>;
    requestId: string;
  };
}

// Response message interface
export interface ResponseMessage extends BaseMessage {
  type: MessageType.RESPONSE;
  payload: {
    requestId: string;
    success: boolean;
    data?: any;
  };
}

// Broadcast message interface (no request correlation)
export interface BroadcastMessage extends BaseMessage {
  type: Exclude<MessageType, MessageType.REQUEST | MessageType.RESPONSE>;
}

// Command definitions for type safety
export const Commands = {
  // Health checks
  PING: "ping",
  GET_CONNECTION_STATUS: "get_connection_status",

  // License operations
  VALIDATE_LICENSE: "validate_license",
  LOGOUT: "logout",

  // Settings operations
  GET_SETTINGS: "get_settings",
  UPDATE_SETTINGS: "update_settings",
  GET_DOCUMENTATION: "get_documentation",

  // Queue operations
  START_QUEUE_ENTRY: "start_queue_entry",
  STOP_QUEUE_ENTRY: "stop_queue_entry",
  GET_QUEUE_STATUS: "get_queue_status"
} as const;

// Type for command names
export type CommandName = typeof Commands[keyof typeof Commands];

// Helper functions
export function createMessageMetadata(options: Partial<MessageMetadata> = {}): MessageMetadata {
  return {
    version: "1.0",
    timestamp: Date.now(),
    correlationId: options.correlationId || generateUUID(),
    retryCount: options.retryCount || 0,
    priority: options.priority || 0
  };
}

export function createRequestMessage(
  command: CommandName,
  params: Record<string, any> = {},
  requestId?: string,
  metadata?: MessageMetadata
): RequestMessage {
  return {
    type: MessageType.REQUEST,
    metadata: metadata || createMessageMetadata(),
    payload: {
      command,
      params,
      requestId: requestId || generateUUID()
    }
  };
}

export function createResponseMessage(
  requestId: string,
  success: boolean = true,
  data?: any,
  error?: ErrorDetails,
  metadata?: MessageMetadata
): ResponseMessage {
  return {
    type: MessageType.RESPONSE,
    metadata: metadata || createMessageMetadata(),
    payload: {
      requestId,
      success,
      data
    },
    error
  };
}

export function createErrorResponse(
  requestId: string,
  errorCode: ErrorCode,
  message: string,
  details?: Record<string, any>,
  recoverySuggestions?: string[]
): ResponseMessage {
  const error: ErrorDetails = {
    code: errorCode,
    message,
    details,
    recoverySuggestions
  };
  
  return createResponseMessage(requestId, false, undefined, error);
}

// Schema validation
export function validateMessageSchema(data: any): data is BaseMessage {
  return (
    typeof data === 'object' &&
    data !== null &&
    'type' in data &&
    'metadata' in data &&
    'payload' in data &&
    Object.values(MessageType).includes(data.type)
  );
}

export function isRequestMessage(message: BaseMessage): message is RequestMessage {
  return message.type === MessageType.REQUEST;
}

export function isResponseMessage(message: BaseMessage): message is ResponseMessage {
  return message.type === MessageType.RESPONSE;
}

export function isBroadcastMessage(message: BaseMessage): message is BroadcastMessage {
  return !isRequestMessage(message) && !isResponseMessage(message);
}

// Utility function to generate UUIDs
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Type guards for specific message types
export function isQueueProgressUpdate(message: BaseMessage): boolean {
  return message.type === MessageType.QUEUE_PROGRESS_UPDATE;
}

export function isConnectionEstablished(message: BaseMessage): boolean {
  return message.type === MessageType.CONNECTION_ESTABLISHED;
}

export function isConnectionRejected(message: BaseMessage): boolean {
  return message.type === MessageType.CONNECTION_REJECTED;
}

export function isPongMessage(message: BaseMessage): boolean {
  return message.type === MessageType.PONG;
}

// Message transformation hooks interface
export interface MessageTransformHook {
  name: string;
  priority: number;
  encode?: (message: BaseMessage) => Promise<BaseMessage> | BaseMessage;
  decode?: (message: BaseMessage) => Promise<BaseMessage> | BaseMessage;
}

// Rate limiting configuration
export interface RateLimitConfig {
  maxMessages: number;
  windowMs: number;
  enabled: boolean;
}
