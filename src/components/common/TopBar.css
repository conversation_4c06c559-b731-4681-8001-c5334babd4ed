.kasper-topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 48px;
  width: 100vw;
  background: #0D0D0D;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 200;
  user-select: none;
  overflow: visible;
  padding: 0 24px;
  box-shadow: none;
  -webkit-app-region: drag;
}

.kasper-topbar__controls {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 100%;
  -webkit-app-region: no-drag;
  z-index: 1;
}

.kasper-topbar__center {
  position: absolute;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  pointer-events: auto;
  -webkit-app-region: drag;
}

.mac-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.10) inset, 0 0 0 1px rgba(255,255,255,0.08) inset;
  position: relative;
  transition: background 0.18s ease-out, box-shadow 0.18s ease-out;
  border: none;
  outline: none;
  cursor: pointer;
  -webkit-app-region: no-drag !important;
}
.mac-dot--red { background: #FF5F57; box-shadow: 0 1px 4px 0 rgba(0,0,0,0.10) inset, 0 0 0 1px #C1463C inset; }
.mac-dot--yellow { background: #FFBD2E; box-shadow: 0 1px 4px 0 rgba(0,0,0,0.10) inset, 0 0 0 1px #B78B1E inset; }
.mac-dot--green { background: #28C840; box-shadow: 0 1px 4px 0 rgba(0,0,0,0.10) inset, 0 0 0 1px #1A7F2E inset; }

.mac-dot:focus-visible {
  outline: 2px solid #7C4DFF;
  outline-offset: 2px;
}
.mac-dot:hover {
  filter: brightness(1.08) saturate(1.2);
  box-shadow: 0 2px 8px 0 rgba(138,0,238,0.18);
}

.mac-dot__icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  animation: fadeInDotIcon 0.18s cubic-bezier(0.4,0,0.2,1) forwards;
  pointer-events: none;
}
.mac-dot__icon svg {
  width: 7px;
  height: 7px;
  display: block;
}

@keyframes fadeInDotIcon {
  from { opacity: 0; transform: scale(0.7); }
  to { opacity: 1; transform: scale(1); }
}

.kasper-topbar__title {
  flex: 1;
  text-align: center;
  font-family: var(--font-main);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  color: #EAEAEA;
  font-size: 1.05rem;
  pointer-events: none;
}

/* Remove right section */
.kasper-topbar__right {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 100%;
  -webkit-app-region: no-drag;
}

.topbar-action-button {
  background: transparent;
  border: none;
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background var(--transition-fast), color var(--transition-fast);
  font-size: 1.1rem;
  outline: none;
  -webkit-app-region: no-drag;
}
.topbar-action-button:hover, .topbar-action-button:focus {
  background: rgba(255, 255, 255, 0.08);
  color: var(--color-text-primary);
}
.topbar-action-button:active {
  background: rgba(255, 255, 255, 0.12);
  color: var(--color-text-primary);
}
.topbar-action-button:focus-visible {
  outline: 2px solid var(--color-primary-light);
  outline-offset: 2px;
}
.topbar-action-button svg {
  display: block;
}

/* Tooltip style for accessibility */
.topbar-action-button[title] {
  position: relative;
}

.topbar-action-button[title]::after {
  content: attr(title);
  position: absolute;
  bottom: -36px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-accent);
  color: var(--color-text-primary);
  padding: 4px 10px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  white-space: nowrap;
  box-shadow: var(--shadow-md);
  opacity: 0;
  pointer-events: none;
  z-index: var(--z-tooltip);
  transition: opacity var(--transition-fast);
}

.topbar-action-button[title]:hover::after,
.topbar-action-button[title]:focus::after {
  opacity: 1;
}

.topbar-action-button[title]:focus:not(:focus-visible)::after {
  opacity: 0;
}

.kasper-logo-container {
  position: absolute;
  left: calc(50% + 40px); /* Offset by half the width of traffic lights */
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 1;
}

.kasper-logo {
  height: 24px;
  width: auto;
  opacity: 0.9;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  animation: ghostFloat 6s ease-in-out infinite;
}

.kasper-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(124, 77, 255, 0.2),
    transparent
  );
  animation: lightPass 4s ease-in-out infinite;
}

.kasper-logo:hover {
  opacity: 1;
  filter: drop-shadow(0 0 8px rgba(124, 77, 255, 0.3));
  transform: scale(1.05);
}

@keyframes ghostFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes lightPass {
  0% {
    left: -100%;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    left: 200%;
    opacity: 0;
  }
}

.ghost-logo-container {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}


.ghost-tooltip {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  animation: tooltipFade 0.2s ease-out;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes tooltipFade {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
} 