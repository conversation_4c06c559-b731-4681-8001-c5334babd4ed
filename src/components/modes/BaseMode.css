/* Base Mode Container */
.base-mode-container {
  width: 100%;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #0B0A12;
}

/* Interactive Background */
.base-mode-container::before {
  content: '';
  position: absolute;
  inset: -50%;
  background: radial-gradient(
    800px circle at var(--mouse-x) var(--mouse-y),
    rgba(124, 77, 255, 0.15),
    transparent 40%
  );
  z-index: 0;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: blur(60px);
  opacity: 0.6;
  pointer-events: none;
}

.base-mode-container::after {
  content: '';
  position: absolute;
  inset: -50%;
  background: radial-gradient(
    circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(124, 77, 255, 0.05) 0%,
    rgba(124, 77, 255, 0.02) 25%,
    transparent 45%
  );
  z-index: 0;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  filter: blur(80px);
  opacity: 0.4;
  pointer-events: none;
  transform: scale(1.5);
}

#base-input-card {
  z-index: 15;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 0;
  line-height: 1;
}

#base-input-card input {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(11, 10, 18, 0.4);
  border: none;
  color: #fff;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 0 0 1px rgba(124, 77, 255, 0.2),
    0 0 15px rgba(124, 77, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  margin: 0;
  line-height: 1;
  position: relative;
  overflow: hidden;
}

#base-input-card input::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent,
    rgba(124, 77, 255, 0.1),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  pointer-events: none;
}

#base-input-card input:first-of-type {
  border-top: 1px solid rgba(124, 77, 255, 0.3);
  border-right: 1px solid rgba(124, 77, 255, 0.3);
  border-radius: 2px 2px 0 0;
  clip-path: polygon(
    0% 0%,
    100% 0%,
    100% calc(100% - 6px),
    calc(100% - 6px) 100%,
    0% 100%
  );
  margin-bottom: -2px;
  padding-bottom: calc(1rem + 2px);
  box-shadow: 
    0 0 0 1px rgba(124, 77, 255, 0.2),
    0 0 15px rgba(124, 77, 255, 0.1),
    2px 0 10px rgba(124, 77, 255, 0.2),
    0 2px 10px rgba(124, 77, 255, 0.2);
}

#base-input-card input:last-of-type {
  border-bottom: 1px solid rgba(124, 77, 255, 0.3);
  border-left: 1px solid rgba(124, 77, 255, 0.3);
  border-radius: 0 0 2px 2px;
  clip-path: polygon(
    0% 0%,
    100% 0%,
    100% 100%,
    0% 100%,
    0% 6px,
    6px 0%
  );
  padding-top: calc(1rem + 2px);
  box-shadow: 
    0 0 0 1px rgba(124, 77, 255, 0.2),
    0 0 15px rgba(124, 77, 255, 0.1),
    -2px 0 10px rgba(124, 77, 255, 0.2),
    0 -2px 10px rgba(124, 77, 255, 0.2);
}

#base-input-card input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  text-shadow: 
    0 0 10px rgba(124, 77, 255, 0.3),
    0 0 20px rgba(124, 77, 255, 0.2);
  font-style: italic;
  letter-spacing: 0.5px;
  font-size: 0.95rem;
  line-height: 1;
  font-weight: 300;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.5),
    rgba(124, 77, 255, 0.5)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

#base-input-card input:focus {
  outline: none;
  background: rgba(11, 10, 18, 0.5);
  box-shadow: 
    0 0 0 1px rgba(124, 77, 255, 0.4),
    0 0 20px rgba(124, 77, 255, 0.2),
    2px 0 15px rgba(124, 77, 255, 0.3),
    0 2px 15px rgba(124, 77, 255, 0.3);
  transform: translateY(-1px) scale(1.01);
  z-index: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  animation: inputPulse 2s infinite;
}

#base-input-card input:focus::after {
  transform: translateX(100%);
}

#base-input-card input:focus::placeholder {
  opacity: 0.7;
  text-shadow: 
    0 0 15px rgba(124, 77, 255, 0.4),
    0 0 30px rgba(124, 77, 255, 0.3);
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.6),
    rgba(124, 77, 255, 0.6)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes inputPulse {
  0% {
    box-shadow: 
      0 0 0 1px rgba(124, 77, 255, 0.4),
      0 0 20px rgba(124, 77, 255, 0.2),
      2px 0 15px rgba(124, 77, 255, 0.3),
      0 2px 15px rgba(124, 77, 255, 0.3);
  }
  50% {
    box-shadow: 
      0 0 0 1px rgba(124, 77, 255, 0.4),
      0 0 30px rgba(124, 77, 255, 0.3),
      2px 0 20px rgba(124, 77, 255, 0.4),
      0 2px 20px rgba(124, 77, 255, 0.4);
  }
  100% {
    box-shadow: 
      0 0 0 1px rgba(124, 77, 255, 0.4),
      0 0 20px rgba(124, 77, 255, 0.2),
      2px 0 15px rgba(124, 77, 255, 0.3),
      0 2px 15px rgba(124, 77, 255, 0.3);
  }
}

#base-input-card input:hover {
  background: rgba(11, 10, 18, 0.45);
  box-shadow: 
    0 0 0 1px rgba(124, 77, 255, 0.3),
    0 0 20px rgba(124, 77, 255, 0.15),
    2px 0 12px rgba(124, 77, 255, 0.25),
    0 2px 12px rgba(124, 77, 255, 0.25);
  transform: translateY(-1px);
  z-index: 1;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Error Message */
#base-input-card .error-message {
  color: #ff4d4d;
  font-size: 0.85rem;
  margin: 0.25rem 0;
  text-shadow: 
    0 0 10px rgba(255, 77, 77, 0.4),
    0 0 15px rgba(255, 77, 77, 0.2);
  position: relative;
  z-index: 15;
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  line-height: 1;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

/* Floating Circles */
.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(124, 77, 255, 0.05);
  animation: float var(--duration) ease-in-out infinite, pulse 4s ease-in-out infinite;
  animation-delay: var(--delay);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  z-index: 0;
  pointer-events: none;
  filter: blur(8px);
}

.floating-circle:nth-child(1) {
  width: 200px;
  height: 200px;
  top: 15%;
  left: 10%;
  --duration: 20s;
  --delay: 0s;
  background: radial-gradient(circle at center, rgba(124, 77, 255, 0.15) 0%, rgba(124, 77, 255, 0.05) 70%);
  box-shadow: 
    0 0 40px rgba(124, 77, 255, 0.2),
    0 0 80px rgba(124, 77, 255, 0.1);
}

.floating-circle:nth-child(2) {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  --duration: 25s;
  --delay: -5s;
  background: radial-gradient(circle at center, rgba(124, 77, 255, 0.12) 0%, rgba(124, 77, 255, 0.03) 70%);
  box-shadow: 
    0 0 30px rgba(124, 77, 255, 0.2),
    0 0 60px rgba(124, 77, 255, 0.1);
}

.floating-circle:nth-child(3) {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  --duration: 18s;
  --delay: -2s;
  background: radial-gradient(circle at center, rgba(124, 77, 255, 0.1) 0%, rgba(124, 77, 255, 0.02) 70%);
  box-shadow: 
    0 0 20px rgba(124, 77, 255, 0.2),
    0 0 40px rgba(124, 77, 255, 0.1);
}

.floating-circle:nth-child(4) {
  width: 180px;
  height: 180px;
  top: 30%;
  right: 25%;
  --duration: 22s;
  --delay: -8s;
  background: radial-gradient(circle at center, rgba(124, 77, 255, 0.13) 0%, rgba(124, 77, 255, 0.04) 70%);
  box-shadow: 
    0 0 35px rgba(124, 77, 255, 0.2),
    0 0 70px rgba(124, 77, 255, 0.1);
}

.floating-circle:nth-child(5) {
  width: 120px;
  height: 120px;
  bottom: 30%;
  right: 30%;
  --duration: 28s;
  --delay: -12s;
  background: radial-gradient(circle at center, rgba(124, 77, 255, 0.11) 0%, rgba(124, 77, 255, 0.03) 70%);
  box-shadow: 
    0 0 25px rgba(124, 77, 255, 0.2),
    0 0 50px rgba(124, 77, 255, 0.1);
}

.floating-circle:hover {
  transform: scale(1.1);
  filter: brightness(1.2) blur(8px);
}

/* Base Mode Content */
.base-mode-content {
  position: relative;
  z-index: 15;
  width: 100%;
  max-width: 800px;
  padding: 2rem;
  animation: fadeIn 0.5s ease-out;
}

.base-mode-header {
  text-align: center;
  margin-bottom: 2rem;
  animation: slideDown 0.5s ease-out;
}

.base-mode-header h1 {
  color: #fff;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-shadow: 
    0 0 10px rgba(124, 77, 255, 0.5),
    0 0 20px rgba(124, 77, 255, 0.3);
}

.base-mode-header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

/* Form Styles */
.base-mode-form {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(124, 77, 255, 0.1);
  animation: fadeIn 0.5s ease-out 0.2s both;
  position: relative;
  z-index: 15;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  color: #fff;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  text-shadow: 0 0 10px rgba(124, 77, 255, 0.3);
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.form-group input:focus {
  outline: none;
  border-color: rgba(124, 77, 255, 0.5);
  box-shadow: 
    0 0 0 2px rgba(124, 77, 255, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.form-group input.error {
  border-color: #ff4444;
  box-shadow: 
    0 0 0 2px rgba(255, 68, 68, 0.2),
    0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-message {
  color: #ff4d4d;
  font-size: 0.9rem;
  margin-top: -1rem;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(255, 77, 77, 0.3);
  position: relative;
  z-index: 15;
  animation: shake 0.5s ease-in-out;
}

.error-message.hidden {
  display: none;
}

/* Button Styles */
#base-start-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(45deg, #7C4DFF, #6A00CC);
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out 0.4s both;
  box-shadow: 
    0 4px 12px rgba(124, 77, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  z-index: 15;
}

#base-start-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(124, 77, 255, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

#base-start-btn:active {
  transform: translateY(0);
  box-shadow: 
    0 4px 12px rgba(124, 77, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Status Card */
#base-status-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(124, 77, 255, 0.1);
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-out 0.6s both;
  position: relative;
  z-index: 15;
}

#base-status-card.expanded {
  transform: scale(1.02);
  box-shadow: 
    0 12px 48px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    0 0 30px rgba(124, 77, 255, 0.15);
}

#base-status-feed {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 1rem;
  padding-right: 0.5rem;
}

#base-status-feed::-webkit-scrollbar {
  width: 6px;
}

#base-status-feed::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

#base-status-feed::-webkit-scrollbar-thumb {
  background: rgba(124, 77, 255, 0.3);
  border-radius: 3px;
}

#base-status-feed::-webkit-scrollbar-thumb:hover {
  background: rgba(124, 77, 255, 0.5);
}

.status-row {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  animation: slideIn 0.3s ease-out;
}

.status-row:hover {
  background: rgba(255, 255, 255, 0.1);
}

.status-row.clickable {
  cursor: pointer;
}

.status-row.clickable:hover {
  background: rgba(138, 0, 238, 0.1);
}

.status-icon {
  width: 24px;
  height: 24px;
  margin-right: 1rem;
  color: #7C4DFF;
}

.status-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  margin-left: auto;
}

.link-part {
  color: #7C4DFF;
  text-decoration: none;
}

/* Animations */
@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    filter: blur(0px);
  }
  25% {
    transform: translate(100px, 50px) rotate(90deg) scale(1.2);
    filter: blur(2px);
  }
  50% {
    transform: translate(50px, 100px) rotate(180deg) scale(0.8);
    filter: blur(0px);
  }
  75% {
    transform: translate(-50px, 50px) rotate(270deg) scale(1.1);
    filter: blur(1px);
  }
  100% {
    transform: translate(0, 0) rotate(360deg) scale(1);
    filter: blur(0px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 
      0 0 20px rgba(138, 0, 238, 0.2),
      0 0 40px rgba(138, 0, 238, 0.1);
  }
  50% {
    box-shadow: 
      0 0 40px rgba(138, 0, 238, 0.3),
      0 0 80px rgba(138, 0, 238, 0.2);
  }
  100% {
    box-shadow: 
      0 0 20px rgba(138, 0, 238, 0.2),
      0 0 40px rgba(138, 0, 238, 0.1);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Monitoring Container */
.base-mode-monitoring-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(11, 10, 18, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  animation: fadeIn 0.3s ease-out;
}

.base-monitoring-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  animation: slideDown 0.5s ease-out;
}

.base-ghost-icon {
  width: 32px;
  height: 32px;
  margin-right: 1rem;
  animation: float 6s ease-in-out infinite;
}

.base-monitoring-text {
  color: #fff;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(138, 0, 238, 0.5);
}

.base-stop-button {
  background: linear-gradient(45deg, #ff4444, #cc0000);
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 2rem;
  animation: fadeIn 0.5s ease-out 0.2s both;
}

.base-stop-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 68, 68, 0.3);
}

.base-stop-button:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.5);
}

/* Form Elements */
.base-mode-form label {
  display: block;
  margin-bottom: 0.75rem;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
  position: relative;
  z-index: 15;
  text-shadow: 
    0 0 10px rgba(138, 0, 238, 0.5),
    0 0 20px rgba(138, 0, 238, 0.3);
  letter-spacing: 1px;
  text-transform: uppercase;
  background: linear-gradient(90deg, #fff, #7C4DFF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.base-mode-form input {
  width: 100%;
  padding: 1.2rem;
  margin-bottom: 2rem;
  background: rgba(11, 10, 18, 0.6);
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 15;
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.3),
    0 0 20px rgba(138, 0, 238, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  clip-path: polygon(
    0% 0%,
    100% 0%,
    100% calc(100% - 8px),
    calc(100% - 8px) 100%,
    0% 100%
  );
}

.base-mode-form input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  text-shadow: 0 0 8px rgba(138, 0, 238, 0.3);
  font-style: italic;
  letter-spacing: 0.5px;
}

.base-mode-form input:focus {
  outline: none;
  background: rgba(11, 10, 18, 0.8);
  box-shadow: 
    0 0 0 2px rgba(138, 0, 238, 0.5),
    0 0 30px rgba(138, 0, 238, 0.3);
  transform: translateY(-2px);
}

.base-mode-form input:hover {
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.4),
    0 0 25px rgba(138, 0, 238, 0.25);
  transform: translateY(-1px);
}

#base-input-card input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

#base-input-card input[type="number"]::-webkit-outer-spin-button,
#base-input-card input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

#base-input-card input:first-of-type:focus {
  outline: none;
  background: rgba(11, 10, 18, 0.5);
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.4),
    0 0 20px rgba(138, 0, 238, 0.2),
    2px 0 15px rgba(138, 0, 238, 0.3),
    0 2px 15px rgba(138, 0, 238, 0.3);
  transform: translateY(-1px) scale(1.01);
  z-index: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  animation: inputPulse 2s infinite;
  border-top: 1px solid rgba(138, 0, 238, 0.6);
  border-right: 1px solid rgba(138, 0, 238, 0.6);
  position: relative;
}

#base-input-card input:first-of-type:focus::before {
  content: '';
  position: absolute;
  top: -1px;
  right: -1px;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(138, 0, 238, 0.2),
    rgba(138, 0, 238, 0.1)
  );
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-top: 1px solid rgba(138, 0, 238, 0.4);
  border-right: 1px solid rgba(138, 0, 238, 0.4);
  pointer-events: none;
  z-index: -1;
}

#base-input-card input:last-of-type:focus {
  outline: none;
  background: rgba(11, 10, 18, 0.5);
  box-shadow: 
    0 0 0 1px rgba(138, 0, 238, 0.4),
    0 0 20px rgba(138, 0, 238, 0.2),
    -2px 0 15px rgba(138, 0, 238, 0.3),
    0 -2px 15px rgba(138, 0, 238, 0.3);
  transform: translateY(-1px) scale(1.01);
  z-index: 1;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  animation: inputPulse 2s infinite;
  border-bottom: 1px solid rgba(138, 0, 238, 0.6);
  border-left: 1px solid rgba(138, 0, 238, 0.6);
  position: relative;
}

#base-input-card input:last-of-type:focus::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: -1px;
  width: 100%;
  height: 100%;
  background: linear-gradient(315deg,
    rgba(138, 0, 238, 0.2),
    rgba(138, 0, 238, 0.1)
  );
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-bottom: 1px solid rgba(138, 0, 238, 0.4);
  border-left: 1px solid rgba(138, 0, 238, 0.4);
  pointer-events: none;
  z-index: -1;
}

#base-input-card button {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #7C4DFF, #6a00cc);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 0 10px rgba(138, 0, 238, 0.3),
    0 0 20px rgba(138, 0, 238, 0.2);
}

.traffic-lights {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  padding: 1rem 0;
  position: relative;
  z-index: 2;
}

.action-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.action-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.15);
}

.action-button:active {
  transform: translateY(1px) scale(0.95);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-button:hover::before {
  opacity: 1;
}

.action-button.start {
  background: linear-gradient(45deg, #7C4DFF, #6A00CC);
  color: #fff;
}

.action-button.start:hover {
  background: linear-gradient(45deg, #8B5EFF, #7A00E6);
}

.action-button.stop {
  background: linear-gradient(45deg, #FF4D4D, #CC0000);
  color: #fff;
}

.action-button.stop:hover {
  background: linear-gradient(45deg, #FF6666, #E60000);
}

.action-button.cancel {
  background: linear-gradient(45deg, #4D4D4D, #333333);
  color: #fff;
}

.action-button.cancel:hover {
  background: linear-gradient(45deg, #666666, #404040);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

#base-input-card .card-header {
  margin-bottom: 2.5rem;
  text-align: center;
}

#base-input-card .card-title {
  color: #fff;
  font-size: 2rem;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(124, 77, 255, 0.5);
}

#base-input-card .card-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px rgba(124, 77, 255, 0.3);
}