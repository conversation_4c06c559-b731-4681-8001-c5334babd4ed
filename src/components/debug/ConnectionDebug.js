import React, { useState, useEffect } from 'react';
import { useAppContext } from '../../context/AppContext';
import BackendService from '../../services/BackendService';
import './ConnectionDebug.css';

const ConnectionDebug = () => {
  const { connectionStatus, backendConnected, lastPingTime } = useAppContext();
  const [metadata, setMetadata] = useState({});
  const [logs, setLogs] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  // Update metadata regularly
  useEffect(() => {
    const updateMetadata = () => {
      const meta = BackendService.getConnectionMetadata();
      setMetadata(meta);
    };

    updateMetadata();
    const interval = setInterval(updateMetadata, 1000);
    return () => clearInterval(interval);
  }, []);

  // Log connection state changes
  useEffect(() => {
    const logEntry = {
      timestamp: new Date().toLocaleTimeString(),
      status: connectionStatus,
      metadata: { ...metadata }
    };
    
    setLogs(prev => [logEntry, ...prev.slice(0, 9)]); // Keep last 10 logs
  }, [connectionStatus, metadata.retryAttempt]);

  const handleManualReconnect = () => {
    BackendService.reconnect().catch(error => {
      console.error('Manual reconnect failed:', error);
    });
  };

  const handleDisconnect = () => {
    BackendService.disconnect();
  };

  const handleToggleAutoReconnect = () => {
    const newState = !metadata.shouldReconnect;
    BackendService.setAutoReconnect(newState);
  };

  const formatDuration = (ms) => {
    if (!ms) return 'N/A';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return '#22c55e';
      case 'connecting': return '#f59e0b';
      case 'reconnecting': return '#f97316';
      case 'retrying': return '#3b82f6';
      case 'error': return '#ef4444';
      default: return '#6b7280';
    }
  };

  if (!isVisible) {
    return (
      <button 
        className="debug-toggle"
        onClick={() => setIsVisible(true)}
        title="Show Connection Debug"
      >
        🔧
      </button>
    );
  }

  return (
    <div className="connection-debug">
      <div className="debug-header">
        <h3>Connection Debug</h3>
        <button 
          className="debug-close"
          onClick={() => setIsVisible(false)}
        >
          ✕
        </button>
      </div>

      <div className="debug-section">
        <h4>Current Status</h4>
        <div className="status-grid">
          <div className="status-item">
            <span className="label">State:</span>
            <span 
              className="value"
              style={{ color: getStatusColor(connectionStatus) }}
            >
              {connectionStatus}
            </span>
          </div>
          <div className="status-item">
            <span className="label">Connected:</span>
            <span className="value">{backendConnected ? 'Yes' : 'No'}</span>
          </div>
          <div className="status-item">
            <span className="label">Retry Attempt:</span>
            <span className="value">{metadata.retryAttempt || 0}</span>
          </div>
          <div className="status-item">
            <span className="label">Duration:</span>
            <span className="value">{formatDuration(metadata.connectionDuration)}</span>
          </div>
          <div className="status-item">
            <span className="label">Missed Heartbeats:</span>
            <span className="value">{metadata.missedHeartbeats || 0}</span>
          </div>
          <div className="status-item">
            <span className="label">Auto-Reconnect:</span>
            <span className="value">{metadata.shouldReconnect ? 'Enabled' : 'Disabled'}</span>
          </div>
        </div>
      </div>

      {metadata.lastError && (
        <div className="debug-section">
          <h4>Last Error</h4>
          <div className="error-message">{metadata.lastError}</div>
        </div>
      )}

      <div className="debug-section">
        <h4>Controls</h4>
        <div className="control-buttons">
          <button 
            onClick={handleManualReconnect}
            disabled={connectionStatus === 'connecting' || connectionStatus === 'retrying'}
          >
            🔄 Reconnect
          </button>
          <button 
            onClick={handleDisconnect}
            disabled={connectionStatus === 'disconnected'}
          >
            🔌 Disconnect
          </button>
          <button 
            onClick={handleToggleAutoReconnect}
            className={metadata.shouldReconnect ? 'active' : ''}
          >
            🔁 Auto-Reconnect: {metadata.shouldReconnect ? 'ON' : 'OFF'}
          </button>
        </div>
      </div>

      <div className="debug-section">
        <h4>Connection Log</h4>
        <div className="connection-log">
          {logs.map((log, index) => (
            <div key={index} className="log-entry">
              <span className="log-time">{log.timestamp}</span>
              <span 
                className="log-status"
                style={{ color: getStatusColor(log.status) }}
              >
                {log.status}
              </span>
              {log.metadata.retryAttempt > 0 && (
                <span className="log-detail">
                  (attempt {log.metadata.retryAttempt})
                </span>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="debug-section">
        <h4>Retry Configuration</h4>
        <div className="config-grid">
          <div className="config-item">
            <span className="label">Base Delay:</span>
            <span className="value">1s</span>
          </div>
          <div className="config-item">
            <span className="label">Max Delay:</span>
            <span className="value">30s</span>
          </div>
          <div className="config-item">
            <span className="label">Backoff Factor:</span>
            <span className="value">2x</span>
          </div>
          <div className="config-item">
            <span className="label">Max Attempts:</span>
            <span className="value">∞</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionDebug;
