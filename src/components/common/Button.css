.kasper-btn {
  border-radius: 8px;
  padding: 0 24px;
  height: 40px;
  font-size: 0.95rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.08em;
  transition: transform 120ms ease-out, box-shadow 120ms, background 120ms, color 120ms;
  outline: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  box-shadow: none;
  background: none;
  border: none;
}

.kasper-btn:active {
  transform: scale(0.97);
}

.kasper-btn:focus-visible {
  box-shadow: 0 0 0 2px #7C4DFF44;
}

.kasper-btn--primary {
  background: #7C4DFF;
  color: #EAEAEA;
  border: none;
}

.kasper-btn--primary:hover:not(:disabled) {
  box-shadow: 0 0 8px 0 #7C4DFF55;
}

.kasper-btn--secondary {
  background: transparent;
  color: #7C4DFF;
  border: 1px solid #7C4DFF;
}

.kasper-btn--secondary:hover:not(:disabled) {
  box-shadow: 0 0 8px 0 #7C4DFF55;
  background: rgba(124,77,255,0.08);
}

.kasper-btn--disabled,
.kasper-btn:disabled {
  background: #262626;
  color: #888;
  border: none;
  cursor: not-allowed;
  box-shadow: none;
} 