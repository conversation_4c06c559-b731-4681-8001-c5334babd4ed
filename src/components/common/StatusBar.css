/* Status Bar Styles */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-color);
  padding: 0 var(--spacing-md);
  font-size: 0.8rem;
  color: #7C4DFF;
  position: relative;
  z-index: 100;
}

.status-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--accent-color) 50%, transparent 100%);
  opacity: 0.2;
}

.status-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.status-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--text-primary);
}

.status-item i {
  font-size: 0.7rem;
}

/* Connection Status */
.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.connection-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--error-color);
  animation: pulse 2s infinite;
}

.connection-indicator.connected {
  background-color: var(--success-color);
  animation: none;
}

.connection-indicator.connecting {
  background-color: var(--warning-color);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* Mode Indicator */
.mode-indicator {
  background: linear-gradient(90deg, var(--accent-color), var(--info-color));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* System Stats */
.system-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-mono);
  font-size: 0.7rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.stat-value {
  color: var(--accent-color);
  font-weight: 500;
}

/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 120px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: var(--tertiary-bg);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--info-color));
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 0.7rem;
  color: var(--text-muted);
  min-width: 30px;
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-bar {
    padding: 0 var(--spacing-sm);
    font-size: 0.7rem;
  }
  
  .system-stats {
    display: none;
  }
  
  .status-item {
    padding: 1px 4px;
  }
  
  .progress-container {
    min-width: 80px;
  }
}

.kasper-statusbar {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.kasper-statusbar__connection {
  font-weight: 600;
  color: #7C4DFF;
  letter-spacing: 0.08em;
  text-transform: uppercase;
}
.kasper-statusbar__connection.offline {
  color: #888;
}

.kasper-statusbar__system {
  text-align: center;
  color: #EAEAEA;
  font-weight: 400;
  letter-spacing: 0.04em;
}

.kasper-statusbar__time {
  text-align: right;
  color: #EAEAEA;
  font-variant-numeric: tabular-nums;
  font-weight: 400;
}
