.documentation-container {
  padding: 24px;
  width: 100%;
  height: calc(100% - 48px); /* Subtract top bar height */
  margin-top: 48px; /* Add margin equal to top bar height */
  background-color: #0D0D0D;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(124, 77, 255, 0.3) transparent;
  position: relative;
}

/* Webkit scrollbar styles */
.documentation-container::-webkit-scrollbar {
  width: 8px;
}

.documentation-container::-webkit-scrollbar-track {
  background: transparent;
  margin: 4px;
}

.documentation-container::-webkit-scrollbar-thumb {
  background: rgba(124, 77, 255, 0.3);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.documentation-container::-webkit-scrollbar-thumb:hover {
  background: rgba(124, 77, 255, 0.4);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.documentation-section {
  margin-bottom: 32px;
}

.documentation-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(124, 77, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title .section-icon {
  font-size: 1.1rem;
  color: #7C4DFF;
  background: rgba(124, 77, 255, 0.1);
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 0 20px rgba(124, 77, 255, 0.1);
}

.section-title:hover .section-icon {
  background: rgba(124, 77, 255, 0.15);
  transform: scale(1.05);
}

.documentation-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.documentation-card {
  display: flex;
  flex-direction: column;
  background: rgba(124, 77, 255, 0.03);
  border: 1px solid rgba(124, 77, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 120px;
  position: relative;
  overflow: hidden;
}

.documentation-card::after {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(124, 77, 255, 0.08) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.documentation-card:hover {
  background: rgba(124, 77, 255, 0.05);
  border-color: rgba(124, 77, 255, 0.2);
  transform: translateY(-2px);
}

.documentation-card:hover::after {
  opacity: 1;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(124, 77, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 12px;
  color: #7C4DFF;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.documentation-card:hover .card-icon {
  background: rgba(124, 77, 255, 0.15);
  transform: scale(1.05);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-content h3 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 1.3;
}

.card-content p {
  margin: 0;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
}

.card-action {
  position: absolute;
  top: 16px;
  right: 16px;
  color: rgba(255, 255, 255, 0.4);
  font-size: 0.9rem;
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateX(-8px);
}

.documentation-card:hover .card-action {
  opacity: 1;
  transform: translateX(0);
}

/* Video card specific styles */
.documentation-card.video::after {
  content: '';
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.documentation-card.video::before {
  content: '';
  position: absolute;
  top: 24px;
  right: 24px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6px 0 6px 10px;
  border-color: transparent transparent transparent #fff;
  z-index: 2;
}

/* Update video card styles */
.documentation-card.video {
  cursor: pointer;
}

.documentation-card.video .card-action {
  color: #7C4DFF;
}

.documentation-card.video:hover .card-action {
  transform: scale(1.1);
}

/* Video Embed Styles */
.documentation-card.video-embed {
  height: auto;
  min-height: 200px;
  padding: 0;
  overflow: hidden;
  cursor: default;
  grid-column: span 1;
}

.documentation-card.video-embed:hover {
  transform: none;
}

.documentation-card.video-embed .video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  background: #000;
}

.documentation-card.video-embed .video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.documentation-card.video-embed .video-info {
  padding: 12px;
  background: rgba(11, 10, 18, 0.95);
  border-top: 1px solid rgba(124, 77, 255, 0.1);
}

.documentation-card.video-embed .video-info h3 {
  margin: 0 0 4px 0;
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
}

.documentation-card.video-embed .video-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .documentation-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .documentation-card.video-embed {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .documentation-container {
    padding: 16px;
    margin-top: 40px; /* Adjust for smaller top bar on mobile */
    height: calc(100% - 40px);
  }
  
  .documentation-section {
    margin-bottom: 24px;
  }
  
  .documentation-grid {
    grid-template-columns: 1fr;
  }
  
  .documentation-card {
    height: 100px;
  }
  
  .card-action {
    opacity: 1;
    transform: translateX(0);
  }
  
  .documentation-card.video-embed {
    min-height: 180px;
  }
  
  .documentation-card.video-embed .video-info {
    padding: 10px;
  }
  
  .documentation-card.video-embed .video-info h3 {
    font-size: 0.85rem;
  }
  
  .documentation-card.video-embed .video-info p {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .documentation-container {
    padding: 12px;
  }
  
  .documentation-grid {
    gap: 10px;
  }
}

/* Video Modal Styles */
.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.video-modal-content {
  background: rgba(11, 10, 18, 0.95);
  border: 1px solid rgba(124, 77, 255, 0.2);
  border-radius: 12px;
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.video-close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(124, 77, 255, 0.1);
  border: 1px solid rgba(124, 77, 255, 0.2);
  color: #fff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.2s ease;
}

.video-close-button:hover {
  background: rgba(124, 77, 255, 0.2);
  transform: scale(1.1);
}

.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
  background: #000;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-info {
  padding: 20px;
  border-top: 1px solid rgba(124, 77, 255, 0.1);
}

.video-info h3 {
  margin: 0 0 8px 0;
  color: #fff;
  font-size: 1.2rem;
  font-weight: 500;
}

.video-info p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
}

/* Responsive adjustments for video modal */
@media (max-width: 768px) {
  .video-modal {
    padding: 10px;
  }
  
  .video-modal-content {
    max-height: 80vh;
  }
  
  .video-info {
    padding: 15px;
  }
  
  .video-info h3 {
    font-size: 1rem;
  }
  
  .video-info p {
    font-size: 0.8rem;
  }
}

.mode-switcher {
  display: flex;
  gap: 8px;
  margin-bottom: 32px;
  padding: 4px;
  background: rgba(124, 77, 255, 0.03);
  border: 1px solid rgba(124, 77, 255, 0.1);
  border-radius: 12px;
  width: fit-content;
}

.mode-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  outline: none;
}

.mode-button::after {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(124, 77, 255, 0.08);
  border-radius: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.mode-button:hover {
  color: rgba(255, 255, 255, 0.9);
}

.mode-button:hover::after {
  opacity: 1;
}

.mode-button.active {
  color: #FFFFFF;
  background: rgba(124, 77, 255, 0.1);
  box-shadow: 0 0 20px rgba(124, 77, 255, 0.1);
}

.mode-button.active::after {
  opacity: 0;
}

.mode-button:focus {
  outline: none;
}

.mode-button:focus-visible {
  box-shadow: 0 0 0 2px rgba(124, 77, 255, 0.3);
}

.mode-button svg {
  font-size: 1rem;
  color: #7C4DFF;
  transition: transform 0.2s ease;
}

.mode-button:hover svg {
  transform: scale(1.1);
}

.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background: rgba(124, 77, 255, 0.95);
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 8px 12px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: 0 4px 20px rgba(124, 77, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(124, 77, 255, 0.2);
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: rgba(124, 77, 255, 0.95) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
} 