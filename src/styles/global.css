@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --color-bg: #0D0D0D;
  --color-accent: #7C4DFF;
  --color-text: #EAEAEA;
  --color-text-secondary: #888;
  --color-placeholder: #555;
  --color-border: #1A1A1A;
  --color-disabled-bg: #262626;
  --color-disabled-text: #888;
  --shadow: 0 4px 32px 0 rgba(138,0,238,0.10);
  --radius-card: 12px;
  --radius-btn: 8px;
  --font-main: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Menlo', monospace;
  --font-header-weight: 700;
  --font-body-weight: 400;
  --tracking-header: 0.08em;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 16px;
  --spacing-4: 24px;
  --spacing-5: 32px;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background: var(--color-bg);
  color: var(--color-text);
  font-family: var(--font-main);
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;

}

*, *::before, *::after {
  box-sizing: inherit;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-main);
  font-weight: var(--font-header-weight);
  text-transform: uppercase;
  letter-spacing: var(--tracking-header);
  margin: 0 0 var(--spacing-2) 0;
}

p {
  margin: 0 0 var(--spacing-2) 0;
}

button, input, select, textarea {
  font-family: inherit;
  font-size: 1em;
  color: inherit;
  background: none;
  border: none;
  outline: none;
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced pulse animation for background and elements */
@keyframes pulse {
  0% { 
    opacity: 0.1; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.2; 
    transform: scale(1.02); 
  }
  100% { 
    opacity: 0.1; 
    transform: scale(1); 
  }
}

/* Enhanced glow animation for logo */
@keyframes glow {
  0% { 
    opacity: 0.2; 
    transform: translate(-50%, -50%) scale(1); 
    filter: blur(25px) brightness(1);
  }
  50% { 
    opacity: 0.5; 
    transform: translate(-50%, -50%) scale(1.05); 
    filter: blur(25px) brightness(1.2);
  }
  100% { 
    opacity: 0.2; 
    transform: translate(-50%, -50%) scale(1); 
    filter: blur(25px) brightness(1);
  }
}

/* Enhanced dot pulse animation for progress indicators */
@keyframes dotPulse {
  0% { 
    opacity: 0.3; 
    transform: scale(1); 
    box-shadow: 0 0 8px var(--color-primary-lightest);
  }
  50% { 
    opacity: 1; 
    transform: scale(1.3); 
    box-shadow: 0 0 15px var(--color-primary-lightest), 0 0 25px var(--color-primary-lightest);
  }
  100% { 
    opacity: 0.3; 
    transform: scale(1); 
    box-shadow: 0 0 8px var(--color-primary-lightest);
  }
}

/* New floating animation for background elements */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  33% { 
    transform: translateY(-10px) rotate(1deg); 
  }
  66% { 
    transform: translateY(5px) rotate(-1deg); 
  }
}

/* New shimmer animation for logo text */
@keyframes shimmer {
  0% { 
    background-position: -200% center; 
  }
  100% { 
    background-position: 200% center; 
  }
}

/* New ripple animation for center dot */
@keyframes ripple {
  0% { 
    transform: scale(1);
    opacity: 1;
  }
  100% { 
    transform: scale(2);
    opacity: 0;
  }
}