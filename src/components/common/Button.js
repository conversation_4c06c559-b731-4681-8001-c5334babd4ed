import React from 'react';
import './Button.css';

const Button = ({ variant = 'primary', disabled, children, ...rest }) => {
  const className = [
    'kasper-btn',
    `kasper-btn--${variant}`,
    disabled ? 'kasper-btn--disabled' : '',
  ].join(' ');

  return (
    <button
      className={className}
      disabled={disabled}
      {...rest}
    >
      {children}
    </button>
  );
};

export default Button; 