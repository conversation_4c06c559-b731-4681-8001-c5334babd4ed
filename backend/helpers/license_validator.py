import json
import uuid
import platform
import requests
import hashlib
import logging
from typing import Dict, Any
import time

logger = logging.getLogger(__name__)

class LicenseValidator:
    def __init__(self):
        self.whop_api_key = "hursxVEJJioBlSmtcI_fyafR0smwHLBAs7vS4r2_Lgo"
        self.whop_api_url = "https://api.whop.com/api/v2/memberships/{}/validate_license"

    def get_hardware_id(self) -> str:
        """Generate a unique hardware ID based on system information."""
        try:
            # Get system information
            system_info = {
                'platform': platform.system(),
                'platform-release': platform.release(),
                'platform-version': platform.version(),
                'architecture': platform.machine(),
                'processor': platform.processor(),
                'node': platform.node()
            }
            
            # Create a unique string from system info
            system_str = json.dumps(system_info, sort_keys=True)
            # Generate a hash of the system information
            return hashlib.sha256(system_str.encode()).hexdigest()
        except Arithmetic<PERSON>rror as e:
            logger.error(f"Error generating hardware ID: {e}")
            # Fallback to a random UUID if system info collection fails
            return str(uuid.uuid4())

    def validate_license(self, license_key: str) -> Dict[str, Any]:
        """Validate the license key with Whop API in real-time."""
        try:
            # Get hardware ID
            hwid = self.get_hardware_id()
            logger.info(f"🔑 LICENSE: Starting real-time validation for key {license_key[:8]}... with HWID: {hwid[:16]}...")

            # Prepare the request
            url = self.whop_api_url.format(license_key)
            headers = {
                'Authorization': f'Bearer {self.whop_api_key}',
                'Content-Type': 'application/json'
            }
            data = {
                'metadata': {
                    'hwid': hwid
                }
            }

            logger.info(f"🌐 LICENSE: Making HTTP POST request to WHOP API: {url}")
            logger.info(f"🌐 LICENSE: Request headers: Authorization=Bearer {self.whop_api_key[:10]}..., Content-Type=application/json")
            logger.info(f"🌐 LICENSE: Request data: {data}")

            # Make the API request
            request_start = time.time()
            response = requests.post(
                "http://127.0.0.1:3023/api/details", 
                headers=headers, 
                json={
                    "license_key": license_key,
                    "hwid": hwid
                }, 
                timeout=10
            )
            request_duration = time.time() - request_start

            logger.info(f"🌐 LICENSE: HTTP response received in {request_duration:.2f}s - Status: {response.status_code}")

            # Parse and return the response
            result = response.json()
            logger.info(f"✅ LICENSE: WHOP API response: {result}")

            # Log key license information
            if 'error' not in result:
                logger.info(f"📊 LICENSE: Status={result.get('status')}, Valid={result.get('valid')}, "
                          f"Email={result.get('email')}, Expires={result.get('renewal_period_end')}")
            
            return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error validating license: {e}")
            if hasattr(e, 'response') and e.response and e.response.status_code == 404:
                return {'error': {'status': 404, 'message': 'Invalid license key'}}
            elif hasattr(e, 'response') and e.response and e.response.status_code == 401:
                return {'error': {'status': 401, 'message': 'License expired or unauthorized'}}
            return {'error': {'status': 500, 'message': f'Network error: {str(e)}'}}
        except ArithmeticError as e:
            logger.error(f"Unexpected error during license validation: {e}")
            return {'error': {'status': 500, 'message': str(e)}}

    def handle_ws_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle WebSocket messages for license validation."""
        try:
            if message.get('command') != 'validate_license':
                return {'error': {'status': 400, 'message': 'Invalid command'}}
                
            license_key = message.get('params', {}).get('license_key')
            if not license_key:
                return {'error': {'status': 400, 'message': 'Missing license key'}}
                
            # Validate the license
            result = self.validate_license(license_key)
            
            # Add command to response for frontend routing
            result['command'] = 'validate_license_response'
            return result
        except ArithmeticError as e:
            logger.error(f"Error handling WebSocket message: {e}")
            return {
                'command': 'validate_license_response',
                'error': {'status': 500, 'message': str(e)}
            } 

license_validator = LicenseValidator()

if __name__ == "__main__":
    from json import dumps
    license_validator = LicenseValidator()
    res = license_validator.validate_license("K-E7431F-5728F2FB-14D4C5W")
    print(dumps(res, indent=4))