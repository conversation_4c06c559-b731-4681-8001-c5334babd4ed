import React, { createContext, useContext, useState, useEffect } from 'react';
import BackendService from '../services/BackendService';

// Create context
const AppContext = createContext();

// Custom hook to use the app context
export const useAppContext = () => useContext(AppContext);

export const AppContextProvider = ({ children }) => {
  // Application state
  const [appMode, setAppMode] = useState('basic'); // 'basic', 'advanced', 'developer'
  const [currentPage, setCurrentPage] = useState('main'); // 'main', 'settings', 'about'
  const [backendConnected, setBackendConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected'); // 'disconnected', 'connecting', 'connected', 'error'
  const [lastPingTime, setLastPingTime] = useState(null);
  const [userCredits, setUserCredits] = useState(4.50);
  const [subscriptionDays, setSubscriptionDays] = useState(26);
  const [subscriptionTier, setSubscriptionTier] = useState('PRO'); // 'FREE', 'PRO', 'ENTERPRISE'

  // Queue tasks state
  const [queueTasks, setQueueTasks] = useState([]);

  // Add a new queue task
  const addQueueTask = (task) => {
    setQueueTasks(prev => [...prev, { ...task, id: Date.now(), status: 'pending' }]);
  };

  // Update a queue task
  const updateQueueTask = (id, updates) => {
    setQueueTasks(prev =>
      prev.map(task => task.id === id ? { ...task, ...updates } : task)
    );
  };

  // Remove a queue task
  const removeQueueTask = (id) => {
    setQueueTasks(prev => prev.filter(task => task.id !== id));
  };

  // Backend connection management
  const connectToBackend = async () => {
    // Safety check
    if (!BackendService || typeof BackendService.getConnectionState !== 'function') {
      console.error('BackendService is not available or missing getConnectionState method');
      setConnectionStatus('error');
      return;
    }

    // Check if already connected or connecting
    const currentState = BackendService.getConnectionState();
    if (currentState === 'connected' || currentState === 'connecting') {
      console.log('Backend connection already established or in progress');
      return;
    }

    setConnectionStatus('connecting');
    try {
      if (typeof BackendService.connectGracefully === 'function') {
        await BackendService.connectGracefully();
        // Connection state will be updated via callback
      } else if (typeof BackendService.connect === 'function') {
        await BackendService.connect();
        // Connection state will be updated via callback
      } else {
        throw new Error('BackendService.connect is not available');
      }
    } catch (error) {
      console.error('Failed to connect to backend:', error);

      // Check if we're in startup grace period - don't set error state if so
      const metadata = BackendService.getConnectionMetadata();
      if (!metadata.isInGracePeriod) {
        setConnectionStatus('error');
      } else {
        console.log('AppContext: Suppressing connection error during startup grace period');
        // Keep connecting state during grace period
        setConnectionStatus('connecting');
      }
    }
  };

  // Connection state change handler
  const handleConnectionStateChange = (newState, metadata) => {
    console.log(`AppContext: Connection state changed to ${newState}`, metadata);

    // Map BackendService states to AppContext states
    const stateMapping = {
      'disconnected': 'disconnected',
      'connecting': 'connecting',
      'connected': 'connected',
      'reconnecting': 'reconnecting',
      'retrying': 'retrying',
      'error': 'error'
    };

    const mappedState = stateMapping[newState] || 'error';
    setConnectionStatus(mappedState);
    setBackendConnected(newState === 'connected');

    // Handle specific state transitions
    if (newState === 'connected') {
      setLastPingTime(Date.now());
      console.log('Backend connected successfully');
    } else if (newState === 'error') {
      // During grace period, log but don't treat as critical error
      if (metadata.isInGracePeriod) {
        console.log('Backend connection error during grace period (expected):', metadata);
      } else {
        console.error('Backend connection error:', metadata);
      }
    } else if (newState === 'reconnecting') {
      console.log('Backend attempting to reconnect...');
    } else if (newState === 'retrying') {
      console.log(`Backend retrying connection (attempt ${metadata.retryAttempt})`);
    }
  };

  const disconnectFromBackend = () => {
    // Safe disconnect with method existence checks
    if (BackendService && typeof BackendService.stopHeartbeat === 'function') {
      BackendService.stopHeartbeat();
    }

    if (BackendService && typeof BackendService.disconnect === 'function') {
      BackendService.disconnect();
    }

    setBackendConnected(false);
    setConnectionStatus('disconnected');
    setLastPingTime(null);
  };

  // Auto-connect on mount
  useEffect(() => {
    let mounted = true;

    // Safety check: Ensure BackendService is properly loaded
    if (!BackendService || typeof BackendService.registerConnectionStateCallback !== 'function') {
      console.error('BackendService is not properly loaded or missing registerConnectionStateCallback method');
      console.log('BackendService type:', typeof BackendService);
      console.log('Available methods:', Object.getOwnPropertyNames(BackendService || {}));
      return;
    }

    // Register for connection state updates
    BackendService.registerConnectionStateCallback(handleConnectionStateChange);

    // Register message handlers
    if (typeof BackendService.registerHandler === 'function') {
      BackendService.registerHandler('connection_established', (message) => {
        console.log('Backend connection established:', message);
        if (mounted) {
          setLastPingTime(Date.now());
        }
      });

      BackendService.registerHandler('pong', (message) => {
        if (mounted) {
          setLastPingTime(Date.now());
          console.log('Ping/pong message received:', message);

          // Check for license status in pong response
          if (message.license_status) {
            console.log(`License status from pong: ${message.license_status}`);

            // If backend has valid license but frontend is not authenticated, trigger auto-login
            if (message.license_status === 'valid' && message.license_data) {
              console.log('🔐 AppContext: Backend has valid license, checking frontend auth state...');

              // Dispatch event to check if frontend needs auto-authentication
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('pongLicenseValid', {
                  detail: {
                    type: 'pong_license_valid',
                    data: message.license_data,
                    message: 'Backend validated license during ping/pong cycle',
                    timestamp: Date.now()
                  }
                }));
              }
            }

            // Handle license validation failures
            if (message.license_status === 'invalid' && message.license_error) {
              console.warn('License validation failed during ping/pong cycle:', message.license_error);

              // Dispatch authentication failure event to trigger logout
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('authenticationFailure', {
                  detail: {
                    type: 'authentication_failure',
                    reason: 'license_invalid',
                    message: message.license_error || 'License expired or invalid. Please log in again.',
                    timestamp: Date.now()
                  }
                }));
              }
            }
          }

          // Legacy support for authentication_status field
          if (message.authentication_status) {
            console.log(`Legacy authentication status: ${message.authentication_status}`);

            if (message.license_data) {
              console.log('Updated license data:', message.license_data);
            }

            // Handle authentication failures during ping/pong cycle
            if (message.authentication_status === 'failed' || message.authentication_status === 'status_changed') {
              console.warn('License validation failed during ping/pong cycle, triggering logout');

              // Dispatch authentication failure event to trigger logout
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('authenticationFailure', {
                  detail: {
                    type: 'authentication_failure',
                    reason: message.authentication_status === 'failed' ? 'license_invalid' : 'license_status_changed',
                    message: message.authentication_status === 'failed'
                      ? 'License expired or invalid. Please log in again.'
                      : `License status changed. Please log in again.`,
                    timestamp: Date.now()
                  }
                }));
              }
            }
          }
        }
      });

      // Handle direct authentication failure messages from backend
      BackendService.registerHandler('authentication_failure', (message) => {
        if (mounted) {
          console.warn('Direct authentication failure message received from backend:', message);

          // Dispatch authentication failure event to trigger logout
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('authenticationFailure', {
              detail: {
                type: 'authentication_failure',
                reason: message.reason || 'license_invalid',
                message: message.message || 'License expired or invalid. Please log in again.',
                timestamp: message.timestamp || Date.now()
              }
            }));
          }
        }
      });

      // Handle auto-login success messages from backend
      BackendService.registerHandler('auto_login_success', (message) => {
        if (mounted) {
          console.log('🔐 AppContext: Auto-login successful:', message);

          // Dispatch auto-login success event to trigger authentication
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('autoLoginSuccess', {
              detail: {
                type: 'auto_login_success',
                data: message.data,
                message: message.message || 'Automatically logged in with stored license',
                timestamp: Date.now()
              }
            }));
          }
        }
      });

      // Handle logout completion messages from backend
      BackendService.registerHandler('logout_completed', (message) => {
        if (mounted) {
          console.log('🔐 AppContext: Logout completed by backend:', message);

          // Dispatch logout completion event
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('logoutCompleted', {
              detail: {
                type: 'logout_completed',
                data: message.data,
                message: message.message || 'Logout completed successfully',
                timestamp: Date.now()
              }
            }));
          }
        }
      });

      // Handle settings updates from backend
      BackendService.registerHandler('settings_updated', (message) => {
        if (mounted) {
          console.log('🔧 AppContext: Settings updated by backend:', message);

          // Dispatch settings update event
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('settingsUpdated', {
              detail: {
                type: 'settings_updated',
                data: message.data,
                message: message.message || 'Settings updated successfully',
                timestamp: Date.now()
              }
            }));
          }
        }
      });

      // Handle queue progress updates from backend
      BackendService.registerHandler('queue_progress_update', (message) => {
        if (mounted) {
          console.log('🔄 AppContext: Queue progress update received:', message);

          // Update queue tasks state if we have task data
          if (message.task_id) {
            updateQueueTask(message.task_id, {
              status: message.overall_status || 'processing',
              progress: message.progress || 0,
              passLinks: message.pass_links || [],
              error: message.error_message || null,
              lastUpdate: Date.now()
            });
          }

          // Dispatch queue progress event for other components
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('queueProgressUpdate', {
              detail: {
                type: 'queue_progress_update',
                data: message,
                timestamp: Date.now()
              }
            }));
          }
        }
      });
    } else {
      console.error('BackendService.registerHandler is not available');
    }

    // Start connection only if not already connected
    const initializeConnection = async () => {
      if (mounted) {
        await connectToBackend();
      }
    };

    initializeConnection();

    // Cleanup on unmount
    return () => {
      mounted = false;

      // Safe cleanup with method existence checks
      if (BackendService && typeof BackendService.removeConnectionStateCallback === 'function') {
        BackendService.removeConnectionStateCallback(handleConnectionStateChange);
      }

      if (BackendService && typeof BackendService.unregisterHandler === 'function') {
        BackendService.unregisterHandler('connection_established');
        BackendService.unregisterHandler('pong');
        BackendService.unregisterHandler('authentication_failure');
        BackendService.unregisterHandler('auto_login_success');
        BackendService.unregisterHandler('logout_completed');
        BackendService.unregisterHandler('settings_updated');
        BackendService.unregisterHandler('queue_progress_update');
      }

      disconnectFromBackend();
    };
  }, []);

  // Context value
  const value = {
    appMode,
    setAppMode,
    currentPage,
    setCurrentPage,
    backendConnected,
    setBackendConnected,
    connectionStatus,
    setConnectionStatus,
    lastPingTime,
    setLastPingTime,
    userCredits,
    setUserCredits,
    subscriptionDays,
    subscriptionTier,
    queueTasks,
    setQueueTasks,
    addQueueTask,
    updateQueueTask,
    removeQueueTask,
    connectToBackend,
    disconnectFromBackend
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};