import React, { useRef, useState, useLayoutEffect } from 'react';
import './ModeSwitcher.css';

const MODES = [
  { key: 'base', label: 'Base' },
  { key: 'dev', label: 'Dev' }
];

const ModeSwitcher = ({ mode, onChange }) => {
  const containerRef = useRef(null);
  const [highlight, setHighlight] = useState({ left: 0, width: 0 });
  const [isFocused, setIsFocused] = useState(false);

  // Update highlight position when mode changes
  useLayoutEffect(() => {
    const activeButton = containerRef.current?.querySelector('.mode-switcher__option.active');
    if (activeButton && containerRef.current) {
      const buttonRect = activeButton.getBoundingClientRect();
      const containerRect = containerRef.current.getBoundingClientRect();
      setHighlight({
        left: buttonRect.left - containerRect.left,
        width: buttonRect.width
      });
    }
  }, [mode]);

  return (
    <div
      ref={containerRef}
      className="kasper-mode-switcher"
      role="tablist"
      aria-label="Mode Switcher"
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      style={isFocused ? { outline: '1px dotted rgba(255,255,255,0.5)', outlineOffset: 2 } : {}}
    >
      {/* Highlight bar */}
      <div
        className="mode-switcher__highlight"
        style={{
          width: highlight.width,
          transform: `translateX(${highlight.left}px)`
        }}
        aria-hidden="true"
      />

      {/* Mode buttons */}
      {MODES.map(({ key, label }) => (
        <button
          key={key}
          className={`mode-switcher__option${mode === key ? ' active' : ''}`}
          onClick={() => onChange(key)}
          role="tab"
          aria-selected={mode === key}
          tabIndex={mode === key ? 0 : -1}
          type="button"
        >
          {label.toUpperCase()}
        </button>
      ))}
    </div>
  );
};

export default ModeSwitcher; 