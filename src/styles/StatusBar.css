.status-bar {
  height: 28px;
  background: var(--color-surface);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
}

.status-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.status-indicator,
.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-indicator svg,
.status-item svg {
  width: 14px;
  height: 14px;
  opacity: 0.7;
}

.status-indicator.online {
  color: var(--color-primary-light);
}

.status-indicator.online svg {
  opacity: 1;
  filter: drop-shadow(0 0 4px var(--color-primary-light));
}

.status-indicator.offline {
  color: #e81123;
}

.status-indicator.offline svg {
  opacity: 1;
}

/* Hover effects */
.status-item:hover {
  color: var(--color-text-primary);
}

.status-item:hover svg {
  opacity: 1;
  transform: scale(1.1);
  transition: all var(--transition-fast);
}

/* Responsive design */
@media (max-width: 768px) {
  .status-bar {
    height: 32px;
    padding: 0 var(--spacing-sm);
  }

  .status-section {
    gap: var(--spacing-md);
  }

  .status-item span {
    display: none;
  }

  .status-indicator span {
    display: none;
  }
} 