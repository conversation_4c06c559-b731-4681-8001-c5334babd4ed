import React, { useState, useEffect } from 'react';
import './styles/global.css';
import { TopBar } from './components/common';
import BaseMode from './components/modes/BaseMode';
import Documentation from './components/pages/Documentation';
import Settings from './components/pages/Settings';
import MonitoringView from './components/monitoring/MonitoringView';
import LoginPage from './components/auth/LoginPage';
import ConnectionRejectedModal from './components/common/ConnectionRejectedModal';
import LoadingScreen from './components/common/LoadingScreen';
import BackendService from './services/BackendService';
import LicenseStorage from './services/LicenseStorage';
// import GlassBackground from './components/common/GlassBackground';

function App() {
  // Core state
  const [mode, setMode] = useState('base');
  const [currentPage, setCurrentPage] = useState('main');
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState('');
  const [connectionRejected, setConnectionRejected] = useState(false);
  const [rejectionDetails, setRejectionDetails] = useState(null);

  // License validation state
  const [isValidatingStoredLicense, setIsValidatingStoredLicense] = useState(true);
  const [storedLicenseInfo, setStoredLicenseInfo] = useState(null);
  const [isAutoLoginInProgress, setIsAutoLoginInProgress] = useState(false);

  // Debug state changes
  useEffect(() => {
    console.log('🔍 App State Change - isValidatingStoredLicense:', isValidatingStoredLicense);
  }, [isValidatingStoredLicense]);

  useEffect(() => {
    console.log('🔍 App State Change - isAutoLoginInProgress:', isAutoLoginInProgress);
  }, [isAutoLoginInProgress]);

  useEffect(() => {
    console.log('🔍 App State Change - isAuthenticated:', isAuthenticated);
  }, [isAuthenticated]);

  // Queue entry parameters
  const [queueParams, setQueueParams] = useState({
    eventUrl: '',
    ticketQuantity: 1
  });

  // Mode switching handler
  const handleModeChange = (newMode) => {
    if (newMode === mode) return; // Prevent unnecessary re-renders
    setMode(newMode);
  };

  // Navigation handler
  const handleNavigate = (page) => {
    if (page === currentPage) return; // Prevent unnecessary re-renders
    setCurrentPage(page);
  };

  // Monitoring handlers
  const handleEnterQueue = (params) => {
    // Store the queue entry parameters
    if (params) {
      setQueueParams({
        eventUrl: params.eventUrl || '',
        ticketQuantity: params.ticketQuantity || 1
      });
    }
    setIsMonitoring(true);
    setCurrentPage('monitoring');
  };

  const handleExitQueue = () => {
    setIsMonitoring(false);
    setCurrentPage('main');
  };

  // Authentication handler
  const handleAuthentication = async (success, licenseInfo) => {
    console.log('🔐 Authentication handler called:', { success, licenseInfo });
    setIsAuthenticated(success);
    setAuthError('');

    if (success && licenseInfo) {
      setStoredLicenseInfo(licenseInfo);

    }
  };

  // Validate stored license key
  const validateStoredLicense = async () => {
    console.log('🔐 App: Checking for stored license key...');

    // Check if we have a stored license
    if (!LicenseStorage.hasStoredLicense()) {
      console.log('🔐 App: No stored license found');
      setIsValidatingStoredLicense(false);
      return;
    }

    const storedKey = LicenseStorage.getLicenseKey();
    const storedInfo = LicenseStorage.getLicenseInfo();

    console.log('🔐 App: Found stored license key:', storedKey ? `${storedKey.substring(0, 8)}...` : 'invalid');

    // Basic format validation
    if (!LicenseStorage.isValidLicenseFormat(storedKey)) {
      console.warn('🔐 App: Stored license key has invalid format, clearing storage');
      LicenseStorage.clearLicense();
      setAuthError('Stored license key is invalid. Please log in again.');
      setIsValidatingStoredLicense(false);
      return;
    }

    // Check if license is expired based on stored info
    if (LicenseStorage.isStoredLicenseExpired()) {
      console.warn('🔐 App: Stored license is expired, clearing storage');
      LicenseStorage.clearLicense();
      setAuthError('Your license has expired. Please log in again.');
      setIsValidatingStoredLicense(false);
      return;
    }

    try {
      // Wait for backend connection before validating
      console.log('🔐 App: Ensuring backend connection for license validation...');

      // Use graceful connection to avoid startup errors
      if (typeof BackendService.connectGracefully === 'function') {
        await BackendService.connectGracefully();
      } else {
        await BackendService.connect();
      }

      console.log('🔐 App: Validating stored license with backend...');
      const response = await BackendService.validateLicense(storedKey);

      // Check for validation errors
      if (response.data && response.data.error) {
        console.warn('🔐 App: Stored license validation failed:', response.data.error.message);
        LicenseStorage.clearLicense();
        setAuthError('License validation failed. Please log in again.');
        setIsValidatingStoredLicense(false);
        return;
      }

      // License is valid - update stored info and authenticate
      const updatedLicenseInfo = {
        licenseKey: storedKey,
        manageUrl: response.data.manage_url,
        email: response.data.email,
        status: response.data.status,
        renewalPeriodEnd: response.data.renewal_period_end
      };

      // Update stored license info with fresh data
      LicenseStorage.updateLicenseInfo(updatedLicenseInfo);

      console.log('🔐 App: Stored license validated successfully, auto-authenticating user');
      setStoredLicenseInfo(updatedLicenseInfo);
      setIsAuthenticated(true);
      setAuthError('');


    } catch (error) {
      console.error('🔐 App: Error validating stored license:', error);

      // Check if this is a network error during startup grace period
      const metadata = BackendService.getConnectionMetadata();
      if (metadata.isInGracePeriod) {
        console.log('🔐 App: Network error during grace period, will retry validation');
        // Don't clear license during grace period - retry later
        setIsValidatingStoredLicense(false); // Clear loading state temporarily
        setTimeout(() => {
          if (LicenseStorage.hasStoredLicense()) {
            setIsValidatingStoredLicense(true); // Set loading state for retry
            validateStoredLicense();
          }
        }, 3000); // Retry in 3 seconds
        return;
      }

      // Network error outside grace period or other error - clear license
      console.warn('🔐 App: Clearing stored license due to validation error');
      LicenseStorage.clearLicense();
      setAuthError('Connection error during license validation. Please log in again.');
    } finally {
      setIsValidatingStoredLicense(false);
    }
  };

  // Logout function that clears stored license
  const handleLogout = async () => {
    console.log('🔐 App: Logging out user and clearing stored license');

    try {
      // Clear license key from backend
      console.log('🔐 App: Clearing license key from backend...');
      await BackendService.logOut();
      console.log('🔐 App: Backend license key cleared successfully');
    } catch (error) {
      console.warn('🔐 App: Failed to clear backend license key:', error);
      // Continue with logout even if backend clear fails
    }

    // Clear stored license from localStorage
    LicenseStorage.clearLicense();

    // Reset authentication state
    setIsAuthenticated(false);
    setStoredLicenseInfo(null);
    setAuthError('');
    setCurrentPage('main');
    setIsMonitoring(false);

    console.log('🔐 App: Logout completed successfully');
  };

  // Authentication failure handler
  const handleAuthenticationFailure = (message) => {
    console.log('Authentication failure detected:', message);

    // Clear stored license on authentication failure
    LicenseStorage.clearLicense();

    setIsAuthenticated(false);
    setStoredLicenseInfo(null);
    setAuthError(message.message || 'License expired or invalid. Please log in again.');
    setCurrentPage('main');
    setIsMonitoring(false);
  };

  // Connection rejection handler
  const handleConnectionRejection = (message) => {
    console.error('Connection rejected:', message);

    // Clear stored license on connection rejection
    LicenseStorage.clearLicense();

    setConnectionRejected(true);
    setRejectionDetails(message);
    setIsAuthenticated(false);
    setStoredLicenseInfo(null);
    setCurrentPage('main');
    setIsMonitoring(false);
  };

  // Check for stored license on app startup
  useEffect(() => {
    validateStoredLicense();

    // Safety timeout to prevent infinite loading state
    const loadingTimeout = setTimeout(() => {
      if (isValidatingStoredLicense) {
        console.warn('🔐 App: License validation timeout - clearing loading state');
        setIsValidatingStoredLicense(false);
        setAuthError('License validation timed out. Please try logging in manually.');
      }
    }, 15000); // 15 second timeout

    return () => clearTimeout(loadingTimeout);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  // Handle auto-login success from backend
  useEffect(() => {
    const handleAutoLoginSuccess = (event) => {
      console.log('🔐 App: Auto-login success event received:', event.detail);

      const { data } = event.detail;
      if (data) {
        // Update license info and authenticate
        const updatedLicenseInfo = {
          licenseKey: data.license_key,
          manageUrl: data.manage_url,
          email: data.email,
          status: data.status,
          renewalPeriodEnd: data.renewal_period_end
        };

        // Update localStorage with fresh license data
        LicenseStorage.storeLicense(data.license_key, updatedLicenseInfo);

        // Set authentication state
        setIsAuthenticated(true);
        setStoredLicenseInfo(updatedLicenseInfo);
        setAuthError('');
        setIsValidatingStoredLicense(false); // Stop validation loading state
        setIsAutoLoginInProgress(false); // Auto-login completed

        // Navigate to main page if currently on login
        if (!isAuthenticated) {
          setCurrentPage('main');
        }

        console.log('🔐 App: Auto-login completed successfully - user authenticated and navigated to main page');
      }
    };

    const handleLogoutCompleted = (event) => {
      console.log('🔐 App: Logout completed event received:', event.detail);

      // Clear frontend state
      setIsAuthenticated(false);
      setStoredLicenseInfo(null);
      setAuthError('');
      setCurrentPage('main');
      setIsMonitoring(false);

      console.log('🔐 App: Frontend logout state cleared');
    };

    const handlePongLicenseValid = (event) => {
      console.log('🔐 App: Pong license valid event received:', event.detail);

      // Only trigger auto-authentication if frontend is not currently authenticated
      if (!isAuthenticated && !isValidatingStoredLicense && !isAutoLoginInProgress) {
        console.log('🔐 App: Frontend not authenticated but backend has valid license - triggering auto-authentication');

        setIsAutoLoginInProgress(true); // Mark auto-login as in progress

        // Safety timeout for auto-login
        setTimeout(() => {
          if (isAutoLoginInProgress) {
            console.warn('🔐 App: Auto-login timeout - clearing progress state');
            setIsAutoLoginInProgress(false);
          }
        }, 10000); // 10 second timeout for auto-login

        const { data } = event.detail;
        if (data) {
          // Create license info from backend data
          const licenseInfo = {
            licenseKey: data.license_key,
            manageUrl: data.manage_url,
            email: data.email,
            status: data.status,
            renewalPeriodEnd: data.renewal_period_end
          };

          // Update localStorage with license data
          LicenseStorage.storeLicense(data.license_key, licenseInfo);

          // Set authentication state
          setIsAuthenticated(true);
          setStoredLicenseInfo(licenseInfo);
          setAuthError('');
          setCurrentPage('main');
          setIsAutoLoginInProgress(false); // Auto-login completed

          console.log('🔐 App: Auto-authentication from pong completed - user logged in');
        } else {
          setIsAutoLoginInProgress(false); // Reset if no data
        }
      } else if (isAuthenticated) {
        console.log('🔐 App: Frontend already authenticated, ignoring pong license valid event');
      } else if (isValidatingStoredLicense) {
        console.log('🔐 App: Currently validating stored license, ignoring pong license valid event');
      } else if (isAutoLoginInProgress) {
        console.log('🔐 App: Auto-login already in progress, ignoring pong license valid event');
      }
    };

    const handleLicenseUpdated = (event) => {
      console.log('🔐 App: License updated event received:', event.detail);

      // Sync frontend state with backend license data
      const { data } = event.detail;
      if (data && data.license && data.license.license_key) {
        const licenseData = data.license;
        const updatedLicenseInfo = {
          licenseKey: licenseData.license_key,
          manageUrl: licenseData.manage_url,
          email: licenseData.email,
          status: licenseData.status,
          renewalPeriodEnd: licenseData.renewal_period_end
        };

        // Update localStorage with fresh license data
        LicenseStorage.storeLicense(licenseData.license_key, updatedLicenseInfo);

        // Update frontend state if authenticated
        if (isAuthenticated) {
          setStoredLicenseInfo(updatedLicenseInfo);
          console.log('🔐 App: License info synchronized with backend');
        }
      }
    };

    // Add event listeners
    window.addEventListener('autoLoginSuccess', handleAutoLoginSuccess);
    window.addEventListener('logoutCompleted', handleLogoutCompleted);
    window.addEventListener('pongLicenseValid', handlePongLicenseValid);
    window.addEventListener('settingsUpdated', handleLicenseUpdated);

    // Cleanup
    return () => {
      window.removeEventListener('autoLoginSuccess', handleAutoLoginSuccess);
      window.removeEventListener('logoutCompleted', handleLogoutCompleted);
      window.removeEventListener('pongLicenseValid', handlePongLicenseValid);
      window.removeEventListener('settingsUpdated', handleLicenseUpdated);
    };
  }, []);

  // Register authentication failure and connection rejection handlers
  useEffect(() => {
    // Register handler for authentication failures from backend
    BackendService.registerHandler('authentication_failure', handleAuthenticationFailure);

    // Register handler for connection rejections from backend
    BackendService.registerHandler('connection_rejected', handleConnectionRejection);

    // Also listen for global events
    const handleGlobalAuthFailure = (event) => {
      handleAuthenticationFailure(event.detail);
    };

    const handleGlobalConnectionRejection = (event) => {
      handleConnectionRejection(event.detail);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('authenticationFailure', handleGlobalAuthFailure);
      window.addEventListener('connectionRejected', handleGlobalConnectionRejection);
    }

    // Cleanup
    return () => {
      BackendService.unregisterHandler('authentication_failure');
      BackendService.unregisterHandler('connection_rejected');
      if (typeof window !== 'undefined') {
        window.removeEventListener('authenticationFailure', handleGlobalAuthFailure);
        window.removeEventListener('connectionRejected', handleGlobalConnectionRejection);
      }
    };
  }, []);

  // Content rendering
  const renderContent = () => {
    // Show loading state while validating stored license or auto-login in progress
    if (isValidatingStoredLicense || isAutoLoginInProgress) {
      const loadingMessage = isAutoLoginInProgress ? 'Logging in...' : 'Validating license...';

      // Prepare debug data for development
      const debugData = process.env.NODE_ENV === 'development' ? {
        'isValidatingStoredLicense': isValidatingStoredLicense.toString(),
        'isAutoLoginInProgress': isAutoLoginInProgress.toString(),
        'isAuthenticated': isAuthenticated.toString(),
        'Backend': BackendService?.getConnectionState() || 'unknown'
      } : {};

      // Debug override handler
      const handleDebugOverride = () => {
        console.log('🔧 Manual override: Clearing loading states');
        setIsValidatingStoredLicense(false);
        setIsAutoLoginInProgress(false);
      };

      return (
        <LoadingScreen
          message={loadingMessage}
          showDebugInfo={process.env.NODE_ENV === 'development'}
          debugData={debugData}
          onDebugOverride={process.env.NODE_ENV === 'development' ? handleDebugOverride : null}
        />
      );
    }

    // Show login page if not authenticated
    if (!isAuthenticated) {
      return <LoginPage onAuthenticate={handleAuthentication} authError={authError} />;
    }

    // Monitoring view takes precedence
    if (isMonitoring) {
      return (
        <MonitoringView
          onExit={handleExitQueue}
          eventUrl={queueParams.eventUrl}
          ticketsRequested={queueParams.ticketQuantity}
        />
      );
    }

    // Page-based content
    switch (currentPage) {
      case 'documentation':
        return <Documentation />;
      case 'settings':
        return <Settings onNavigate={handleNavigate} onLogout={handleLogout} />;
      case 'main':
      default:
        // Mode-based content
        return mode === 'dev' ? (
          <div style={{ padding: 48 }}>
            <h1>Dev Mode</h1>
            <p>Coming soon...</p>
          </div>
        ) : (
          <BaseMode onEnterQueue={handleEnterQueue} />
        );
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      height: '100vh', 
      background: 'var(--color-bg)', 
      position: 'relative', 
      zIndex: 1,
      overflow: 'hidden',
      margin: 0,
      padding: 0
    }}>
      {/* <GlassBackground /> */}
      {isAuthenticated && (
        <TopBar 
          mode={mode}
          onChangeMode={handleModeChange}
          onNavigate={handleNavigate}
          currentPage={currentPage}
          isMonitoring={isMonitoring}
        />
      )}
      <main style={{ 
        flex: 1,
        display: 'flex', 
        flexDirection: 'column', 
        position: 'relative', 
        zIndex: 2,
        margin: 0,
        padding: 0,
        overflow: 'hidden',
        height: isAuthenticated ? 'calc(100vh - 72px)' : '100vh' // Adjust height based on authentication
      }}>
        <div style={{ 
          width: '100%', 
          height: '100%',
          margin: 0,
          padding: 0,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {renderContent()}
        </div>
      </main>

      {/* Connection Rejected Modal */}
      <ConnectionRejectedModal
        isOpen={connectionRejected}
        onClose={() => setConnectionRejected(false)}
        rejectionDetails={rejectionDetails}
      />
    </div>
  );
}

export default App;