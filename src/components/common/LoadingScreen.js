import React from 'react';
import './LoadingScreen.css';
import ghostLogo from '../../assets/ghost.svg';

const LoadingScreen = ({ message = "Validating key", debugData = {}, onDebugOverride }) => {
  return (
    <div className="loading-screen">
      {/* Smooth background gradient */}
      <div className="loading-background"></div>
      
      {/* Ghost logo with smooth lighting */}
      <div className="loader-logo">
        <img src={ghostLogo} alt="Kasper-Q Logo" />
      </div>
      
      {/* Loading message */}
      <div className="loading-message">
        {message}
      </div>
      
      {/* Debug override button for development */}
      {process.env.NODE_ENV === 'development' && onDebugOverride && (
        <button 
          className="debug-override-btn" 
          onClick={onDebugOverride}
          title="Manual override for development"
        >
          Clear Loading State
        </button>
      )}
    </div>
  );
};

export default LoadingScreen; 