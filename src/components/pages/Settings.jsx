import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>a<PERSON>ey, FaCoins, FaDiscord, FaSignOutAlt, FaCopy, FaCheck, FaCreditCard, FaBitcoin, FaSpinner, FaCheckCircle, FaExclamationTriangle, FaTimes } from 'react-icons/fa';
import BackendService from '../../services/BackendService';
import './Settings.css';
import { toast, Toaster } from 'react-hot-toast';

const Settings = ({ onNavigate, onLogout }) => {
  const [backendSettings, setBackendSettings] = useState(null);
  const [backendConnected, setBackendConnected] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // Webhook synchronization state
  const [webhookSaveState, setWebhookSaveState] = useState('idle'); // 'idle', 'saving', 'saved', 'error'
  const [webhookError, setWebhookError] = useState(null);
  const debounceTimeoutRef = useRef(null);
  const lastSavedWebhookRef = useRef('');

  // Fallback data - used when backend is unavailable
  const fallbackData = {
    licenseDetails: {
      status: 'active',
      type: 'Premium',
      key: 'XXXX-XXXX-XXXX-XXXX',
      renewalDate: '2024-12-31'
    },
    balance: {
      current: 1000,
      pending: 50,
      history: [
        { date: '2024-03-15', amount: 100, type: 'purchase' },
        { date: '2024-03-14', amount: -50, type: 'usage' },
        { date: '2024-03-13', amount: 200, type: 'purchase' }
      ]
    },
    webhookUrl: 'https://discord.com/api/webhooks/your-webhook-url'
  };

  // Fetch settings from backend
  useEffect(() => {
    const fetchBackendSettings = async () => {
      try {
        const response = await BackendService.getSettings();

        if (response.type === 'settings_data') {
          setBackendSettings(response.data);
          setBackendConnected(true);
          console.log('⚙️ Settings loaded from backend:', Object.keys(response.data));
        }
      } catch (err) {
        console.error('Failed to fetch settings from backend:', err);
        setBackendConnected(false);
      }
    };

    fetchBackendSettings();
  }, []);

  // Listen for settings refresh events from TopBar
  useEffect(() => {
    const handleSettingsRefresh = (event) => {
      console.log('⚙️ Settings: Received refresh event with data:', Object.keys(event.detail || {}));
      setBackendSettings(event.detail);
      setBackendConnected(true);

      // Update webhook URL if it changed
      if (event.detail?.webhook?.url) {
        setWebhookUrl(event.detail.webhook.url);
        lastSavedWebhookRef.current = event.detail.webhook.url;
      }
    };

    // Add event listener for settings refresh
    window.addEventListener('settingsRefreshed', handleSettingsRefresh);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('settingsRefreshed', handleSettingsRefresh);
    };
  }, []);

  // Dynamic data - uses backend data if available, otherwise fallback
  const licenseDetails = backendSettings?.license || fallbackData.licenseDetails;
  const balance = backendSettings?.balance || fallbackData.balance;
  const [webhookUrl, setWebhookUrl] = useState(fallbackData.webhookUrl);

  // Update webhook URL when backend data is loaded
  useEffect(() => {
    if (backendSettings?.webhook?.url) {
      setWebhookUrl(backendSettings.webhook.url);
      lastSavedWebhookRef.current = backendSettings.webhook.url;
    }
  }, [backendSettings]);

  // Validate Discord webhook URL
  const isValidDiscordWebhook = useCallback((url) => {
    if (!url || url.trim() === '') return true; // Empty is valid (disabled webhook)
    const discordWebhookRegex = /^https:\/\/discord(?:app)?\.com\/api\/webhooks\/\d+\/[\w-]+$/;
    return discordWebhookRegex.test(url.trim());
  }, []);

  // Debounced save function for webhook URL
  const debouncedSaveWebhook = useCallback(async (url) => {
    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Skip if URL hasn't changed from last saved value
    if (url === lastSavedWebhookRef.current) {
      return;
    }

    // Validate URL format
    if (url && !isValidDiscordWebhook(url)) {
      setWebhookSaveState('error');
      setWebhookError('Invalid Discord webhook URL format');
      return;
    }

    // Set up debounced save
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        setWebhookSaveState('saving');
        setWebhookError(null);

        if (backendConnected) {
          const response = await BackendService.updateSettings({
            webhook: {
              url: url.trim(),
              enabled: url.trim() !== ''
            }
          });

          if (response.type === 'settings_updated') {
            setWebhookSaveState('saved');
            lastSavedWebhookRef.current = url.trim();

            // Reset to idle after showing success
            setTimeout(() => {
              setWebhookSaveState('idle');
            }, 2000);
          } else {
            throw new Error('Unexpected response from backend');
          }
        } else {
          // Backend offline - save locally
          setWebhookSaveState('saved');
          lastSavedWebhookRef.current = url.trim();

          setTimeout(() => {
            setWebhookSaveState('idle');
          }, 2000);
        }
      } catch (error) {
        console.error('Failed to save webhook:', error);
        setWebhookSaveState('error');
        setWebhookError(error.message || 'Failed to save webhook URL');

        // Reset error state after 5 seconds
        setTimeout(() => {
          setWebhookSaveState('idle');
          setWebhookError(null);
        }, 5000);
      }
    }, 750); // 750ms debounce delay
  }, [backendConnected, isValidDiscordWebhook]);

  // Handle webhook URL changes with real-time sync
  const handleWebhookChange = useCallback((e) => {
    const newUrl = e.target.value;
    setWebhookUrl(newUrl);

    // Reset error state when user starts typing
    if (webhookSaveState === 'error') {
      setWebhookSaveState('idle');
      setWebhookError(null);
    }

    // Trigger debounced save
    debouncedSaveWebhook(newUrl);
  }, [debouncedSaveWebhook, webhookSaveState]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Fallback transactions data - used when backend is unavailable
  const fallbackTransactions = [];

  // Dynamic transactions data - uses backend data if available, otherwise fallback
  const backendTransactions = backendSettings?.transactions || backendSettings?.balance?.history;

  // Convert backend timestamps to Date objects if needed
  const recentTransactions = backendTransactions
    ? backendTransactions.map(transaction => ({
        ...transaction,
        timestamp: typeof transaction.timestamp === 'number'
          ? new Date(transaction.timestamp * 1000) // Convert Unix timestamp to Date
          : new Date(transaction.timestamp) // Handle string dates
      }))
    : fallbackTransactions;

  // Mouse tracking effect for card hover
  useEffect(() => {
    const handleMouseMove = (e) => {
      const cards = document.getElementsByClassName('settings-card');
      for (const card of cards) {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        card.style.setProperty('--mouse-x', `${x}px`);
        card.style.setProperty('--mouse-y', `${y}px`);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleCopyWebhook = () => {
    navigator.clipboard.writeText(webhookUrl);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleLogout = async () => {
    if (!onLogout) {
      console.error('Settings: onLogout prop not provided');
      toast.error('Logout functionality not available');
      return;
    }

    if (isLoggingOut) {
      return; // Prevent multiple logout attempts
    }

    try {
      setIsLoggingOut(true);

      // Show loading toast
      const loadingToast = toast.loading('Logging out...');

      console.log('🔐 Settings: Initiating logout process');

      // Call the logout handler from App.js
      await onLogout();

      // Dismiss loading toast and show success
      toast.dismiss(loadingToast);
      toast.success('Logged out successfully');

      console.log('🔐 Settings: Logout completed successfully');

    } catch (error) {
      console.error('🔐 Settings: Logout failed:', error);
      toast.error('Logout failed. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleManageSubscription = async () => {
    const url = backendSettings?.license?.manage_url;
    console.log("Opening url", url)
    if (!url) {
      toast.error('Subscription management URL not available');
      return;
    }

    try {
      // This returns a Promise; it will reject if something goes wrong
      await window.electron.openExternal(url);
      console.log("Opened")
    } catch (err) {
      console.error('Failed to open subscription URL:', err);
      toast.error('Couldn\'t open in default browser — please try again.');
    }
  };

  const handleTestWebhook = async () => {
    if (!webhookUrl) {
      toast.error('Please enter a webhook URL first');
      return;
    }

    try {
      // Ensure BackendService is connected
      if (!BackendService.isConnected) {
        await BackendService.connect();
      }

      // Use the backend's test webhook functionality
      const response = await BackendService.testWebhook();

      if (response?.type === 'test_webhook_response') {
        if (response.success) {
          toast.success(response.message || 'Webhook test successful!');
        } else {
          toast.error(response.error || 'Webhook test failed. Please check the URL and try again.');
        }
      } else if (response?.success) {
        toast.success(response.message || 'Webhook test successful!');
      } else {
        toast.error(response?.error || 'Webhook test failed. Please check the URL and try again.');
      }
    } catch (error) {
      toast.error('Failed to test webhook. Please check the URL and try again.');
      console.error('Webhook test error:', error);
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInSeconds = Math.floor((now - date) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInDays > 10) {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    } else if (diffInDays > 0) {
      return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInMinutes > 0) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? 'min' : 'mins'} ago`;
    } else {
      return 'Right now';
    }
  };

  const [showCreditPopup, setShowCreditPopup] = useState(false);
  const [selectedCredits, setSelectedCredits] = useState(null);
  const [isProcessingPurchase, setIsProcessingPurchase] = useState(false);
  const [customAmount, setCustomAmount] = useState('');

  const handleCreditPurchase = async (amount) => {
    if (isProcessingPurchase) return;
    
    try {
      setIsProcessingPurchase(true);
      const response = await fetch('http://127.0.0.1:8000/api/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          credits: amount,
          api_key: backendSettings?.license?.key
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }
      toast.success('Complete the purchase in the opened window');
      await window.electron.openExternal(data.url);
      console.log("Opened");
      setShowCreditPopup(false);
    } catch (error) {
      console.error('Failed to process credit purchase:', error);
      toast.error('Failed to process purchase. Please try again.');
    } finally {
      setIsProcessingPurchase(false);
    }
  };

  const handleCustomAmountSubmit = async (e) => {
    e.preventDefault();
    if (!customAmount || customAmount < 1) {
      toast.error('Please enter a valid amount');
      return;
    }
    await handleCreditPurchase(parseFloat(customAmount));
  };

  const CreditPurchasePopup = ({ onClose }) => {
    const [selectedAmount, setSelectedAmount] = useState(null);
    const [isProcessingPurchase, setIsProcessingPurchase] = useState(false);

    const handleCreditPurchase = async (amount) => {
      if (isProcessingPurchase) return;
      
      try {
        setIsProcessingPurchase(true);
        const response = await fetch('http://127.0.0.1:8000/api/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            credits: amount,
            api_key: backendSettings?.license?.key
          })
        });

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }
        toast.success('Complete the purchase in the opened window');
        await window.electron.openExternal(data.url);
        console.log("Opened");
        onClose();
      } catch (error) {
        console.error('Failed to process credit purchase:', error);
        toast.error('Failed to process purchase. Please try again.');
      } finally {
        setIsProcessingPurchase(false);
      }
    };

    return (
      <div className="credit-popup-overlay">
        <div className="credit-popup">
          <button className="close-button" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
          <div className="credit-popup-header">
            <h3>Purchase Credits</h3>
            <p>Select an amount to purchase</p>
          </div>
          <div className="credit-popup-content">
            <div className="credit-amount-buttons">
              <button
                className={`credit-amount-button ${selectedAmount === 5 ? 'selected' : ''}`}
                onClick={() => setSelectedAmount(5)}
              >
                <span className="amount">$5</span>
              </button>
              <button
                className={`credit-amount-button recommended ${selectedAmount === 15 ? 'selected' : ''}`}
                onClick={() => setSelectedAmount(15)}
              >
                <span className="amount">$15</span>
              </button>
              <button
                className={`credit-amount-button ${selectedAmount === 25 ? 'selected' : ''}`}
                onClick={() => setSelectedAmount(25)}
              >
                <span className="amount">$25</span>
              </button>
              <button
                className={`credit-amount-button ${selectedAmount === 50 ? 'selected' : ''}`}
                onClick={() => setSelectedAmount(50)}
              >
                <span className="amount">$50</span>
              </button>
            </div>
            <button
              className="primary-button"
              onClick={() => selectedAmount && handleCreditPurchase(selectedAmount)}
              disabled={!selectedAmount || isProcessingPurchase}
            >
              {isProcessingPurchase ? 'Processing...' : 'Continue to Payment'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="settings-container">
      <Toaster
        position="bottom-right"
        toastOptions={{
          style: {
            background: 'rgba(26, 26, 26, 0.5)',
            color: '#fff',
            border: 'none',
            backdropFilter: 'blur(10px)',
            padding: '12px 16px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            minWidth: '300px',
          },
          success: {
            iconTheme: {
              primary: 'rgba(76, 175, 80, 0.8)',
              secondary: 'rgba(26, 26, 26, 0.5)',
            },
            style: {
              background: 'rgba(26, 26, 26, 0.5)',
            },
          },
          error: {
            iconTheme: {
              primary: 'rgba(244, 67, 54, 0.8)',
              secondary: 'rgba(26, 26, 26, 0.5)',
            },
            style: {
              background: 'rgba(26, 26, 26, 0.5)',
            },
          },
          duration: 3000,
        }}
      />

      <div className="settings-grid">

        {/* License Card */}
        <div className="settings-card license-card">
          <div className="settings-card-header">
            <div className="settings-icon">
              <FaKey />
            </div>
            <h3>License Details</h3>
          </div>
          <div className="settings-card-content">
            <div className="license-status">
              <span className={`status-badge active`}>
                {licenseDetails.status}
              </span>
            </div>
            <div className="license-key">
              <div className="renewal-info">
                <span>License Key:</span>
                <code>{licenseDetails.license_key}</code>
              </div>
            </div>
            <div className="license-renewal">
              <div className="renewal-info">
                <span>Mail:</span>
                <code>{licenseDetails.email}</code>
              </div>
            </div>
            <div className="license-actions">
              <button 
                className="settings-button primary"
                onClick={handleManageSubscription}
              >
                Manage Subscription
              </button>
              <button
                className="settings-button danger"
                onClick={handleLogout}
                disabled={isLoggingOut}
              >
                {isLoggingOut ? (
                  <>
                    <FaSpinner className="spinning" />
                    Logging out...
                  </>
                ) : (
                  <>
                    <FaSignOutAlt />
                    Logout
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Balance Card */}
        <div className="settings-card balance-card">
          <div className="settings-card-header">
            <div className="settings-icon">
              <FaCoins />
            </div>
            <h3>Captcha Balance</h3>
          </div>
          <div className="settings-card-content">
            <div className="balance-amount">
              {/* Monthly Credits Progress */}
              <div className="credit-progress">
                <div className="credit-progress-header">
                  <div className="credit-progress-label">
                    <FaCoins className="icon" />
                    <span>Monthly Credits</span>
                  </div>
                  <div className="credit-progress-value">
                    ${balance.monthly_used?.toFixed(2) || '0.00'} / $5.00
                  </div>
                </div>
                <div className="credit-progress-bar-container">
                  <div 
                    className="credit-progress-bar monthly"
                    style={{ 
                      width: `${Math.min((balance.monthly_used || 0) / 5 * 100, 100)}%` 
                    }}
                  />
                </div>
              </div>

              {/* Purchased Credits Progress */}
              <div className="credit-progress">
                <div className="credit-progress-header">
                  <div className="credit-progress-label">
                    <FaCreditCard className="icon" />
                    <span>Purchased Credits</span>
                  </div>
                  <div className="credit-progress-value">
                    ${balance.purchased_used?.toFixed(2) || '0.00'} / ${balance.purchased?.toFixed(2) || '0.00'}
                  </div>
                </div>
                <div className="credit-progress-bar-container">
                  <div 
                    className="credit-progress-bar purchased"
                    style={{ 
                      width: `${Math.min((balance.purchased_used || 0) / (balance.purchased || 1) * 100, 100)}%` 
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="balance-history">
              <h4>Recent Transactions</h4>
              <div className="recent-transactions">
                <div className="transactions-list">
                  {recentTransactions.map((transaction, index) => (
                    <div key={index} className="transaction-item">
                      <div className={`transaction-amount ${transaction.id}`}>
                        {transaction.id === 'credit' ? '+' : '-'}{transaction.changed.toFixed(2)}
                      </div>
                      <div className="transaction-description">
                        {transaction.description}
                      </div>
                      <div className="transaction-time">
                        {formatTimestamp(transaction.timestamp)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="balance-actions">
              <div className="topup-buttons">
                <button 
                  className="settings-button primary card-option"
                  onClick={() => setShowCreditPopup(true)}
                >
                  <FaCreditCard />
                  Purchase Credits
                </button>
              </div>

              {/* Credit Purchase Popup */}
              {showCreditPopup && (
                <CreditPurchasePopup onClose={() => setShowCreditPopup(false)} />
              )}
            </div>
          </div>
        </div>

        {/* Webhook Card */}
        <div className="settings-card">
          <div className="settings-card-header">
            <div className="settings-icon">
              <FaDiscord />
            </div>
            <h3>Discord Webhook</h3>
            {/* Save status indicator */}
            <div className="webhook-status">
              {webhookSaveState === 'saving' && (
                <div className="status-indicator saving">
                  <FaSpinner className="spinning" />
                  <span>Saving...</span>
                </div>
              )}
              {webhookSaveState === 'saved' && (
                <div className="status-indicator saved">
                  <FaCheckCircle />
                  <span>Saved</span>
                </div>
              )}
              {webhookSaveState === 'error' && (
                <div className="status-indicator error">
                  <FaExclamationTriangle />
                  <span>Error</span>
                </div>
              )}
            </div>
          </div>
          <div className="settings-card-content">
            <div className="webhook-section">
              <div className="webhook-input-container">
                <input
                  type="text"
                  placeholder="Enter Discord Webhook URL"
                  value={webhookUrl}
                  onChange={handleWebhookChange}
                  className={`webhook-input ${webhookSaveState === 'error' ? 'error' : ''}`}
                />
              </div>

              {/* Help text */}
              <div className="webhook-help">
                Changes are automatically saved. Use a Discord webhook URL format:
                <code>https://discord.com/api/webhooks/...</code>
              </div>

              <div className="webhook-actions">
                <button
                  className="settings-button test-button"
                  onClick={handleTestWebhook}
                  disabled={!webhookUrl || webhookSaveState === 'saving'}
                >
                  Test
                </button>
                <button
                  className="settings-button copy-button"
                  onClick={handleCopyWebhook}
                  disabled={!webhookUrl}
                >
                  {copied ? <FaCheck /> : <FaCopy />}
                  {copied ? 'Copied!' : 'Copy'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings; 