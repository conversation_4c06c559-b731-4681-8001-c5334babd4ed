/* Loading screen container */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #0D0D0D;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* Smooth background gradient */
.loading-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(124, 77, 255, 0.06) 0%, transparent 70%);
  animation: smoothPulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Ghost logo with smooth lighting */
.loader-logo {
  width: 120px;
  height: 120px;
  position: relative;
  z-index: 2;
  animation: smoothLogoPulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  filter: brightness(1.2) contrast(1.1);
}

.loader-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 0 20px rgba(124, 77, 255, 0.3)) 
          drop-shadow(0 0 40px rgba(124, 77, 255, 0.15));
}

/* Loading message */
.loading-message {
  margin-top: 2rem;
  color: rgba(255, 255, 255, 0.87);
  font-size: 1.125rem;
  font-weight: 500;
  text-align: center;
  position: relative;
  z-index: 2;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Debug override button */
.debug-override-btn {
  margin-top: 2rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 68, 68, 0.1);
  color: #ff4444;
  border: 1px solid rgba(255, 68, 68, 0.3);
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.15s ease;
  position: relative;
  z-index: 2;
  font-family: inherit;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(255, 68, 68, 0.2);
}

.debug-override-btn:hover {
  background: rgba(255, 68, 68, 0.2);
  border-color: rgba(255, 68, 68, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 68, 68, 0.3);
}

.debug-override-btn:active {
  transform: translateY(0);
}

/* Smooth logo pulse animation */
@keyframes smoothLogoPulse {
  0%, 100% { 
    transform: scale(1); 
    filter: drop-shadow(0 0 20px rgba(124, 77, 255, 0.3)) 
            drop-shadow(0 0 40px rgba(124, 77, 255, 0.15));
  }
  50% { 
    transform: scale(1.05);
    filter: drop-shadow(0 0 25px rgba(124, 77, 255, 0.4)) 
            drop-shadow(0 0 50px rgba(124, 77, 255, 0.2))
            drop-shadow(0 0 70px rgba(124, 77, 255, 0.08));
  }
}

/* Smooth background pulse animation */
@keyframes smoothPulse {
  0%, 100% { 
    opacity: 0.06; 
    transform: scale(1); 
  }
  50% { 
    opacity: 0.12; 
    transform: scale(1.02); 
  }
}

/* Animated background gradient */
.loading-background {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle at 20% 20%, var(--color-primary-lightest) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--color-accent) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, var(--color-primary) 0%, transparent 70%);
  opacity: 0.15;
  animation: pulse 4s ease-in-out infinite alternate;
}

/* Secondary background layer */
.loading-background::after {
  content: '';
  position: absolute;
  top: -30%;
  left: -30%;
  width: 160%;
  height: 160%;
  background: linear-gradient(45deg, var(--color-primary-dark) 0%, var(--color-accent-dark) 100%);
  opacity: 0.05;
  animation: glow 3s ease-in-out infinite alternate-reverse;
}

/* Logo container with glow effect */
.logo-container {
  position: relative;
  z-index: 2;
  margin-bottom: 3rem;
}

.logo-text {
  font-size: 3.5rem;
  font-weight: var(--font-bold);
  background: linear-gradient(135deg, var(--color-primary-lightest) 0%, var(--color-accent) 50%, var(--color-primary-lightest) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 40px rgba(151, 57, 228, 0.8);
  letter-spacing: var(--tracking-header);
  text-transform: uppercase;
  position: relative;
  animation: glow 2.5s ease-in-out infinite alternate;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 140%;
  height: 140%;
  background: linear-gradient(135deg, var(--color-primary-lightest) 0%, var(--color-accent) 100%);
  filter: blur(25px);
  opacity: 0.4;
  animation: glow 3s ease-in-out infinite alternate;
  z-index: -1;
}

/* Additional glow rings */
.logo-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 160%;
  height: 160%;
  border: 2px solid var(--color-primary-lightest);
  border-radius: 50%;
  opacity: 0.2;
  animation: pulse 2s ease-in-out infinite;
  z-index: -2;
}

/* Enhanced loading spinner */
.spinner-container {
  position: relative;
  z-index: 2;
  margin-bottom: 2rem;
}

.spinner {
  width: 80px;
  height: 80px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-ring {
  position: absolute;
  border-radius: 50%;
}

.spinner-ring.outer {
  width: 100%;
  height: 100%;
  border: 4px solid rgba(151, 57, 228, 0.1);
  border-top: 4px solid var(--color-primary-lightest);
  animation: spin 2s linear infinite;
}

.spinner-ring.middle {
  width: 80%;
  height: 80%;
  border: 3px solid rgba(124, 77, 255, 0.1);
  border-top: 3px solid var(--color-accent);
  animation: spin 1.5s linear infinite reverse;
}

.spinner-ring.inner {
  width: 60%;
  height: 60%;
  border: 2px solid rgba(151, 57, 228, 0.1);
  border-top: 2px solid var(--color-primary);
  animation: spin 1s linear infinite;
}

.spinner-dot {
  width: 12px;
  height: 12px;
  background: var(--color-primary-lightest);
  border-radius: 50%;
  box-shadow: 
    0 0 15px var(--color-primary-lightest),
    0 0 30px var(--color-primary-lightest),
    0 0 45px var(--color-primary-lightest);
  animation: dotPulse 1.5s ease-in-out infinite;
}

/* Progress dots */
.progress-dots {
  display: flex;
  gap: 0.75rem;
  position: relative;
  z-index: 2;
}

.progress-dot {
  width: 10px;
  height: 10px;
  background: var(--color-primary-lightest);
  border-radius: 50%;
  opacity: 0.4;
  box-shadow: 0 0 8px var(--color-primary-lightest);
  animation: dotPulse 1.6s ease-in-out infinite both;
}

/* Debug info */
.debug-info {
  margin-top: 2rem;
  font-size: 0.75rem;
  color: var(--color-text-disabled);
  text-align: center;
  opacity: 0.6;
  position: relative;
  z-index: 2;
}

.debug-info div {
  margin-bottom: 0.25rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .logo-text {
    font-size: 2.5rem;
  }
  
  .spinner {
    width: 50px;
    height: 50px;
  }
  
  .loading-message {
    font-size: var(--text-md);
  }
}

@media (max-width: 480px) {
  .logo-text {
    font-size: 2rem;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
  }
  
  .spinner-ring.outer {
    border-width: 2px;
  }
  
  .spinner-ring.inner {
    border-width: 1px;
  }
} 