/* Global styles for Kasper-Q */
@import './App.css';

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: var(--font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-dark);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Selection styles */
::selection {
  background: var(--color-primary);
  color: white;
}

/* Focus styles */
:focus {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--color-primary-light);
  outline-offset: 2px;
}

/* Disable text selection on UI elements */
.app-container {
  user-select: none;
  -webkit-user-select: none;
}

/* Enable text selection in content areas */
.app-content {
  user-select: text;
  -webkit-user-select: text;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
