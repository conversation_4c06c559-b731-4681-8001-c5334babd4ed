from resilient_caller import resilient_call, update_session_proxy
from tls_client import Session
from requests.models import Response
from typing import Union
from random import choice
from re import search
from tasks_logger import Logger

def handle_redirect(response: Response) -> Union[Response, bool]:
    """
    Handle redirects

    Args:
        response (Response): Response object

    Returns:
        Union[Response, bool]: Response object or False
    """
    redirect = response.headers.get("Location")
    if redirect:
        domain = response.url.split("/")[2]
        response.url = redirect if redirect[0:4] == "http" else f"https://{domain}{redirect}"
    return response

REQUESTS_OPTS = {
    "conditions_criteria": lambda x: x.status_code,
    "conditions": {
        200: handle_redirect,
        301: handle_redirect,
        302: handle_redirect,
        303: handle_redirect,
        307: handle_redirect,
        308: handle_redirect
    }
}

def send_tls_request(
    url: str, 
    method: str="GET", 
    session=Session(client_identifier="chrome_110"),
    **kwargs
) -> Response:
    """
    Send a request to a url (resilient, w/TLS)

    Args:
        url (str): Url to send the request to
        method (str, optional): Method to use. Defaults to "GET".
        session (Session, optional): Session object. Defaults to Session(client_identifier="chrome_110").
        **kwargs: Keyword arguments to pass to the request
    
    Returns:
        Response: Response object
    """
    # pop conditions_criteria
    kwargs.pop("conditions", None)
    kwargs.pop("conditions_criteria", None)
    if method == "GET":
        return session.get(url, **kwargs)
    elif method == "POST":
        return session.post(url, **kwargs)
    elif method == "PUT":
        return session.put(url, **kwargs)
    elif method == "DELETE":
        return session.delete(url, **kwargs)
    else:
        raise ValueError("Invalid method")

class FixedResponse:
    def __init__(self, response: Response):
        self.response = response
        self.url = str(response.url)
        self.status_code = response.status_code
        self.text = response.text
        self.headers = response.headers
        self.cookies = response.cookies
    
    def __repr__(self):
        return f"<Response status_code={self.status_code} url={self.url}>"

    def __str__(self):
        return self.__repr__()
    
    def json(self):
        return self.response.json()

def set_proxy(
    session: Session,
) -> Union[bool, str]:
    """
    Set a proxy to the session

    Args:
        session (Session): Session object
    
    Returns:
        Union[bool, str]: False if no proxy is set, proxy if proxy is set
    """
    try:
        proxy = choice(open("proxies.txt", "r").read().splitlines())
        if proxy.strip() == '':
            return False
        update_session_proxy(session, proxy)
        return proxy
    except ArithmeticError:
        return False    

class Challenge:
    def __init__(self, extracted_script: dict) -> None:
        self.custpygomer_id, self.event_id, self.culture, self.challenges, self.domain, self.challengeApiChecksumHash = \
            extracted_script['customerId'], extracted_script['eventId'], extracted_script['culture'], \
                extracted_script['challenges'], extracted_script['proofOfWorkHost'], extracted_script['challengeApiChecksumHash']
        pass


def setup_logger(name: str) -> Logger:
    return Logger(
        level='INFO',
        formatting="{timestamp} [{level:^7}] ({name}) {message}",
        name=name
    )

extract_ua_info = lambda ua: (search(r'Chrome/(\d+)', ua).group(1), search(r'\(([^;]+);', ua).group(1), search(r'((?:\w+\s?)+)(\d+(_\d+)*)', ua).group(2), search(r'((?:\w+\s?)+)(\d+(_\d+)*)', ua).group(1)) if ua else (None, None, None, None)
