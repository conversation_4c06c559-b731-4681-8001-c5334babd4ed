@import './theme.css';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-family);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

/* App Container */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-background);
}

/* Title Bar */
.title-bar {
  background: var(--gradient-surface);
  padding: var(--spacing-sm) var(--spacing-md);
  -webkit-app-region: drag;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.title-bar-controls {
  -webkit-app-region: no-drag;
  display: flex;
  gap: var(--spacing-sm);
}

/* App Body */
.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Navigation */
.navigation {
  width: 240px;
  background: var(--color-surface);
  padding: var(--spacing-md);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-xs) 0;
  border-radius: var(--radius-md);
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.nav-item:hover {
  background: var(--color-surface-elevated);
  color: var(--color-text-primary);
}

.nav-item.active {
  background: var(--gradient-primary);
  color: var(--color-text-primary);
}

/* Main Content */
.app-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

/* Cards and Containers */
.card {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Buttons */
.button {
  background: var(--gradient-primary);
  color: var(--color-text-primary);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.button.secondary {
  background: var(--color-surface-elevated);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading Screen */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: var(--spacing-md);
}

.ghost-loader {
  width: 60px;
  height: 60px;
  border: 3px solid var(--color-primary-dark);
  border-top-color: var(--color-primary-lightest);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Status Bar */
.status-bar {
  background: var(--color-surface);
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--text-sm);
  color: var(--color-text-secondary);
}

/* Forms and Inputs */
.input {
  background: var(--color-surface-elevated);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--text-md);
  transition: all var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--color-primary-light);
  box-shadow: 0 0 0 2px rgba(134, 46, 210, 0.2);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-bold);
}

h1 { font-size: var(--text-3xl); }
h2 { font-size: var(--text-2xl); }
h3 { font-size: var(--text-xl); }
h4 { font-size: var(--text-lg); }

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-dark);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}
