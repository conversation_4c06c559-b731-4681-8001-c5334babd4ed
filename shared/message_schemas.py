"""
Shared Message Schemas for Kasper-Q WebSocket Communication

This module defines the standardized message format and schemas used for communication
between the React frontend and Python backend. It provides type safety, validation,
and versioning support for all WebSocket messages.

Author: <PERSON><PERSON>-Q Development Team
Created: 2024
Version: 1.0
"""

from typing import Dict, Any, Optional, Union, List, Literal
from dataclasses import dataclass, asdict
from enum import Enum
import time
import uuid


class MessageType(str, Enum):
    """Enumeration of all supported message types."""
    # Connection management
    CONNECTION_ESTABLISHED = "connection_established"
    CONNECTION_REJECTED = "connection_rejected"
    AUTHENTICATION_FAILURE = "authentication_failure"
    
    # Request/Response pairs
    REQUEST = "request"
    RESPONSE = "response"
    ERROR = "error"
    
    # Health checks
    PING = "ping"
    PONG = "pong"
    
    # Queue operations
    QUEUE_ENTRY_STARTED = "queue_entry_started"
    QUEUE_ENTRY_STOPPED = "queue_entry_stopped"
    QUEUE_PROGRESS_UPDATE = "queue_progress_update"
    QUEUE_STATUS = "queue_status"
    
    # License management
    VALIDATE_LICENSE_RESPONSE = "validate_license_response"
    LICENSE_UPDATED = "license_updated"
    
    # Settings management
    SETTINGS_DATA = "settings_data"
    DOCUMENTATION_DATA = "documentation_data"
    
    # Connection status
    CONNECTION_STATUS = "connection_status"


class ErrorCode(str, Enum):
    """Standardized error codes for consistent error handling."""
    # Authentication errors
    AUTHENTICATION_REQUIRED = "AUTH_001"
    INVALID_LICENSE = "AUTH_002"
    LICENSE_EXPIRED = "AUTH_003"
    
    # Connection errors
    CONNECTION_REJECTED = "CONN_001"
    SINGLE_CLIENT_VIOLATION = "CONN_002"
    CONNECTION_TIMEOUT = "CONN_003"
    
    # Message errors
    INVALID_MESSAGE_FORMAT = "MSG_001"
    UNKNOWN_COMMAND = "MSG_002"
    MISSING_PARAMETERS = "MSG_003"
    INVALID_PARAMETERS = "MSG_004"
    
    # Queue operation errors
    QUEUE_START_FAILED = "QUEUE_001"
    QUEUE_STOP_FAILED = "QUEUE_002"
    QUEUE_NOT_FOUND = "QUEUE_003"
    
    # System errors
    INTERNAL_ERROR = "SYS_001"
    SERVICE_UNAVAILABLE = "SYS_002"
    RATE_LIMITED = "SYS_003"


@dataclass
class MessageMetadata:
    """Metadata for message tracking and debugging."""
    version: str = "1.0"
    timestamp: float = None
    correlation_id: Optional[str] = None
    retry_count: int = 0
    priority: int = 0  # 0 = normal, 1 = high, 2 = critical
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.correlation_id is None:
            self.correlation_id = str(uuid.uuid4())


@dataclass
class ErrorDetails:
    """Standardized error information."""
    code: ErrorCode
    message: str
    details: Optional[Dict[str, Any]] = None
    recovery_suggestions: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)


@dataclass
class BaseMessage:
    """Base message structure for all WebSocket communications."""
    type: MessageType
    metadata: MessageMetadata
    payload: Dict[str, Any]
    error: Optional[ErrorDetails] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary for JSON serialization."""
        result = {
            "type": self.type.value,
            "metadata": asdict(self.metadata),
            "payload": self.payload
        }
        if self.error:
            result["error"] = self.error.to_dict()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseMessage':
        """Create message from dictionary (JSON deserialization)."""
        metadata_data = data.get("metadata", {})
        metadata = MessageMetadata(**metadata_data)
        
        error = None
        if "error" in data and data["error"]:
            error_data = data["error"]
            error = ErrorDetails(
                code=ErrorCode(error_data["code"]),
                message=error_data["message"],
                details=error_data.get("details"),
                recovery_suggestions=error_data.get("recovery_suggestions")
            )
        
        return cls(
            type=MessageType(data["type"]),
            metadata=metadata,
            payload=data.get("payload", {}),
            error=error
        )


@dataclass
class RequestMessage(BaseMessage):
    """Request message with command and parameters."""
    command: str = ""
    request_id: str = ""

    def __init__(self, command: str, params: Dict[str, Any] = None,
                 request_id: str = None, metadata: MessageMetadata = None):
        if request_id is None:
            request_id = str(uuid.uuid4())
        if metadata is None:
            metadata = MessageMetadata()
        if params is None:
            params = {}

        payload = {
            "command": command,
            "params": params,
            "request_id": request_id
        }

        super().__init__(
            type=MessageType.REQUEST,
            metadata=metadata,
            payload=payload
        )
        self.command = command
        self.request_id = request_id


@dataclass
class ResponseMessage(BaseMessage):
    """Response message with data and request correlation."""
    request_id: str = ""
    success: bool = True
    data: Any = None

    def __init__(self, request_id: str, success: bool = True,
                 data: Any = None, error: ErrorDetails = None,
                 metadata: MessageMetadata = None):
        if metadata is None:
            metadata = MessageMetadata()

        payload = {
            "request_id": request_id,
            "success": success,
            "data": data
        }

        super().__init__(
            type=MessageType.RESPONSE,
            metadata=metadata,
            payload=payload,
            error=error
        )
        self.request_id = request_id
        self.success = success
        self.data = data


# Command definitions for type safety
class Commands:
    """Standardized command names for request/response operations."""
    # Health checks
    PING = "ping"
    GET_CONNECTION_STATUS = "get_connection_status"
    
    # License operations
    VALIDATE_LICENSE = "validate_license"
    LOGOUT = "logout"

    # Settings operations
    GET_SETTINGS = "get_settings"
    UPDATE_SETTINGS = "update_settings"
    GET_DOCUMENTATION = "get_documentation"
    
    # Queue operations
    START_QUEUE_ENTRY = "start_queue_entry"
    STOP_QUEUE_ENTRY = "stop_queue_entry"
    GET_QUEUE_STATUS = "get_queue_status"


# Schema validation helpers
def validate_message_schema(data: Dict[str, Any]) -> bool:
    """Validate that a message conforms to the base schema."""
    required_fields = ["type", "metadata", "payload"]
    return all(field in data for field in required_fields)


def create_error_response(request_id: str, error_code: ErrorCode, 
                         message: str, details: Dict[str, Any] = None,
                         recovery_suggestions: List[str] = None) -> ResponseMessage:
    """Helper function to create standardized error responses."""
    error = ErrorDetails(
        code=error_code,
        message=message,
        details=details,
        recovery_suggestions=recovery_suggestions
    )
    
    return ResponseMessage(
        request_id=request_id,
        success=False,
        error=error
    )
