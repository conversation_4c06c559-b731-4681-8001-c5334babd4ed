import React, { useState } from 'react';
import './DeveloperMode.css';

const DeveloperMode = () => {
  const [activeTab, setActiveTab] = useState('console');
  const [command, setCommand] = useState('');
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'info', text: 'Kasper-Q Developer Console initialized', timestamp: new Date() },
    { type: 'success', text: 'Backend connection established', timestamp: new Date() },
    { type: 'info', text: 'Type "help" for available commands', timestamp: new Date() }
  ]);

  const tabs = [
    { id: 'console', name: 'Console', icon: 'fas fa-terminal' },
    { id: 'api', name: 'API Testing', icon: 'fas fa-code' },
    { id: 'logs', name: 'System Logs', icon: 'fas fa-file-alt' },
    { id: 'config', name: 'Configuration', icon: 'fas fa-cog' }
  ];

  const handleCommandSubmit = (e) => {
    e.preventDefault();
    if (!command.trim()) return;

    // Add command to output
    setConsoleOutput(prev => [...prev, {
      type: 'command',
      text: `> ${command}`,
      timestamp: new Date()
    }]);

    // Simulate command response
    setTimeout(() => {
      let response = '';
      switch (command.toLowerCase()) {
        case 'help':
          response = 'Available commands: scan, status, clear, config, exit';
          break;
        case 'status':
          response = 'System Status: Online | Backend: Connected | Mode: Developer';
          break;
        case 'clear':
          setConsoleOutput([]);
          setCommand('');
          return;
        default:
          response = `Unknown command: ${command}`;
      }
      
      setConsoleOutput(prev => [...prev, {
        type: 'response',
        text: response,
        timestamp: new Date()
      }]);
    }, 100);

    setCommand('');
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'console':
        return (
          <div className="console-container">
            <div className="console-output">
              {consoleOutput.map((line, index) => (
                <div key={index} className={`console-line ${line.type}`}>
                  <span className="timestamp">
                    {line.timestamp.toLocaleTimeString()}
                  </span>
                  <span className="text">{line.text}</span>
                </div>
              ))}
            </div>
            <form onSubmit={handleCommandSubmit} className="console-input">
              <span className="prompt">kasper-q$</span>
              <input
                type="text"
                value={command}
                onChange={(e) => setCommand(e.target.value)}
                className="command-input"
                placeholder="Enter command..."
                autoFocus
              />
            </form>
          </div>
        );
      
      case 'api':
        return (
          <div className="api-testing">
            <h3>API Testing Interface</h3>
            <div className="api-form">
              <div className="input-group">
                <label>Method</label>
                <select className="input">
                  <option>GET</option>
                  <option>POST</option>
                  <option>PUT</option>
                  <option>DELETE</option>
                </select>
              </div>
              <div className="input-group">
                <label>Endpoint</label>
                <input type="text" className="input" placeholder="/api/scan" />
              </div>
              <div className="input-group">
                <label>Request Body</label>
                <textarea className="input" rows="6" placeholder='{"target": "example.com"}'></textarea>
              </div>
              <button className="btn btn-primary">Send Request</button>
            </div>
          </div>
        );
      
      case 'logs':
        return (
          <div className="logs-viewer">
            <h3>System Logs</h3>
            <div className="log-entries">
              <div className="log-entry info">
                <span className="log-time">2024-01-15 10:30:15</span>
                <span className="log-level">INFO</span>
                <span className="log-message">Application started successfully</span>
              </div>
              <div className="log-entry warning">
                <span className="log-time">2024-01-15 10:30:20</span>
                <span className="log-level">WARN</span>
                <span className="log-message">High memory usage detected</span>
              </div>
              <div className="log-entry error">
                <span className="log-time">2024-01-15 10:30:25</span>
                <span className="log-level">ERROR</span>
                <span className="log-message">Failed to connect to external service</span>
              </div>
            </div>
          </div>
        );
      
      case 'config':
        return (
          <div className="config-editor">
            <h3>System Configuration</h3>
            <div className="config-form">
              <div className="config-section">
                <h4>Backend Settings</h4>
                <div className="input-group">
                  <label>Backend URL</label>
                  <input type="text" className="input" defaultValue="http://localhost:8080" />
                </div>
                <div className="input-group">
                  <label>API Key</label>
                  <input type="password" className="input" defaultValue="••••••••••••" />
                </div>
              </div>
              <div className="config-section">
                <h4>Scan Settings</h4>
                <div className="input-group">
                  <label>Default Timeout (ms)</label>
                  <input type="number" className="input" defaultValue="30000" />
                </div>
                <div className="input-group">
                  <label>Max Concurrent Scans</label>
                  <input type="number" className="input" defaultValue="5" />
                </div>
              </div>
              <button className="btn btn-primary">Save Configuration</button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="developer-mode">
      <div className="mode-header">
        <h2>
          <i className="fas fa-code"></i>
          Developer Mode
        </h2>
        <p>Advanced debugging and development tools for Kasper-Q</p>
      </div>

      <div className="developer-content">
        <div className="tab-navigation">
          {tabs.map(tab => (
            <button
              key={tab.id}
              className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => setActiveTab(tab.id)}
            >
              <i className={tab.icon}></i>
              {tab.name}
            </button>
          ))}
        </div>

        <div className="tab-content">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default DeveloperMode;
