.title-bar,
.title-bar.compact {
  height: 24px;
  min-height: 24px;
  background: var(--color-surface);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px;
  -webkit-app-region: drag;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
}

.title-bar-left {
  display: flex;
  align-items: center;
  height: 100%;
  min-width: 22px;
  -webkit-app-region: drag;
}

.title-bar-left svg {
  display: block;
  margin: 0;
  vertical-align: middle;
}

.mode-switcher {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(60, 20, 80, 0.18);
  border-radius: 999px;
  box-shadow: 0 2px 12px 0 rgba(151, 57, 228, 0.10);
  padding: 2px 8px;
  margin: 0 auto;
  -webkit-app-region: no-drag;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  height: 22px;
  backdrop-filter: blur(8px) saturate(1.2);
}

.mode-pill {
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  font-size: 0.98rem;
  font-weight: var(--font-medium);
  padding: 3px 18px;
  border-radius: 999px;
  cursor: pointer;
  transition: color var(--transition-fast), background var(--transition-fast), box-shadow var(--transition-fast), font-weight var(--transition-fast);
  position: relative;
  z-index: 1;
  outline: none;
  box-shadow: none;
  height: 18px;
  display: flex;
  align-items: center;
  letter-spacing: 0.02em;
}

.mode-pill.active {
  background: linear-gradient(90deg, #862ED2 0%, #FF9EFF 100%);
  color: #fff;
  box-shadow: 0 2px 12px 0 rgba(151, 57, 228, 0.18), 0 0 0 2px rgba(151, 57, 228, 0.10);
  font-weight: var(--font-bold);
  filter: brightness(1.08) saturate(1.2);
}

.mode-pill:focus-visible {
  outline: 2px solid var(--color-primary-lightest);
}

.mode-pill:not(.active):hover {
  background: rgba(151, 57, 228, 0.10);
  color: var(--color-primary-lightest);
  font-weight: var(--font-semibold);
}

.window-controls,
.window-controls.modern {
  display: flex;
  gap: 8px;
  -webkit-app-region: no-drag;
  align-items: center;
  height: 100%;
}

.window-controls.modern .window-control {
  width: 22px;
  height: 22px;
  min-width: 22px;
  min-height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.04);
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: 7px;
  margin: 0;
  box-shadow: none;
  font-size: 1rem;
  position: relative;
}

.window-controls.modern .window-control svg {
  width: 16px;
  height: 16px;
  display: block;
}

.window-controls.modern .window-control.minimize:hover {
  background: linear-gradient(90deg, #FFD700 0%, #862ED2 100%);
  color: var(--color-surface);
  box-shadow: 0 0 8px 2px #FFD70044, 0 0 4px 1px var(--color-primary-lightest, #9739E4);
}

.window-controls.modern .window-control.close:hover {
  background: #e81123;
  color: white;
  box-shadow: 0 0 8px 2px #e81123, 0 0 4px 1px #e81123;
}

@media (max-width: 900px) {
  .title-bar.compact {
    height: 36px;
    min-height: 36px;
    padding: 0 4px;
  }
  .mode-switcher {
    height: 28px;
  }
  .mode-pill {
    font-size: 0.95rem;
    height: 26px;
    padding: 4px 14px;
  }
  .window-controls.modern .window-control {
    width: 28px;
    height: 28px;
    min-width: 28px;
    min-height: 28px;
    font-size: 1.15rem;
  }
} 