import time
from requests import Session
from tls_client.response import Response

from queue_it.main import Queue<PERSON><PERSON><PERSON><PERSON><PERSON>
from threading import Thread


if __name__ == "__main__":
    print("Starting...")
    session = Session()
    response = session.get(
        url="https://footlocker.queue-it.net/?c=footlocker&e=cxcdtest02&t=https%3A%2F%2Fwww.footlocker.com%2Fsearch%3Fquery%3D6H053P85&cid=en-EN",
        headers={
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36"
        }
    )
    handler = QueueItHandler(
        session=session,
        url=response,
        license_key="K-203562-2C152996-6FDFEBW",
        hwid="996bff208d6346293982b7deaf537a9ee64976df608ee4b1ebbb177a62c6b79b",
        referrer=response.url,
    )
    Thread(target=handler.solve).start()
    while not handler.pass_url:
        print(f"Status: {handler.status}")
        time.sleep(1)
    print(f"Status: {handler.status}")
    print(f"Pass url: {handler.pass_url}")