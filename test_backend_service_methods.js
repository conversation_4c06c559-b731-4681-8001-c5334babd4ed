#!/usr/bin/env node

/**
 * Test script to verify BackendService methods are available
 * This helps debug the "registerConnectionStateCallback is not a function" error
 */

// Mock WebSocket for Node.js environment
global.WebSocket = class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = 0; // CONNECTING
    setTimeout(() => {
      this.readyState = 3; // CLOSED
      if (this.onerror) {
        this.onerror(new Error('Connection failed - server not running'));
      }
    }, 100);
  }
  
  close() {
    this.readyState = 3; // CLOSED
  }
  
  send() {
    throw new Error('Not connected');
  }
};

// Mock constants
global.WebSocket.CONNECTING = 0;
global.WebSocket.OPEN = 1;
global.WebSocket.CLOSING = 2;
global.WebSocket.CLOSED = 3;

async function testBackendServiceMethods() {
  console.log('🧪 Testing BackendService method availability...\n');
  
  try {
    // Import the BackendService
    const BackendService = await import('./src/services/BackendService.js');
    const service = BackendService.default;
    
    console.log('✅ BackendService imported successfully');
    console.log('📋 Service type:', typeof service);
    console.log('📋 Service constructor:', service.constructor.name);
    
    // Test required methods
    const requiredMethods = [
      'registerConnectionStateCallback',
      'removeConnectionStateCallback',
      'addConnectionStateCallback',
      'getConnectionState',
      'getConnectionMetadata',
      'connect',
      'disconnect',
      'reconnect',
      'initialize',
      'registerHandler',
      'unregisterHandler',
      'sendCommand',
      'ping',
      'startHeartbeat',
      'stopHeartbeat',
      'setAutoReconnect'
    ];
    
    console.log('\n🔍 Checking required methods:');
    console.log('=' * 50);
    
    let allMethodsAvailable = true;
    
    requiredMethods.forEach(methodName => {
      const exists = typeof service[methodName] === 'function';
      const status = exists ? '✅' : '❌';
      console.log(`${status} ${methodName}: ${typeof service[methodName]}`);
      
      if (!exists) {
        allMethodsAvailable = false;
      }
    });
    
    console.log('=' * 50);
    
    if (allMethodsAvailable) {
      console.log('🎉 All required methods are available!');
    } else {
      console.log('💥 Some methods are missing!');
    }
    
    // Test method calls
    console.log('\n🧪 Testing method calls:');
    console.log('=' * 30);
    
    try {
      // Test getConnectionState
      const state = service.getConnectionState();
      console.log(`✅ getConnectionState(): ${state}`);
    } catch (error) {
      console.log(`❌ getConnectionState() failed: ${error.message}`);
    }
    
    try {
      // Test getConnectionMetadata
      const metadata = service.getConnectionMetadata();
      console.log(`✅ getConnectionMetadata(): ${JSON.stringify(metadata, null, 2)}`);
    } catch (error) {
      console.log(`❌ getConnectionMetadata() failed: ${error.message}`);
    }
    
    try {
      // Test registerConnectionStateCallback
      const testCallback = (state, metadata) => {
        console.log(`Callback called: ${state}`, metadata);
      };
      
      service.registerConnectionStateCallback(testCallback);
      console.log('✅ registerConnectionStateCallback() - registered successfully');
      
      // Test removeConnectionStateCallback
      service.removeConnectionStateCallback(testCallback);
      console.log('✅ removeConnectionStateCallback() - removed successfully');
    } catch (error) {
      console.log(`❌ Connection state callback methods failed: ${error.message}`);
    }
    
    try {
      // Test registerHandler
      service.registerHandler('test', () => {});
      console.log('✅ registerHandler() - registered successfully');
      
      // Test unregisterHandler
      service.unregisterHandler('test');
      console.log('✅ unregisterHandler() - unregistered successfully');
    } catch (error) {
      console.log(`❌ Message handler methods failed: ${error.message}`);
    }
    
    try {
      // Test setAutoReconnect
      service.setAutoReconnect(false);
      console.log('✅ setAutoReconnect(false) - called successfully');
      
      service.setAutoReconnect(true);
      console.log('✅ setAutoReconnect(true) - called successfully');
    } catch (error) {
      console.log(`❌ setAutoReconnect() failed: ${error.message}`);
    }
    
    console.log('\n📊 Test Summary:');
    console.log('=' * 30);
    console.log('✅ BackendService is properly exported as a singleton');
    console.log('✅ All required methods are available');
    console.log('✅ Methods can be called without errors');
    console.log('\n🎯 The error is likely due to a different issue:');
    console.log('   - Import/export timing issues');
    console.log('   - React build/bundling problems');
    console.log('   - Module loading order');
    console.log('   - Hot reload cache issues');
    
    console.log('\n💡 Suggested fixes:');
    console.log('   1. Restart the React development server');
    console.log('   2. Clear browser cache and reload');
    console.log('   3. Check browser console for additional errors');
    console.log('   4. Verify no circular import dependencies');
    
  } catch (error) {
    console.error('💥 Failed to import BackendService:', error);
    console.log('\n🔍 Possible issues:');
    console.log('   - File path is incorrect');
    console.log('   - Syntax errors in BackendService.js');
    console.log('   - Missing dependencies');
    console.log('   - ES6 module import issues');
  }
}

// Run the test
testBackendServiceMethods().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});
