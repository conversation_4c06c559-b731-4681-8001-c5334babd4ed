from tls_client import Session
from re import search, sub, DOTALL
from json import loads, dumps
from requests import Response
from typing import Union
from time import sleep, time
from urllib.parse import urlparse, unquote, quote
from bs4 import BeautifulSoup
from js2py import eval_js
from uuid import uuid4
from tasks_logger import Logger


from .helpers import send_tls_request, REQUESTS_OPTS, \
    setup_logger, extract_ua_info
from .solvers import SOLVERS
from .exceptions import ChallengeUnsupportedError, SolverKeyNotSetError

class QueueItHandler:
    @staticmethod
    def check_for_queue_it(response: Response) -> Union[dict, bool]:
        """Check if Queue-IT is required on the response"""
        try: 
            return loads(eval_js('''function() {d = ''' + response.text.split('InQueueView(')[1].split(");")[0] + ''' 
            return JSON.stringify(d)}''')()) 
        except ArithmeticError: return False
    
    def __init__(
        self, 
        session: Session, 
        url: Union[str, Response], 
        user_agent: str="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36",
        license_key: str=None,
        hwid: str=None,
        referrer: str=None,
        scv: dict=None,
    ) -> None:
        self.status = "initializing"
        self.error = None
        self.pass_url = False
        self.should_stop = False

        self.logger = Logger(
            level="INFO",
            format="{timestamp} (QUEUE) {level}: {message}"
        )
        self.session, self.url, self.referrer, self.scv, \
            self.license_key, self.hwid, self.user_agent = \
                session, url, referrer, scv, license_key, \
                    hwid, user_agent
        self.solved = False

        # Load browser info
        self.chrome_version, self.os, self.os_version, self.os_full_name = extract_ua_info(self.user_agent)
        self.sec_ch_ua = f'Google Chrome";v="{self.chrome_version}", "Not(A:Brand";v="8", "Chromium";v="{self.chrome_version}"'
        self.os = "\"macOS\"" if self.os == "Macintosh" else "\"Windows\""
        self.logger.info(f"QueueItHandler initialized with status: {self.status}")

    def stop(self):
        """Stop the handler"""
        self.should_stop = True
        self.status = "stopped"
        self.error = "Task stopped by user"
        self.logger.info("QueueItHandler stop requested")

    def load_queue_page(self) -> None:
        """Load the queue page from the url"""
        if not "queue-it" in self.url:
            self.status = "error"
            self.error = "Invalid queue-it url"
            self.logger.error("Queue-it not found in the url")
            return False
        
        headers = {
            "authority": self.url.split("/")[2],
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-language": "en-GB,en;q=0.9",
            "sec-ch-ua": self.sec_ch_ua,
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": self.os,
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": self.user_agent
        }
        self.logger.debug("Loading queue page")
        self.response = send_tls_request(
            self.url,
            "GET",
            session=self.session,
            headers=headers,
            **REQUESTS_OPTS
        )
        self.status = "initializing"
        return True
    
    def get_enqueue_token(self) -> str:
        """
        Get the enqueue token from the url
        
        Returns:
            `str`: The enqueue token
        """
        # Get enqueue token
        try:
            try:
                enqueue_token = self.url.split("&qet=")[1].split("&")[0]
            except:
                enqueue_token = self.response.url.split("&qet=")[1].split("&")[0]
        except:
            try:
                try:
                    enqueue_token = self.url.split("&enqueuetoken=")[1].split("&")[0]
                except:
                    enqueue_token = self.response.url.split("&enqueuetoken=")[1].split("&")[0]
            except:
                raise ArithmeticError("Couldn't find enqueue token")
        return enqueue_token

    def solve(self) -> str: 
        """
        Solve the challenge and return the url with the queue token
        
        Returns:
            `str`: The url with the queue token
        """
        try:
            self.logger.info("Starting queue bypass process")
            
            if type(self.url) == str:
                self.logger.debug("Loading queue page cause passed just url")
                if not self.load_queue_page():
                    self.status = "error"
                    self.error = "Failed to load queue page"
                    return False
            else:
                self.response = self.url
                self.url = self.response.url
            
            try:
                queue_cookie = [c.value for c in self.session.cookies if "Queue-it-" in c.name][0]
                # Example Cid=en-GB&f=0
                # We convert it to a dict and check if the previous url
                # got the right values
                url_params = {k.lower(): v for k, v in [c.split("=") for c in self.url.split("?")[1].split("&")]}
                queue_cookie = {k.lower(): v for k, v in [c.split("=") for c in queue_cookie.split("&")]}
                same = True
                for k, v in queue_cookie.items():
                    if url_params.get(k) != v:
                        same = False
                        self.url = self.url.replace(f"{k}={url_params.get(k)}", f"{k}={v}")
            except: 
                same = True
            
            if self.referrer is None:
                self.referrer = self.url
            
            if not same:
                self.logger.debug("Loading queue page cause passed url didn't have the right params")
                self.logger.debug(f"New url: {self.url}")
                self.load_queue_page()
            
            if not QueueItHandler.check_for_queue_it(self.response):
                self.logger.debug(f"Queue-it not found on {self.response.url}")
                self.status = "completed"  # Update status if no queue found
                return self.response.url
            
            self.solved = True
            self.status = "solving"
            self.logger.info("Queue-it found")
            self.domain = urlparse(self.response.url).netloc
            body = self.response.text
            
            # This is just to quicklu get the data from the js
            # can be replaced with a regex without much effort
            data = loads(eval_js('''function() {d = ''' + body.split('InQueueView(')[1].split(");")[0] + ''' 
            return JSON.stringify(d)}''')()) 
            self.enqueue_token = False
            self.culture, self.customer_id, self.event_id, self.target_url, self.layout, \
                self.layout_version, self.challenges, self.challenges_endpoint, \
                    self.challenge_hash = \
                    data['culture'], data["customerId"], data["eventId"], unquote(data["targetUrl"]), \
                        data['layout'], data['layoutVersion'], data['challenges'], data['challengeVerifyEndpoint'], \
                            data['challengeApiChecksumHash']
            try:
                if data['isQueueitEnqueueTokenRequired']:
                    self.enqueue_token = self.get_enqueue_token() 
            except:
                self.logger.warning("Couldn't find enqueue token, it might not be required")
            
            if self.target_url == "":
                try: 
                    self.target_url = unquote(self.url.split("&t=")[1].split("&")[0])
                except:
                    self.logger.debug(f"Target URL seems empty and not provided '{self.target_url}'")
                else:
                    self.logger.debug(f"Target url not found, using url from url: {self.target_url}")
                    if '&t=' in self.target_url:
                        self.target_url = unquote(self.target_url.split('&t=')[1].split('&')[0])
                        self.logger.debug(f"Target url: {self.target_url}")
                    if "http" not in self.target_url:
                        self.target_url = f"https://{self.domain}/{self.target_url}"
                        self.logger.debug(f"Target url: {self.target_url}")
            self.logger.debug(f"Referrer: {self.referrer}")

            self.status = "solving"
            self.logger.info(f"Found {len(self.challenges)} challenges to solve / {' '.join([challenge['name'] for challenge in self.challenges])}")
            self.solved_challenges = [
                self.submit_challenge(challenge["name"])
                for challenge in self.challenges
            ]

            self.pass_url = self.enter_queue() # Get final redirect
            if self.pass_url:
                self.status = "completed"
                self.logger.info("Queue bypass completed successfully")
            return self.pass_url
            
        except ArithmeticError as e:
            self.logger.error(f"Error in solve method: {e}")
            self.status = "error"
            self.error = str(e)
            return False

    def submit_challenge(self, name: str) -> dict:
        """Solve and get the response data to Queue-it

        Args:
            name (str): The challenge name

        Returns:
            `dict`: The challenge response data
        
        Raises:
            ChallengeUnsupportedError: If the challenge is not supported
            SolverKeyNotSetError: If the solver key is not set
        """
        self.logger.debug(f"Initializing challenge: {name}")
        if not (solver := SOLVERS.get(name)):
            raise ChallengeUnsupportedError("Unsupported challenge found", [name], {"challenges_found": self.challenges})
        solver_obj = solver(
            self.user_agent,
            self.sec_ch_ua,
            self.chrome_version,
            self.os,
            self.os_full_name,
            self.os_version,
            self.customer_id,
            self.event_id,
            self.challenge_hash,
            self.url,
            self.domain,
            self.license_key,
            self.hwid
        )
        while 1:
            self.logger.info(f"Starting challenge: {name}")
            try:
                self.logger.debug(f"Loading challenge data: {name}")
                # Load challenge data
                challenge = send_tls_request(
                    solver_obj.url, 
                    "POST", 
                    session=self.session,
                    headers=solver_obj.headers,
                    **REQUESTS_OPTS
                ).json()

                # Solve challenge
                self.logger.debug(f"Solving challenge: {name}")
                self.logger.info(f"Solving with {self.license_key=}, {self.hwid=}")
                solution = solver_obj.solve(challenge)
                print(solution)
                # Submit challenge
                self.logger.debug(f"Submitting challenge: {name}")
                headers = {
                    "Host": self.domain,
                    "sec-ch-ua": self.sec_ch_ua,
                    "accept": "application/json, text/javascript, */*; q=0.01",
                    "content-type": "application/json",
                    "x-requested-with": "XMLHttpRequest",
                    "sec-ch-ua-mobile": "?0",
                    "user-agent": self.user_agent,
                    "sec-ch-ua-platform": self.os,
                    "origin": f"https://{self.domain}",
                    "sec-fetch-site": "same-origin",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-dest": "empty",
                    "referer": self.url,
                    "accept-language": "en-GB,en;q=0.9"
                }
                url = f"https://{self.domain}/challengeapi/verify"
                response = send_tls_request(
                    url, 
                    "POST", 
                    session=self.session,
                    json=solution, 
                    headers=headers,
                    **REQUESTS_OPTS
                )
                data = response.json()
                print(data)
            except ArithmeticError as e:
                self.logger.error(f"(ArithmeticError) Error solving challenge: {name} -> {e}")
                continue
            
            if response.status_code == 400:
                self.logger.error(f"""(Status) Error solving challenge: {name} -> {data['title']}: ({' - '.join([f"{k}: {','.join(v)}" for k,v in data['errors'].items()])})""")
            elif data == ['DecryptionError']:
                self.logger.error(f"(DecryptionError) Error solving challenge: {name} -> DecryptionError (Wrong value provided)")
            elif data == ['FailedAgeValidation']:
                self.logger.error(f"(FailedAgeValidation) Error solving challenge: {name} -> FailedAgeValidation (Challenge expired)")
            elif data == ['invalid-input-response']:
                self.logger.error(f"(invalid-input-response) Error solving challenge: {name} -> invalid-input-response (Wrong value provided)")
            elif not data.get("sessionInfo"):
                self.logger.error(f"(sessionInfo) Error solving challenge: {name} -> {data}")
            else:
                self.logger.info(f"Successfully solved challenge: {name}")
                break
            continue
        return data['sessionInfo'] 

    def enter_queue(self) -> None:
        """Enter the queue"""
        payload = {
            "layoutName": self.layout, 
            "customUrlParams": "",
            "Referrer": self.referrer,
            "targetUrl": self.target_url
        } 
        if len(self.solved_challenges) > 0: payload["challengeSessions"] = self.solved_challenges
        self.logger.debug(f"Entering queue with {len(self.solved_challenges)} solved challenges")
        headers = {
            "Host":   self.domain,
            "sec-ch-ua": self.sec_ch_ua,
            "sec-ch-ua-mobile": "?0",
            "user-agent": self.user_agent,
            "content-type": "application/json",
            "accept": "application/json, text/javascript, */*; q=0.01",
            "x-queueit-qpage-referral": self.referrer,
            "x-requested-with": "XMLHttpRequest",
            "sec-ch-ua-platform": self.os,
            "origin": f"https://{self.domain}",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": self.url,
            "accept-language": "en-GB,en;q=0.9"
        }
        url = f"https://{self.domain}/spa-api/queue/{self.customer_id}/{self.event_id}/enqueue"
        self.logger.debug(f"Queue URL: {url}")
        self.timestamp = int(time() * 1000)
        self.queue_querystring = {
            "cid": self.culture,
            "l": quote(self.layout),
            "seid": str(uuid4()),
            "sets": str(self.timestamp)
        }
        if self.enqueue_token: payload["QueueitEnqueueToken"] = self.enqueue_token
        join_payload = {
            "cid": self.culture,
        }
        if self.scv is not None:
            self.logger.debug(f"Using SCV: {self.scv}")
            join_payload["scv"] = dumps(self.scv)
        while 1:
            self.logger.info("Attempting to join queue...")
            try:
                self.status = "queued"
                response = send_tls_request(
                    url, 
                    "POST", 
                    session=self.session,
                    headers=headers, 
                    json=payload, 
                    params=join_payload,
                    **REQUESTS_OPTS
                )
                try: response = response.json()
                except ArithmeticError: 
                    if 'softblock' in response.url:
                        self.logger.critical(f"Softblock -> {response.url}")
                        return response.url
                    else:
                        self.logger.critical(f"Error -> {response.text}")
                        continue
                if response.get("redirectUrl") and not response.get("isRedirectToTarget"):
                    self.logger.critical(f"Invalid redirect -> {response['redirectUrl']}")
                    return response["redirectUrl"] if response['redirectUrl'][0:4] == 'http' else f"https://{self.domain}{response['redirectUrl']}"
                
                self.queue_id, self.redirect_url, challenge_failed, missing_custom_data_key, _, invalid_enqueue, server_busy = list(response.values())
            except ArithmeticError as e:
                self.logger.error(f"Error entering queue -> {e}")
                continue
            
            if any([challenge_failed, missing_custom_data_key, invalid_enqueue, server_busy]):
                self.logger.critical(f"Error in response -> {response}")
                self.status = "error"
                self.error = "Error in response"
                return False 
            elif not self.queue_id:
                self.logger.info("Unable to join queue!")
                self.status = "error"
                self.error = "Unable to join queue"
                return False
            
            elif not self.queue_id and self.redirect_url:
                self.logger.info("Queue down!")
                self.status = "error"
                self.error = "Queue ended"
                return False
            else:
                self.logger.info(f"Successfully joined queue -> {self.queue_id}")
                return self.handle_queue()
    
    def handle_queue(self) -> None:
        """Handle the queue""" 
        self.queue_passed = False
        self.queue_url = f"https://{self.domain}/spa-api/queue/{self.customer_id}/{self.event_id}/{self.queue_id}/status"
        self.queue_payload = {
            "customUrlParams": "",
            "isBeforeOrIdle": False,
            "isClientRedayToRedirect": True,
            "layoutName": self.layout,
            "layoutVersion": self.layout_version,
            "targetUrl": self.target_url,
        } 
        
        headers = {
            "Host": self.domain,
            "sec-ch-ua": self.sec_ch_ua,
            "accept": "application/json, text/javascript, */*; q=0.01",
            "content-type": "application/json",
            "x-requested-with": "XMLHttpRequest",
            "sec-ch-ua-mobile": "?0",
            "user-agent": self.user_agent,
            "sec-ch-ua-platform": self.os,
            "origin": f"https://{self.domain}",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "referer": f"https://{self.domain}/softblock",
            "accept-language": "en-GB,en;q=0.9"
        }
        while not self.should_stop: 
            self.status = "waiting"
            self.logger.debug("Checking queue status...")
            res = send_tls_request(
                self.queue_url, 
                "POST", 
                session=self.session,
                headers=headers, 
                json=self.queue_payload, 
                params=self.queue_querystring,
                #**REQUESTS_OPTS
            )
            response = res.json()

            try: 
                self.logger.debug(f"Queue status -> {response['forecastStatus']} / Should end in {response['ticket']['whichIsIn'] if response['ticket']['whichIsIn'] is not None else 'N/A'}")
            except: 
                self.logger.debug("Waiting for queue to start...")
            queue_item = res.headers.get('X-Queueit-Queueitem-V1')
            if queue_item: 
                self.logger.info(f"Queue item -> {queue_item}")
                headers['X-Queueit-Queueitem-V1'] = queue_item
            redirect = response.get("redirectUrl")
            if redirect: break

            if response.get('forecastStatus') == 'Ended':
                self.logger.info("Queue ended")
                self.status = "error"
                self.error = "Queue ended"
                return (response["redirectUrl"] if response['redirectUrl'][0:4] == 'http' else f"https://{self.domain}{response['redirectUrl']}") if response.get("redirectUrl") else self.target_url
            elif response.get('forecastStatus') == 'NotStarted':
                self.logger.info("Queue not started")
                self.status = "error"
                self.error = "Queue not started"
                return (response["redirectUrl"] if response['redirectUrl'][0:4] == 'http' else f"https://{self.domain}{response['redirectUrl']}") if response.get("redirectUrl") else self.target_url
            elif response.get("redirectUrl") and not response.get("isRedirectToTarget"):
                self.logger.critical(f"Invalid redirect -> {response.get('redirectUrl')}")
                self.status = "error"
                self.error = "Invalid redirect"
                return response["redirectUrl"] if response['redirectUrl'][0:4] == 'http' else f"https://{self.domain}{response['redirectUrl']}"
            elif response.get("redirectUrl") and response.get("isRedirectToTarget"):
                self.logger.info("Queue passed")
                self.status = "active"
                return (response["redirectUrl"] if response['redirectUrl'][0:4] == 'http' else f"https://{self.domain}{response['redirectUrl']}") if response.get("redirectUrl") else self.target_url
            
            if self.should_stop:
                self.logger.info("Stop requested, breaking queue loop")
                break
                
            delay = response['updateInterval'] / 1000
            self.logger.info(f"Sleeping for {round(delay)} seconds... (queue delay)")
            sleep(delay)
            
        if self.should_stop:
            self.status = "stopped"
            self.error = "Task stopped by user"
            return None
            
        self.status = "active"
        self.logger.info(f"Queue passed -> {redirect}")
        return redirect if redirect[0:5] == "https" else f"https://{self.domain}{redirect}"