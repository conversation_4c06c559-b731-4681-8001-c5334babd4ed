import React, { use<PERSON><PERSON>back, useState, useEffect } from 'react';
import {
  FaGithub,
  FaYoutube,
  FaBook,
  FaExternalLinkAlt,
  FaRocket,
  FaCode,
  FaBookOpen,
  FaLaptopCode,
  FaPlayCircle,
  FaCodeBranch,
  FaBookReader,
  FaGraduationCap,
  FaTools
} from 'react-icons/fa';
import BackendService from '../../services/BackendService';
import './Documentation.css';

// Video Embed Component
const VideoEmbed = ({ video }) => {
  const videoId = video.link.split('v=')[1] || video.link.split('/').pop();
  
  return (
    <div className="documentation-card video-embed">
      <div className="video-container">
        <iframe
          width="100%"
          height="100%"
          src={`https://www.youtube.com/embed/${videoId}`}
          title={video.title}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      </div>
      <div className="video-info">
        <h3>{video.title}</h3>
        <p>{video.subtitle}</p>
      </div>
    </div>
  );
};

const Documentation = () => {
  const [activeMode, setActiveMode] = useState('all');
  const [backendDocumentation, setBackendDocumentation] = useState(null);
  const [backendConnected, setBackendConnected] = useState(false);

  // Fetch documentation from backend
  useEffect(() => {
    const fetchBackendDocumentation = async () => {
      try {
        const response = await BackendService.getDocumentation();

        if (response.type === 'documentation_data') {
          setBackendDocumentation(response.data);
          setBackendConnected(true);
          console.log('🔗 Documentation links:', response.data.length || 0, 'links');
        }
      } catch (err) {
        console.error('Failed to fetch documentation from backend:', err);
        setBackendConnected(false);
      }
    };

    fetchBackendDocumentation();
  }, []);

  // Listen for documentation refresh events from TopBar
  useEffect(() => {
    const handleDocumentationRefresh = (event) => {
      console.log('📚 Documentation: Received refresh event with data:', event.detail);
      setBackendDocumentation(event.detail);
      setBackendConnected(true);
    };

    // Add event listener for documentation refresh
    window.addEventListener('documentationRefreshed', handleDocumentationRefresh);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener('documentationRefreshed', handleDocumentationRefresh);
    };
  }, []);

  // Fallback documentation links - used when backend is unavailable
  const fallbackDocsLinks = [
    {
      title: "Getting Started with Kasper",
      subtitle: "Learn the basics of using Kasper",
      type: "guide",
      link: "https://docs.kasper.ai/getting-started",
      icon: "FaRocket",
      category: "Guides"
    },
    {
      title: "Advanced Features",
      subtitle: "Explore Kasper's powerful capabilities",
      type: "guide",
      link: "https://docs.kasper.ai/advanced-features",
      icon: "FaLaptopCode",
      category: "Guides"
    },
    {
      title: "API Documentation",
      subtitle: "Integrate Kasper into your workflow",
      type: "guide",
      link: "https://docs.kasper.ai/api",
      icon: "FaCode",
      category: "Guides"
    },
    {
      title: "Kasper Core",
      subtitle: "The main Kasper repository",
      type: "github",
      link: "https://github.com/kasper-ai/core",
      icon: "FaCodeBranch",
      category: "Open Source"
    },
    {
      title: "Kasper UI",
      subtitle: "The Kasper user interface",
      type: "github",
      link: "https://github.com/kasper-ai/ui",
      icon: "FaTools",
      category: "Open Source"
    },
    {
      title: "Kasper Tutorial",
      subtitle: "There never was an Final Solution Order",
      type: "video",
      link: "https://youtu.be/0bnd6OIUy4A?si=l7cvHW5AlobOzrK4",
      icon: "FaPlayCircle",
      category: "Videos"
    }
  ];

  // Icon mapping for backend string names to React components
  const iconMap = {
    'FaRocket': FaRocket,
    'FaLaptopCode': FaLaptopCode,
    'FaCode': FaCode,
    'FaCodeBranch': FaCodeBranch,
    'FaTools': FaTools,
    'FaPlayCircle': FaPlayCircle,
    'FaBook': FaBook,
    'FaBookOpen': FaBookOpen,
    'FaGraduationCap': FaGraduationCap,
    'FaGithub': FaGithub,
    'FaYoutube': FaYoutube,
    'FaExternalLinkAlt': FaExternalLinkAlt
  };

  // Dynamic documentation links - uses backend data if available, otherwise fallback
  const backendDocsLinks = backendDocumentation?.links || backendDocumentation?.docsLinks;

  // Convert backend icon strings to React components if needed
  const docsLinks = backendDocsLinks
    ? backendDocsLinks.map(link => ({
        ...link,
        icon: typeof link.icon === 'string'
          ? iconMap[link.icon] || FaBook // Map string to component, fallback to FaBook
          : link.icon // Already a component
      }))
    : fallbackDocsLinks.map(link => ({
        ...link,
        icon: iconMap[link.icon] || FaBook // Convert fallback string icons to components
      }));

  const handleOpenLink = (doc) => {
    if (doc.type !== 'video') {
      window.open(doc.link, '_blank');
    }
  };

  const handleMouseMove = useCallback((e) => {
    const card = e.currentTarget;
    const rect = card.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;
    card.style.setProperty('--mouse-x', `${x}%`);
    card.style.setProperty('--mouse-y', `${y}%`);
  }, []);

  // Group links by category
  const groupedLinks = docsLinks.reduce((acc, link) => {
    if (!acc[link.category]) {
      acc[link.category] = [];
    }
    acc[link.category].push(link);
    return acc;
  }, {});

  // Category icons mapping
  const categoryIcons = {
    "Guides": FaBookReader,
    "Open Source": FaCodeBranch,
    "Videos": FaGraduationCap
  };

  const handleModeChange = (mode, e) => {
    e.preventDefault();
    setActiveMode(mode);
    // Remove focus from the button after click
    if (e.target) {
      e.target.blur();
    }
  };

  return (
    <div className="documentation-container">
      <div className="mode-switcher" role="tablist">
        <button
          role="tab"
          aria-selected={activeMode === 'all'}
          className={`mode-button ${activeMode === 'all' ? 'active' : ''}`}
          onClick={(e) => handleModeChange('all', e)}
          onMouseDown={(e) => e.preventDefault()}
        >
          <FaBook /> All
        </button>
        <button
          role="tab"
          aria-selected={activeMode === 'guides'}
          className={`mode-button ${activeMode === 'guides' ? 'active' : ''}`}
          onClick={(e) => handleModeChange('guides', e)}
          onMouseDown={(e) => e.preventDefault()}
        >
          <FaBookReader /> Guides
        </button>
        <button
          role="tab"
          aria-selected={activeMode === 'videos'}
          className={`mode-button ${activeMode === 'videos' ? 'active' : ''}`}
          onClick={(e) => handleModeChange('videos', e)}
          onMouseDown={(e) => e.preventDefault()}
        >
          <FaPlayCircle /> Videos
        </button>
      </div>

      {Object.entries(groupedLinks)
        .filter(([category]) => {
          if (activeMode === 'all') return true;
          if (activeMode === 'guides') return category === 'Guides';
          if (activeMode === 'videos') return category === 'Videos';
          return true;
        })
        .map(([category, links]) => {
          const CategoryIcon = categoryIcons[category];
          return (
            <div key={category} className="documentation-section">
              <h2 className="section-title">
                <CategoryIcon className="section-icon" />
                {category}
              </h2>
              <div className="documentation-grid">
                {links.map((doc, index) => (
                  doc.type === 'video' ? (
                    <VideoEmbed key={index} video={doc} />
                  ) : (
                    <div 
                      key={index} 
                      className="documentation-card"
                      onClick={() => handleOpenLink(doc)}
                      onMouseMove={handleMouseMove}
                    >
                      <div className="card-icon">
                        <doc.icon />
                      </div>
                      <div className="card-content">
                        <h3>{doc.title}</h3>
                        <p>{doc.subtitle}</p>
                      </div>
                      <div className="card-action">
                        <FaExternalLinkAlt />
                      </div>
                    </div>
                  )
                ))}
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default Documentation; 