#!/usr/bin/env python3
"""
Test script to verify the new 2-second ping/pong functionality and refresh features.
This script simulates the frontend behavior and tests the enhanced health monitoring.
"""

import asyncio
import json
import websockets
import time

async def test_ping_pong_functionality():
    """Test the new 2-second ping/pong intervals and refresh commands."""
    
    uri = "ws://localhost:8765"
    
    try:
        print("🔗 Connecting to WebSocket server...")
        async with websockets.connect(uri) as websocket:
            print("✅ Connected successfully!")
            
            # Test 1: Get initial documentation
            print("\n📚 Testing initial documentation fetch...")
            get_docs_message = {
                "command": "get_documentation",
                "params": {},
                "id": str(int(time.time() * 1000))
            }
            
            await websocket.send(json.dumps(get_docs_message))
            response = await websocket.recv()
            docs_response = json.loads(response)
            print(f"📚 Initial documentation response: {docs_response.get('type', 'unknown')}")
            
            # Test 2: Refresh documentation (simulating menu click)
            print("\n🔄 Testing documentation refresh...")
            refresh_docs_message = {
                "command": "refresh_documentation", 
                "params": {},
                "id": str(int(time.time() * 1000))
            }
            
            await websocket.send(json.dumps(refresh_docs_message))
            response = await websocket.recv()
            refresh_docs_response = json.loads(response)
            print(f"🔄 Documentation refresh response: {refresh_docs_response.get('type', 'unknown')}")
            if 'timestamp' in refresh_docs_response:
                print(f"🕒 Refresh timestamp: {refresh_docs_response['timestamp']}")
            
            # Test 3: Get initial settings
            print("\n⚙️ Testing initial settings fetch...")
            get_settings_message = {
                "command": "get_settings",
                "params": {},
                "id": str(int(time.time() * 1000))
            }
            
            await websocket.send(json.dumps(get_settings_message))
            response = await websocket.recv()
            settings_response = json.loads(response)
            print(f"⚙️ Initial settings response: {settings_response.get('type', 'unknown')}")
            
            # Test 4: Refresh settings (simulating menu click)
            print("\n🔄 Testing settings refresh...")
            refresh_settings_message = {
                "command": "refresh_settings",
                "params": {},
                "id": str(int(time.time() * 1000))
            }
            
            await websocket.send(json.dumps(refresh_settings_message))
            response = await websocket.recv()
            refresh_settings_response = json.loads(response)
            print(f"🔄 Settings refresh response: {refresh_settings_response.get('type', 'unknown')}")
            if 'timestamp' in refresh_settings_response:
                print(f"🕒 Refresh timestamp: {refresh_settings_response['timestamp']}")
            
            print("\n✅ All tests completed successfully!")
            
    except websockets.exceptions.ConnectionRefused:
        print("❌ Failed to connect to WebSocket server. Make sure the backend is running on localhost:8765")
    except ArithmeticError as e:
        print(f"❌ Error during testing: {e}")

if __name__ == "__main__":
    print("🧪 Testing Kasper-Q 2-Second Ping/Pong and Menu Refresh Functionality")
    print("=" * 60)
    asyncio.run(test_ping_pong_functionality())
