/* Basic Mode Styles */
.basic-mode-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  height: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.queue-entry-card {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.queue-entry-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-color), var(--purple-secondary));
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.input-group {
  margin-bottom: var(--spacing-lg);
}

.input-group label {
  display: block;
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.neo-input {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--tertiary-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1rem;
  font-family: var(--font-mono);
  transition: all 0.3s ease;
  outline: none;
}

.neo-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.1);
  background: var(--secondary-bg);
}

.neo-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  background: var(--tertiary-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
}

.quantity-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: var(--accent-color);
  color: var(--primary-bg);
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: scale(1.1);
}

.quantity-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.5;
}

.quantity-display {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
  min-width: 40px;
  text-align: center;
  font-family: var(--font-mono);
}

.start-button {
  width: 100%;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--accent-color), var(--purple-primary));
  border: none;
  border-radius: var(--radius-md);
  color: white;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-purple);
}

.start-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.start-button:hover:not(:disabled)::before {
  left: 100%;
}

.start-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.start-button:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.start-icon {
  font-size: 1.2rem;
}

.button-text {
  font-weight: 700;
}

.status-panel {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.status-header h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.status-indicator {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.idle {
  background: rgba(176, 176, 176, 0.2);
  color: var(--text-secondary);
}

.status-indicator.waiting {
  background: rgba(255, 170, 0, 0.2);
  color: var(--warning-color);
  animation: pulse 1.5s infinite;
}

.status-indicator.running {
  background: rgba(168, 85, 247, 0.2);
  color: var(--accent-color);
  animation: pulse 1s infinite;
}

.status-indicator.success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
}

.status-indicator.error {
  background: rgba(255, 68, 68, 0.2);
  color: var(--error-color);
}

.status-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  text-align: center;
}

.ghost-loader {
  width: 50px;
  height: 50px;
  border: 3px solid transparent;
  border-radius: 50%;
  position: relative;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.ghost-loader.animated {
  opacity: 1;
  border-top: 3px solid var(--accent-color);
  animation: ghostSpin 1.5s linear infinite;
}

.ghost-loader.animated::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  right: 3px;
  bottom: 3px;
  border: 2px solid transparent;
  border-top: 2px solid var(--purple-secondary);
  border-radius: 50%;
  animation: ghostSpin 1s linear infinite reverse;
}

.status-message {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .basic-mode-container {
    padding: var(--spacing-md);
    gap: var(--spacing-md);
  }

  .queue-entry-card,
  .status-panel {
    padding: var(--spacing-lg);
  }

  .card-title {
    font-size: 1.3rem;
  }

  .start-button {
    padding: var(--spacing-md);
    font-size: 1rem;
  }

  .quantity-control {
    gap: var(--spacing-sm);
  }

  .quantity-btn {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .quantity-display {
    font-size: 1.3rem;
    min-width: 35px;
  }
}
