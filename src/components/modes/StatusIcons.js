import React from 'react';

export const ClockIcon = () => (
  <svg className="base-status-icon" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm.5-9H7v4l3.5 2.1.5-.8-3-1.8V5z"/>
  </svg>
);

export const SpinnerIcon = () => (
  <svg className="base-status-icon animate-spin" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8h-2c0 3.31-2.69 6-6 6s-6-2.69-6-6 2.69-6 6-6V0z"/>
  </svg>
);

export const NumberIcon = () => (
  <svg className="base-status-icon" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm-1-9H7v2h2V5zm0 3H7v2h2V8zm0 3H7v2h2v-2z"/>
  </svg>
);

export const LinkIcon = () => (
  <svg className="base-status-icon" viewBox="0 0 16 16" fill="currentColor">
    <path d="M8 0C3.58 0 0 3.58 0 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm-1-9H7v2h2V5zm0 3H7v2h2V8zm0 3H7v2h2v-2z"/>
  </svg>
);

export const getIconByType = (type) => {
  switch (type) {
    case 'clock':
      return <ClockIcon />;
    case 'spinner':
      return <SpinnerIcon />;
    case 'number':
      return <NumberIcon />;
    case 'link':
      return <LinkIcon />;
    default:
      return null;
  }
}; 