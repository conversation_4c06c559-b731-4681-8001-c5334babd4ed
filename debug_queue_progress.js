// Debug script for queue progress updates - paste this into browser console
console.log('🔄 Queue Progress Debug Tool');
console.log('='.repeat(50));

// Check if BackendService is available
if (window.BackendService) {
  console.log('✅ BackendService found');
  console.log('Connection State:', window.BackendService.getConnectionState());
  
  // Check current message handlers
  if (window.BackendService.messageHandlers) {
    console.log('\n📋 Current Message Handlers:');
    for (const [type, handlers] of window.BackendService.messageHandlers.entries()) {
      if (Array.isArray(handlers)) {
        console.log(`  ${type}: ${handlers.length} handlers`);
      } else {
        console.log(`  ${type}: 1 handler (legacy)`);
      }
    }
    
    // Check specifically for queue_progress_update handlers
    const queueHandlers = window.BackendService.messageHandlers.get('queue_progress_update');
    if (queueHandlers) {
      console.log('\n🔄 Queue Progress Handlers:', queueHandlers.length || 1);
    } else {
      console.log('\n❌ No queue progress handlers registered');
    }
  }
} else {
  console.log('❌ BackendService not found');
}

// Set up a test handler to monitor queue progress messages
let testHandler = null;
let messageCount = 0;

function setupTestHandler() {
  if (window.BackendService && !testHandler) {
    testHandler = (message) => {
      messageCount++;
      console.log(`🔄 TEST HANDLER ${messageCount}: Queue progress received:`, {
        type: message.type,
        task_id: message.task_id,
        overall_status: message.overall_status,
        pass_links_count: message.pass_links_count,
        ticket_count: message.ticket_count,
        timestamp: new Date(message.timestamp * 1000).toLocaleTimeString()
      });
      
      // Log full message for debugging
      console.log('🔄 Full message:', message);
    };
    
    window.BackendService.registerHandler('queue_progress_update', testHandler);
    console.log('✅ Test handler registered for queue_progress_update');
  }
}

function removeTestHandler() {
  if (window.BackendService && testHandler) {
    window.BackendService.unregisterHandler('queue_progress_update', testHandler);
    testHandler = null;
    console.log('🗑️ Test handler removed');
  }
}

// Check for React components and their state
function checkReactState() {
  console.log('\n⚛️ Checking React Component State:');
  
  // Look for MonitoringView component
  const monitoringElements = document.querySelectorAll('[class*="monitoring"], [class*="progress"]');
  console.log(`Found ${monitoringElements.length} potential monitoring elements`);
  
  // Check for progress indicators
  const progressElements = document.querySelectorAll('.progress-stage, .current-stage, [class*="stage"]');
  console.log(`Found ${progressElements.length} progress stage elements`);
  
  progressElements.forEach((el, i) => {
    console.log(`  Progress Element ${i + 1}:`, el.textContent?.trim() || el.className);
  });
}

// Monitor WebSocket messages
function monitorWebSocketMessages() {
  console.log('\n🔗 WebSocket Message Monitor:');
  
  // Override WebSocket send/receive for debugging
  if (window.BackendService && window.BackendService.ws) {
    const originalOnMessage = window.BackendService.ws.onmessage;
    
    window.BackendService.ws.onmessage = function(event) {
      try {
        const message = JSON.parse(event.data);
        if (message.type === 'queue_progress_update') {
          console.log('🔄 RAW WebSocket queue progress message:', message);
        }
      } catch (e) {
        // Ignore parsing errors
      }
      
      // Call original handler
      if (originalOnMessage) {
        originalOnMessage.call(this, event);
      }
    };
    
    console.log('✅ WebSocket message monitor installed');
  } else {
    console.log('❌ WebSocket not available for monitoring');
  }
}

// Main diagnostic function
function runDiagnostic() {
  console.log('\n🔍 Running Queue Progress Diagnostic...');
  
  setupTestHandler();
  checkReactState();
  monitorWebSocketMessages();
  
  console.log('\n💡 Diagnostic complete. To test:');
  console.log('1. Start a queue entry from the UI');
  console.log('2. Watch console for queue progress messages');
  console.log('3. Check if UI updates match received messages');
  console.log('4. Run removeTestHandler() when done testing');
}

// Expose functions globally for easy access
window.queueDebug = {
  setupTestHandler,
  removeTestHandler,
  checkReactState,
  monitorWebSocketMessages,
  runDiagnostic,
  getMessageCount: () => messageCount
};

console.log('\n🛠️ Debug tools available:');
console.log('- queueDebug.runDiagnostic() - Run full diagnostic');
console.log('- queueDebug.setupTestHandler() - Add test message handler');
console.log('- queueDebug.removeTestHandler() - Remove test handler');
console.log('- queueDebug.checkReactState() - Check React component state');
console.log('- queueDebug.getMessageCount() - Get received message count');

// Auto-run diagnostic
runDiagnostic();
