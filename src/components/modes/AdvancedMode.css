/* Advanced Mode Styles */
.advanced-mode {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--spacing-lg);
  gap: var(--spacing-lg);
}

.mode-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.mode-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: 1.8rem;
  color: var(--accent-color);
  margin-bottom: var(--spacing-sm);
}

.mode-header p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.advanced-content {
  display: flex;
  gap: var(--spacing-lg);
  flex: 1;
  min-height: 0;
}

.left-panel {
  flex: 1;
  min-width: 400px;
}

.right-panel {
  flex: 1;
  min-width: 400px;
}

.config-section {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.config-section h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.input-group label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

.scan-types {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
}

.scan-type-card {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  cursor: pointer;
  transition: all 0.2s ease;
}

.scan-type-card:hover {
  border-color: var(--accent-color);
  background: rgba(0, 255, 136, 0.05);
}

.scan-type-card.selected {
  border-color: var(--accent-color);
  background: rgba(0, 255, 136, 0.1);
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.2);
}

.scan-type-card h4 {
  color: var(--text-primary);
  font-size: 0.95rem;
  margin-bottom: var(--spacing-xs);
}

.scan-type-card p {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: auto;
}

.action-buttons .btn {
  flex: 1;
}

.results-section {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.results-section h3 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.scanning-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: var(--spacing-lg);
}

.scan-progress {
  text-align: center;
  width: 100%;
  max-width: 300px;
}

.scan-progress .progress-bar {
  width: 100%;
  height: 6px;
  background: var(--tertiary-bg);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: var(--spacing-md);
}

.scan-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--info-color));
  border-radius: 3px;
  animation: scanProgress 2s ease-in-out infinite;
}

@keyframes scanProgress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

.scan-progress p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.result-item {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--tertiary-bg);
  transition: all 0.2s ease;
}

.result-item:hover {
  background: rgba(255, 255, 255, 0.02);
}

.result-item.high {
  border-left: 4px solid var(--error-color);
}

.result-item.medium {
  border-left: 4px solid var(--warning-color);
}

.result-item.low {
  border-left: 4px solid var(--info-color);
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.severity {
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.severity.high {
  background: rgba(255, 68, 68, 0.2);
  color: var(--error-color);
}

.severity.medium {
  background: rgba(255, 170, 0, 0.2);
  color: var(--warning-color);
}

.severity.low {
  background: rgba(0, 136, 255, 0.2);
  color: var(--info-color);
}

.result-item h4 {
  color: var(--text-primary);
  font-size: 0.95rem;
  margin: 0;
}

.result-item p {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin: 0;
  font-family: var(--font-mono);
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: var(--spacing-md);
  color: var(--text-muted);
}

.no-results i {
  font-size: 3rem;
  opacity: 0.3;
}

.no-results p {
  text-align: center;
  max-width: 250px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .advanced-content {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    min-width: auto;
  }
  
  .scan-types {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .advanced-mode {
    padding: var(--spacing-md);
  }
  
  .scan-types {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
