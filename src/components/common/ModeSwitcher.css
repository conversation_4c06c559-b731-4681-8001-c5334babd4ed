.kasper-mode-switcher {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
  position: relative;
  gap: 4px;
  min-width: 160px;
  height: 32px;
}

.mode-switcher__option {
  flex: 1;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mode-switcher__option:hover {
  color: rgba(255, 255, 255, 0.9);
}

.mode-switcher__option.active {
  color: #fff;
}

.mode-switcher__highlight {
  position: absolute;
  top: 4px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 0;
}

/* Focus styles */
.mode-switcher__option:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Active state styles */
.mode-switcher__option.active {
  font-weight: 600;
}

/* Hover effect for active button */
.mode-switcher__option.active:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Disabled state */
.mode-switcher__option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mode-switcher__underline {
  display: none !important;
} 