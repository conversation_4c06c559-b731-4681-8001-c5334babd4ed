import React, { useEffect, useRef } from 'react';
import { useAppContext } from '../../context/AppContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaBook, FaCog } from 'react-icons/fa';
import './TitleBar.css';

const MODES = [
  { id: 'basic', label: 'Base', path: '/' },
  { id: 'advanced', label: 'Pro', path: '/advanced' },
  { id: 'developer', label: 'Dev', path: '/developer' }
];

const MacMinimizeIcon = ({ size = 12 }) => (
  <svg width={size} height={size} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="6" cy="6" r="6" fill="#FFD700" stroke="#FFD700" strokeWidth="0.5" />
  </svg>
);

const MacCloseIcon = ({ size = 12 }) => (
  <svg width={size} height={size} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="6" cy="6" r="6" fill="#FF5F57" stroke="#FF5F57" strokeWidth="0.5" />
    <path d="M4.2 4.2L7.8 7.8M7.8 4.2L4.2 7.8" stroke="#fff" strokeWidth="1" strokeLinecap="round" opacity="0" />
  </svg>
);

const TitleBar = () => {
  const { appMode, setAppMode } = useAppContext();
  const navigate = useNavigate();
  const location = useLocation();
  const switcherRef = useRef(null);
  const isMonitoring = location.pathname.includes('/monitoring');

  // Handle window control buttons
  const handleMinimize = () => {
    if (window.electron) {
      window.electron.minimize();
    }
  };

  const handleClose = () => {
    if (window.electron) {
      window.electron.close();
    }
  };

  // Handle mode switch
  const handleModeSwitch = (mode, path) => {
    if (isMonitoring) {
      // If in monitoring mode, only allow navigation back to the base path
      if (mode === 'basic' && path === '/') {
        setAppMode(mode);
        navigate(path);
      }
      return;
    }
    
    setAppMode(mode);
    if (location.pathname !== path) {
      navigate(path);
    }
  };

  const handleDocumentation = () => {
    // Open documentation in a new window or navigate to docs page
    window.open('https://docs.kasper.com', '_blank');
  };

  const handleSettings = () => {
    // Navigate to settings page or open settings modal
    navigate('/settings');
  };

  // Sliding indicator logic
  useEffect(() => {
    const idx = MODES.findIndex(m => m.id === appMode);
    if (switcherRef.current) {
      switcherRef.current.style.setProperty('--mode-indicator-x', `${idx * 100}%`);
    }
  }, [appMode]);

  return (
    <div className="title-bar macos-luxury">
      <div className="window-controls macos">
        <button 
          className="window-control mac-close"
          onClick={handleClose}
          title="Close"
        >
          <MacCloseIcon size={12} />
        </button>
        <button 
          className="window-control mac-minimize"
          onClick={handleMinimize}
          title="Minimize"
        >
          <MacMinimizeIcon size={12} />
        </button>
      </div>
      <div className="mode-switcher center" ref={switcherRef}>
        {MODES.map((mode) => (
          <button
            key={mode.id}
            className={`mode-pill${appMode === mode.id ? ' active' : ''}${isMonitoring && mode.id !== 'basic' ? ' disabled' : ''}`}
            onClick={() => handleModeSwitch(mode.id, mode.path)}
            tabIndex={0}
            disabled={isMonitoring && mode.id !== 'basic'}
          >
            {mode.label}
          </button>
        ))}
      </div>
      <div className="title-bar-actions">
        <button
          className="action-button"
          onClick={handleDocumentation}
          title="Documentation"
        >
          <FaBook />
        </button>
        <button
          className="action-button"
          onClick={handleSettings}
          title="Settings"
        >
          <FaCog />
        </button>
      </div>
    </div>
  );
};

export default TitleBar;