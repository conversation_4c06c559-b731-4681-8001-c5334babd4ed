const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
console.log('🔥 Preload script loaded');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld(
  'electron', {
    // Open external URLs in default browser
    openExternal: (url) => {
      console.log('Preload: invoking open-external-url with', url)
      return ipcRenderer.invoke('open-external-url', url)
    },

    // Send messages to main process
    send: (channel, data) => {
      const validChannels = ['window-minimize', 'window-maximize', 'window-close'];
      if (validChannels.includes(channel)) {
        ipcRenderer.send(channel, data);
      }
    },

    // Window control functions (legacy support)
    windowControls: {
      minimize: () => ipcRenderer.send('window-minimize'),
      maximize: () => ipcRenderer.send('window-maximize'),
      close: () => ipcRenderer.send('window-close')
    },

    // System information
    system: {
      platform: process.platform
    }
  }
);