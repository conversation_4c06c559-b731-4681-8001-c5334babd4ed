#!/usr/bin/env node

/**
 * Test script for WebSocket retry system
 * This script simulates various connection scenarios to test the retry logic
 */

const WebSocket = require('ws');

class RetrySystemTester {
  constructor() {
    this.testResults = [];
    this.serverUrl = 'ws://localhost:8765';
  }

  log(message) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async testServerNotRunning() {
    this.log('🧪 TEST 1: Connecting when server is not running');
    
    try {
      const ws = new WebSocket(this.serverUrl);
      
      const result = await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({ success: false, reason: 'Connection timeout' });
        }, 5000);

        ws.on('open', () => {
          clearTimeout(timeout);
          ws.close();
          resolve({ success: true, reason: 'Unexpected connection success' });
        });

        ws.on('error', (error) => {
          clearTimeout(timeout);
          resolve({ success: true, reason: `Expected connection failure: ${error.code}` });
        });
      });

      this.testResults.push({
        test: 'Server Not Running',
        passed: !result.success || result.reason.includes('Expected'),
        details: result.reason
      });

      this.log(`✅ TEST 1 Result: ${result.reason}`);
      
    } catch (error) {
      this.testResults.push({
        test: 'Server Not Running',
        passed: true,
        details: `Expected error: ${error.message}`
      });
      this.log(`✅ TEST 1 Result: Expected error - ${error.message}`);
    }
  }

  async testRapidReconnection() {
    this.log('🧪 TEST 2: Rapid connection attempts');
    
    const attempts = 5;
    const results = [];
    
    for (let i = 0; i < attempts; i++) {
      try {
        const ws = new WebSocket(this.serverUrl);
        
        const result = await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            resolve({ success: false, attempt: i + 1 });
          }, 2000);

          ws.on('open', () => {
            clearTimeout(timeout);
            ws.close();
            resolve({ success: true, attempt: i + 1 });
          });

          ws.on('error', () => {
            clearTimeout(timeout);
            resolve({ success: false, attempt: i + 1 });
          });
        });

        results.push(result);
        await this.delay(100); // Small delay between attempts
        
      } catch (error) {
        results.push({ success: false, attempt: i + 1, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    this.testResults.push({
      test: 'Rapid Reconnection',
      passed: true, // This test is about behavior, not success/failure
      details: `${attempts} attempts: ${successCount} succeeded, ${failureCount} failed`
    });

    this.log(`✅ TEST 2 Result: ${successCount}/${attempts} connections succeeded`);
  }

  async testConnectionWithServer() {
    this.log('🧪 TEST 3: Testing with server running (if available)');
    
    try {
      const ws = new WebSocket(this.serverUrl);
      
      const result = await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({ success: false, reason: 'Connection timeout' });
        }, 5000);

        ws.on('open', () => {
          clearTimeout(timeout);
          
          // Send a test message
          ws.send(JSON.stringify({
            command: 'ping',
            params: {},
            id: 'test-ping'
          }));
          
          // Wait for response
          const responseTimeout = setTimeout(() => {
            ws.close();
            resolve({ success: true, reason: 'Connected but no response' });
          }, 3000);

          ws.on('message', (data) => {
            clearTimeout(responseTimeout);
            try {
              const message = JSON.parse(data);
              ws.close();
              resolve({ 
                success: true, 
                reason: `Connected and received: ${message.type || 'unknown message'}` 
              });
            } catch (error) {
              ws.close();
              resolve({ success: true, reason: 'Connected but invalid response' });
            }
          });
        });

        ws.on('error', (error) => {
          clearTimeout(timeout);
          resolve({ success: false, reason: `Connection failed: ${error.code}` });
        });
      });

      this.testResults.push({
        test: 'Connection With Server',
        passed: result.success,
        details: result.reason
      });

      this.log(`${result.success ? '✅' : '❌'} TEST 3 Result: ${result.reason}`);
      
    } catch (error) {
      this.testResults.push({
        test: 'Connection With Server',
        passed: false,
        details: `Error: ${error.message}`
      });
      this.log(`❌ TEST 3 Result: Error - ${error.message}`);
    }
  }

  async testExponentialBackoff() {
    this.log('🧪 TEST 4: Simulating exponential backoff timing');
    
    const delays = [1000, 2000, 4000, 8000, 16000, 30000]; // Expected delays
    const tolerance = 200; // 200ms tolerance
    
    let testPassed = true;
    const results = [];
    
    for (let i = 0; i < 3; i++) { // Test first 3 delays
      const expectedDelay = delays[i];
      const startTime = Date.now();
      
      // Simulate the delay calculation
      const baseDelay = 1000;
      const backoffFactor = 2;
      const jitterFactor = 0.1;
      
      let calculatedDelay = Math.min(baseDelay * Math.pow(backoffFactor, i), 30000);
      const jitter = calculatedDelay * jitterFactor * (Math.random() - 0.5);
      calculatedDelay = Math.max(calculatedDelay + jitter, baseDelay);
      
      const isWithinTolerance = Math.abs(calculatedDelay - expectedDelay) <= (expectedDelay * jitterFactor + tolerance);
      
      results.push({
        attempt: i + 1,
        expected: expectedDelay,
        calculated: Math.floor(calculatedDelay),
        withinTolerance: isWithinTolerance
      });
      
      if (!isWithinTolerance && Math.abs(calculatedDelay - expectedDelay) > expectedDelay * 0.2) {
        testPassed = false;
      }
    }

    this.testResults.push({
      test: 'Exponential Backoff',
      passed: testPassed,
      details: `Delay calculations: ${results.map(r => `${r.attempt}:${r.calculated}ms`).join(', ')}`
    });

    this.log(`${testPassed ? '✅' : '❌'} TEST 4 Result: Backoff timing ${testPassed ? 'correct' : 'incorrect'}`);
  }

  printResults() {
    this.log('\n📊 TEST RESULTS SUMMARY');
    this.log('=' * 50);
    
    let passed = 0;
    let total = this.testResults.length;
    
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      this.log(`${status} ${result.test}: ${result.details}`);
      if (result.passed) passed++;
    });
    
    this.log('=' * 50);
    this.log(`📈 SUMMARY: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      this.log('🎉 All tests passed! Retry system is working correctly.');
    } else {
      this.log(`⚠️ ${total - passed} tests failed. Review the implementation.`);
    }
  }

  async runAllTests() {
    this.log('🚀 Starting WebSocket retry system tests...\n');
    
    await this.testServerNotRunning();
    await this.delay(1000);
    
    await this.testRapidReconnection();
    await this.delay(1000);
    
    await this.testConnectionWithServer();
    await this.delay(1000);
    
    await this.testExponentialBackoff();
    
    this.printResults();
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new RetrySystemTester();
  tester.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = RetrySystemTester;
