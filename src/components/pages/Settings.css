.settings-container {
  padding: 12px;
  height: 600px;
  background: #0D0D0D;
  margin-top: 48px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

/* Data source indicator */
.data-source-indicator {
  position: absolute;
  top: 60px;
  right: 24px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 12px;
  font-size: 11px;
  color: #00ff88;
  z-index: 10;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  background: #00ff88;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.indicator-text {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

.settings-container::-webkit-scrollbar {
  width: 4px;
}

.settings-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.settings-container::-webkit-scrollbar-thumb {
  background: rgba(124, 77, 255, 0.3);
  border-radius: 2px;
}

.settings-grid {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
  gap: 12px;
  width: 100%;
  max-width: 976px;
  margin: 0 auto;
  height: auto;
}

/* First Column */
.license-card {
  grid-column: 1;
  grid-row: 1;
  height: fit-content;
}

.webhook-card {
  grid-column: 1;
  grid-row: 2;
}

/* Second Column */
.balance-card {
  grid-column: 2;
  grid-row: 1 / span 2;
  display: flex;
  flex-direction: column;
}

.settings-card {
  background: rgba(124, 77, 255, 0.03);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  border: 1px solid rgba(124, 77, 255, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.settings-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(124, 77, 255, 0.08);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.settings-card:hover::before {
  opacity: 1;
}

.settings-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
}

.settings-icon {
  font-size: 1.1rem;
  color: #7C4DFF;
  background: rgba(124, 77, 255, 0.1);
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 0 20px rgba(124, 77, 255, 0.1);
}

.settings-card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #EAEAEA;
  letter-spacing: 0.02em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.settings-card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  z-index: 2;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* License Card Styles */
.license-card {
  display: flex;
  flex-direction: column;
}

.license-status {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
  letter-spacing: 0.02em;
}

.status-badge.active {
  background: rgba(52, 199, 89, 0.1);
  color: #34C759;
  box-shadow: 0 0 20px rgba(52, 199, 89, 0.1);
}

.license-type {
  font-size: 0.9rem;
  color: #EAEAEA;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  letter-spacing: 0.02em;
}

.license-key {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.license-renewal {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.renewal-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #EAEAEA;
  font-size: 0.9rem;
  letter-spacing: 0.02em;
}

.renewal-info code {
  font-family: var(--font-mono);
  color: #7C4DFF;
  background: rgba(124, 77, 255, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.85rem;
}

.license-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
  flex-shrink: 0;
  width: 100%;
}

.license-actions .settings-button {
  flex: 1;
  justify-content: center;
  padding: 10px 16px;
  font-size: 0.9rem;
}

.license-actions .settings-button.primary {
  background: linear-gradient(135deg, #7C4DFF 0%, #7C4DFF 100%);
  box-shadow: 0 0 20px rgba(124, 77, 255, 0.2);
}

.license-actions .settings-button.primary:hover {
  background: linear-gradient(135deg, #7C4DFF 0%, #7C4DFF 100%);
  box-shadow: 0 0 25px rgba(124, 77, 255, 0.3);
  transform: translateY(-1px);
}

.license-actions .settings-button.danger {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
  border: 1px solid rgba(255, 59, 48, 0.2);
  box-shadow: 0 0 20px rgba(255, 59, 48, 0.1);
}

.license-actions .settings-button.danger:hover {
  background: rgba(255, 59, 48, 0.15);
  border-color: rgba(255, 59, 48, 0.3);
  box-shadow: 0 0 25px rgba(255, 59, 48, 0.15);
  transform: translateY(-1px);
}

/* Balance Card Styles */
.balance-card {
  display: flex;
  flex-direction: column;
}

.balance-amount {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: rgba(124, 77, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(124, 77, 255, 0.1);
}

.credit-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.credit-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.credit-progress-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.credit-progress-label .icon {
  color: #7C4DFF;
  font-size: 1rem;
}

.credit-progress-value {
  font-weight: 500;
  color: #fff;
}

.credit-progress-bar-container {
  height: 8px;
  background: rgba(124, 77, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.credit-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #7C4DFF, #6B3FE7);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.credit-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

.credit-progress-bar.monthly,
.credit-progress-bar.purchased {
  background: linear-gradient(90deg, #7C4DFF, #6B3FE7);
}

.credit-progress-bar.purchased::after {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.15),
    transparent
  );
}


@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Remove old balance styles */
.balance-value,
.balance-label {
  display: none;
}

.balance-pending {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding: 6px 10px;
  background: rgba(255, 214, 10, 0.1);
  border-radius: 8px;
  flex-shrink: 0;
  box-shadow: 0 0 20px rgba(255, 214, 10, 0.1);
}

.pending-label {
  font-size: 0.85rem;
  color: #FFD60A;
  letter-spacing: 0.02em;
}

.pending-value {
  font-size: 0.85rem;
  color: #FFD60A;
  font-weight: 500;
  letter-spacing: 0.02em;
}

.balance-history {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.balance-history h4 {
  margin: 0;
  font-size: 14px;
  color: #7C4DFF;
  font-weight: 600;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 8px;
  padding-right: 8px;
}

.history-list::-webkit-scrollbar {
  width: 4px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb {
  background: rgba(124, 77, 255, 0.3);
  border-radius: 2px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(124, 77, 255, 0.1);
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.history-amount {
  font-size: 12px;
  font-weight: 600;
}

.history-amount.purchase {
  color: #4CAF50;
}

.history-amount.usage {
  color: #FF5252;
}

/* Webhook Card Styles */
.webhook-card {
  display: flex;
  flex-direction: column;
}

.webhook-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1;
  min-height: 0;
}

.webhook-label {
  font-size: 0.9rem;
  color: #888;
  font-weight: 500;
  flex-shrink: 0;
  letter-spacing: 0.02em;
}

.webhook-value {
  font-size: 0.9rem;
  color: #EAEAEA;
  font-family: var(--font-mono);
  background: rgba(0, 0, 0, 0.2);
  padding: 10px 12px;
  border-radius: 8px;
  word-break: break-all;
  border: 1px solid rgba(255, 255, 255, 0.05);
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  letter-spacing: 0.02em;
}

.webhook-value::-webkit-scrollbar {
  width: 4px;
}

.webhook-value::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.webhook-value::-webkit-scrollbar-thumb {
  background: rgba(124, 77, 255, 0.3);
  border-radius: 2px;
}

.webhook-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
  flex-shrink: 0;
}

/* Button Styles */
.settings-button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

/* Card Option Style */
.card-option {
  background: rgba(124, 77, 255, 0.05);
  border: 1px solid rgba(124, 77, 255, 0.3);
  color: #7C4DFF;
}

.card-option:hover {
  background: rgba(124, 77, 255, 0.1);
  border-color: rgba(124, 77, 255, 0.5);
}

/* Crypto Option Style */
.crypto-button {
  position: relative;
  background: rgba(124, 77, 255, 0.1);
  border: 1px solid rgba(124, 77, 255, 0.2);
  color: #7C4DFF;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.crypto-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shine 3s infinite;
}

.crypto-button:hover {
  background: rgba(124, 77, 255, 0.15);
  border-color: rgba(124, 77, 255, 0.3);
  transform: translateY(-2px);
}

@keyframes shine {
  0% {
    left: -100%;
  }
  20% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

.settings-button svg {
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 968px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-container {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .settings-container {
    padding: 16px;
  }
  
  .settings-card {
    padding: 16px;
  }
  
  .webhook-actions {
    flex-direction: column;
  }
}

.balance-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.balance-actions h4 {
  margin: 0;
  font-size: 14px;
  color: #7C4DFF;
  font-weight: 600;
  letter-spacing: 0.02em;
}

.topup-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.card-option,
.crypto-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s ease;
  background: rgba(124, 77, 255, 0.1);
  border: 1px solid rgba(124, 77, 255, 0.2);
  color: #fff;
}

.card-option:hover,
.crypto-button:hover {
  background: rgba(124, 77, 255, 0.2);
  border-color: rgba(124, 77, 255, 0.4);
  transform: translateY(-1px);
}

.card-option:disabled,
.crypto-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.card-option .spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Update toast styles globally */
.Toaster {
  z-index: 9999;
}

.Toast {
  background: #1a1a1a !important;
  color: #fff !important;
  border: 1px solid rgba(124, 77, 255, 0.2) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  animation: slideUp 0.3s ease !important;
}

.Toast--error {
  border-color: rgba(255, 82, 82, 0.2) !important;
}

.Toast__icon {
  color: #7C4DFF !important;
}

.Toast--error .Toast__icon {
  color: #FF5252 !important;
}

.crypto-option {
  position: relative;
}

.promotion-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
  color: white;
  padding: 1px 6px;
  border-radius: 0 8px 0 8px;
  font-size: 9px;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(156, 39, 176, 0.3),
              inset 0 1px 1px rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(156, 39, 176, 0.5);
  backdrop-filter: blur(4px);
  letter-spacing: 0.02em;
  text-transform: uppercase;
  z-index: 1;
}

.promotion-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  border-radius: inherit;
  animation: shine 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3),
                inset 0 1px 1px rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: scale(1.03);
    box-shadow: 0 3px 8px rgba(255, 215, 0, 0.4),
                inset 0 1px 1px rgba(255, 255, 255, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 6px rgba(255, 215, 0, 0.3),
                inset 0 1px 1px rgba(255, 255, 255, 0.3);
  }
}

@keyframes shine {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.webhook-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.webhook-input {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: rgba(26, 26, 26, 0.5);
  color: #fff;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.webhook-input:focus {
  outline: none;
  background: rgba(26, 26, 26, 0.7);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.05);
}

.webhook-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.webhook-actions {
  display: flex;
  gap: 8px;
}

.test-button, .copy-button {
  background: rgba(26, 26, 26, 0.5);
  border: none;
  color: #fff;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.test-button:hover, .copy-button:hover {
  background: rgba(26, 26, 26, 0.7);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-button:active, .copy-button:active {
  transform: translateY(0);
  box-shadow: none;
}

.recent-transactions {
  margin-top: 20px;
}

.recent-transactions h4 {
  margin-bottom: 12px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.transactions-list {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 8px;
}

.transactions-list::-webkit-scrollbar {
  width: 4px;
}

.transactions-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.transactions-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.transaction-item {
  display: grid;
  grid-template-columns: 80px 1fr 100px;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(124, 77, 255, 0.1);
  gap: 12px;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-amount {
  font-weight: 500;
  font-size: 12px;
  text-align: left;
  opacity: 0.9;
}

.transaction-amount.credit {
  color: rgba(76, 175, 80, 0.9);
}

.transaction-amount.debit {
  color: rgba(244, 67, 54, 0.9);
}

.transaction-description {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.transaction-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  text-align: right;
}

/* Webhook Synchronization Styles */
.webhook-status {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;
}

.status-indicator.saving {
  background: rgba(255, 193, 7, 0.1);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.2);
}

.status-indicator.saved {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.status-indicator.error {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.webhook-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.webhook-input.error {
  border: 1px solid rgba(244, 67, 54, 0.5);
  background: rgba(244, 67, 54, 0.05);
}

.webhook-input.error:focus {
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.input-status-indicator {
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  pointer-events: none;
}

.status-icon {
  font-size: 14px;
  transition: all 0.2s ease;
}

.status-icon.saving {
  color: #FFC107;
}

.status-icon.saved {
  color: #4CAF50;
}

.status-icon.error {
  color: #F44336;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.webhook-error-message {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
  color: #F44336;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.webhook-help {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.4;
  margin-top: 4px;
}

.webhook-help code {
  background: rgba(124, 77, 255, 0.1);
  color: #7C4DFF;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: var(--font-mono);
  font-size: 11px;
}

.webhook-actions .settings-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.webhook-actions .settings-button:disabled:hover {
  background: rgba(26, 26, 26, 0.5);
  transform: none;
  box-shadow: none;
}

/* Enhanced header layout for webhook status */
.settings-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  justify-content: space-between;
}

.settings-card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #EAEAEA;
  letter-spacing: 0.02em;
  flex: 1;
}

.credit-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
}

.credit-popup {
  background: #1a1a1a;
  border-radius: 16px;
  padding: 32px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(124, 77, 255, 0.2);
  animation: slideUp 0.3s ease;
}

.credit-popup-header {
  text-align: center;
  margin-bottom: 32px;
}

.credit-popup-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: #fff;
  font-weight: 500;
  margin-bottom: 8px;
}

.credit-popup-header p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.95rem;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.credit-popup-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.credit-amount-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.credit-amount-button {
  background: rgba(124, 77, 255, 0.1);
  border: 1px solid rgba(124, 77, 255, 0.2);
  border-radius: 12px;
  padding: 24px;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.credit-amount-button .amount {
  font-size: 1.75rem;
  font-weight: 600;
  color: #7C4DFF;
}

.credit-amount-button:hover {
  background: rgba(124, 77, 255, 0.15);
  border-color: rgba(124, 77, 255, 0.3);
  transform: translateY(-1px);
}

.credit-amount-button.selected {
  background: rgba(124, 77, 255, 0.2);
  border-color: rgba(124, 77, 255, 0.4);
}

.credit-amount-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Recommended option styling */
.credit-amount-button.recommended {
  background: linear-gradient(135deg, rgba(124, 77, 255, 0.2), rgba(124, 77, 255, 0.3));
  border: 1px solid rgba(124, 77, 255, 0.4);
  backdrop-filter: blur(8px);
  box-shadow: 
    0 0 20px rgba(124, 77, 255, 0.2),
    inset 0 0 20px rgba(124, 77, 255, 0.1);
  animation: glowPulse 2s infinite;
}

.credit-amount-button.recommended .amount {
  color: rgba(124, 77, 255, 0.9);
  text-shadow: 0 0 10px rgba(124, 77, 255, 0.3);
  font-weight: 600;
}

.credit-amount-button.recommended:hover {
  background: linear-gradient(135deg, rgba(124, 77, 255, 0.25), rgba(124, 77, 255, 0.35));
  border-color: rgba(124, 77, 255, 0.5);
  box-shadow: 
    0 0 30px rgba(124, 77, 255, 0.3),
    inset 0 0 30px rgba(124, 77, 255, 0.15);
}

.credit-amount-button.recommended:hover .amount {
  color: rgba(124, 77, 255, 1);
  text-shadow: 0 0 15px rgba(124, 77, 255, 0.4);
}

.credit-amount-button.recommended.selected {
  background: linear-gradient(135deg, rgba(124, 77, 255, 0.3), rgba(124, 77, 255, 0.4));
  border-color: rgba(124, 77, 255, 0.6);
  box-shadow: 
    0 0 40px rgba(124, 77, 255, 0.4),
    inset 0 0 40px rgba(124, 77, 255, 0.2);
}

.credit-amount-button.recommended.selected .amount {
  color: rgba(124, 77, 255, 1);
  text-shadow: 0 0 20px rgba(124, 77, 255, 0.5);
}

@keyframes glowPulse {
  0% {
    box-shadow: 
      0 0 20px rgba(124, 77, 255, 0.2),
      inset 0 0 20px rgba(124, 77, 255, 0.1);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(124, 77, 255, 0.3),
      inset 0 0 30px rgba(124, 77, 255, 0.15);
  }
  100% {
    box-shadow: 
      0 0 20px rgba(124, 77, 255, 0.2),
      inset 0 0 20px rgba(124, 77, 255, 0.1);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.primary-button {
  background: linear-gradient(135deg, #7C4DFF, #6B3FE7);
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 20px rgba(124, 77, 255, 0.3),
    0 0 0 1px rgba(124, 77, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
}

.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 6px 25px rgba(124, 77, 255, 0.4),
    0 0 0 1px rgba(124, 77, 255, 0.3);
  background: linear-gradient(135deg, #8B5DFF, #7C4DFF);
}

.primary-button:active {
  transform: translateY(1px);
  box-shadow: 
    0 2px 15px rgba(124, 77, 255, 0.3),
    0 0 0 1px rgba(124, 77, 255, 0.2);
}

.primary-button:disabled {
  background: linear-gradient(135deg, rgba(124, 77, 255, 0.3), rgba(107, 63, 231, 0.3));
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: 0.5s;
}

.primary-button:hover::before {
  left: 100%;
}

.primary-button .button-content {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.primary-button .button-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.primary-button:hover .button-icon {
  transform: translateX(2px);
}

.primary-button .button-text {
  font-weight: 600;
  letter-spacing: 0.3px;
}

/* Loading state */
.primary-button.loading {
  cursor: wait;
  opacity: 0.8;
}

.primary-button.loading .button-content {
  opacity: 0.7;
}

.primary-button.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: #fff;
  border-radius: 50%;
  animation: buttonSpin 0.8s linear infinite;
}

@keyframes buttonSpin {
  to {
    transform: rotate(360deg);
  }
}