# Kasper-Q Style Guide

## 🎨 Design System Overview

Our application follows a modern, cyberpunk-inspired design system with a focus on glassmorphism, neon accents, and smooth animations. The design language emphasizes depth, interactivity, and a futuristic aesthetic while maintaining usability and accessibility.

## 🎯 Core Design Principles

1. **Glassmorphism**
   - Semi-transparent backgrounds
   - Subtle blur effects
   - Light borders for depth
   - Layered elements with proper z-indexing

2. **Neon Accents**
   - Primary color: `#7C4DFF` (Deep Purple)
   - Secondary color: `#6A00CC` (Darker Purple)
   - Accent color: `#ff4444` (Error Red)
   - Text shadows for emphasis
   - Glowing effects on hover

3. **Animation & Motion**
   - Smooth transitions (0.3s ease)
   - Floating animations for background elements
   - Staggered entrance animations
   - Interactive hover states
   - Subtle pulse effects

## 🎨 Color Palette

```css
/* Primary Colors */
--primary: #7C4DFF;      /* Deep Purple */
--primary-dark: #6A00CC; /* Darker Purple */
--error: #ff4444;        /* Error Red */

/* Background Colors */
--bg-dark: #0B0A12;      /* Main Background */
--bg-light: rgba(255, 255, 255, 0.05); /* Glass Background */

/* Text Colors */
--text-primary: #FFFFFF;
--text-secondary: rgba(255, 255, 255, 0.8);
--text-muted: rgba(255, 255, 255, 0.6);
```

## 🎭 Component Styles

### 1. Container Elements
```css
.container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
```

### 2. Form Elements
```css
.input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.input:focus {
  border-color: rgba(138, 0, 238, 0.5);
  box-shadow: 0 0 0 2px rgba(138, 0, 238, 0.2);
}
```

### 3. Buttons
```css
.button {
  background: linear-gradient(45deg, var(--primary), var(--primary-dark));
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 0, 238, 0.3);
}
```

## 🎬 Animation System

### 1. Entrance Animations
```css
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
```

### 2. Background Animations
```css
@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    filter: blur(0px);
  }
  25% {
    transform: translate(100px, 50px) rotate(90deg) scale(1.2);
    filter: blur(2px);
  }
  50% {
    transform: translate(50px, 100px) rotate(180deg) scale(0.8);
    filter: blur(0px);
  }
  75% {
    transform: translate(-50px, 50px) rotate(270deg) scale(1.1);
    filter: blur(1px);
  }
  100% {
    transform: translate(0, 0) rotate(360deg) scale(1);
    filter: blur(0px);
  }
}
```

### 3. Interactive Animations
```css
@keyframes pulse {
  0% { box-shadow: 0 0 20px rgba(138, 0, 238, 0.2); }
  50% { box-shadow: 0 0 40px rgba(138, 0, 238, 0.4); }
  100% { box-shadow: 0 0 20px rgba(138, 0, 238, 0.2); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
```

## 📱 Responsive Design

Our design system is built with responsiveness in mind:

1. **Fluid Typography**
   - Base font size: 16px
   - Responsive scaling using rem units
   - Minimum text size: 14px

2. **Flexible Layouts**
   - Max-width containers
   - Percentage-based widths
   - Flexible padding and margins

3. **Mobile Considerations**
   - Touch-friendly target sizes (min 44px)
   - Simplified animations on mobile
   - Adjusted spacing for smaller screens

## 🎯 Accessibility Guidelines

1. **Color Contrast**
   - Minimum contrast ratio: 4.5:1
   - Text on glass backgrounds: 7:1
   - Error states clearly visible

2. **Focus States**
   - Visible focus indicators
   - Keyboard navigation support
   - Clear interactive states

3. **Motion**
   - Respects reduced-motion preferences
   - No essential information in animations
   - Smooth transitions for all users

## 🛠️ Implementation Notes

1. **CSS Organization**
   - Component-based structure
   - BEM naming convention
   - Modular animations
   - Reusable utility classes

2. **Performance**
   - Hardware-accelerated animations
   - Efficient backdrop-filter usage
   - Optimized transitions
   - Lazy-loaded animations

3. **Browser Support**
   - Modern browser features
   - Fallbacks for older browsers
   - Progressive enhancement
   - Cross-browser testing

## 🎨 Design Tokens

```css
/* Spacing */
--spacing-xs: 0.25rem;
--spacing-sm: 0.5rem;
--spacing-md: 1rem;
--spacing-lg: 1.5rem;
--spacing-xl: 2rem;

/* Border Radius */
--radius-sm: 4px;
--radius-md: 8px;
--radius-lg: 15px;
--radius-full: 50%;

/* Shadows */
--shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
--shadow-md: 0 8px 32px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 16px 48px rgba(0, 0, 0, 0.2);

/* Transitions */
--transition-fast: 0.2s ease;
--transition-normal: 0.3s ease;
--transition-slow: 0.5s ease;
```

## 📚 Usage Examples

### 1. Card Component
```css
.card {
  background: var(--bg-light);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  animation: fadeIn var(--transition-normal);
}
```

### 2. Button Component
```css
.button {
  background: linear-gradient(45deg, var(--primary), var(--primary-dark));
  color: var(--text-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  transition: transform var(--transition-normal);
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
```

### 3. Input Component
```css
.input {
  background: var(--bg-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  color: var(--text-primary);
  transition: all var(--transition-normal);
}

.input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(138, 0, 238, 0.2);
}
```

## 🔄 Version Control

This style guide is a living document and will be updated as our design system evolves. All changes should be documented and versioned appropriately.

## 📝 Contributing

When contributing to the design system:

1. Follow the established patterns
2. Document new components
3. Update this guide
4. Test across browsers
5. Consider accessibility
6. Optimize performance

---

*Last updated: [Current Date]* 